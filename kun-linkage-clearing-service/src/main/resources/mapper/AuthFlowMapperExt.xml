<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.clearing.ext.mapper.AuthFlowMapperExt">
    <update id="updateClearingData">
        update kl_auth_flow_${partition}
        set
            <choose>
                <when test="reversalFlag">
                    clear_amount = clear_amount - #{clearingTransAmount},
                    clear_bill_amount = clear_bill_amount - #{clearingCardholderBillingAmount},
                    clear_bill_amount_with_markup = clear_bill_amount_with_markup - #{clearingCardholderMarkupBillingAmount},
                </when>
                <otherwise>
                    remaining_trans_amount          = (case
                                                        when remaining_trans_amount - #{clearingTransAmount} >= 0
                                                        then remaining_trans_amount - #{clearingTransAmount}
                                                        else 0 end),
                    remaining_billing_amount        = (case when (remaining_billing_amount - #{clearingCardholderBillingAmount}) >= 0
                                                        then (remaining_billing_amount - #{clearingCardholderBillingAmount})
                                                        else 0 end),
                    remaining_markup_billing_amount = (case when (remaining_markup_billing_amount - #{clearingCardholderMarkupBillingAmount}) >= 0
                                                        then (remaining_markup_billing_amount - #{clearingCardholderMarkupBillingAmount})
                                                        else 0 end),
                    release_flag                    = (case
                                                        when release_flag = #{lockedFlag} and (remaining_trans_amount &lt;= 0)
                                                        then #{releaseFlag}
                                                        when release_flag = #{releaseFlag} and (remaining_trans_amount > 0)
                                                        then #{lockedFlag}
                                                        else release_flag end),
                    clear_amount = clear_amount + #{clearingTransAmount},
                    clear_bill_amount = clear_bill_amount + #{clearingCardholderBillingAmount},
                    clear_bill_amount_with_markup = clear_bill_amount_with_markup + #{clearingCardholderMarkupBillingAmount},
                </otherwise>
            </choose>
            <if test="clearFlag != null and clearFlag != ''">
                clear_flag = (case when clear_flag = #{clearFlag} then clear_flag else #{clearFlag} end),
            </if>
            <if test="clearAccountingDate != null and clearAccountingDate != ''">
                clear_accounting_date = #{clearAccountingDate},
            </if>
            update_time            = #{updateTime}
        where id = #{id}
    </update>
</mapper>