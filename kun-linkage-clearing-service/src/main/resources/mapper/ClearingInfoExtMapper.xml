<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kun.linkage.clearing.ext.mapper.ClearingInfoExtMapper">

    <select id="selectVccCallbackInfoByTransId" resultType="com.kun.linkage.clearing.vo.visa.AuthFlowVo">
        select  * from ${authTableName}
          where trans_id = #{transId} and status = 'SUCC' limit 1
    </select>

    <select id="selectCardInfoByCardNo" resultType="com.kun.linkage.clearing.vo.visa.CardInfoVo">
        SELECT ci.*, aa.account_no AS merchantNo
        FROM kc_card_info ci
        INNER JOIN kc_account_association_info aa
        ON ci.member_id = aa.member_id AND account_type = 'COIN_ACCOUNT'
        WHERE card_no = #{cardNo} limit 1

    </select>
    <select id="selectVccCallbackInfoByAuthFlowId" resultType="com.kun.linkage.clearing.vo.visa.AuthFlowVo">
        select  * from ${tableName}
        where id = #{authFlowId} and status = 'SUCC' limit 1
    </select>

    <select id="selectVisa05DataFileList" resultType="com.kun.linkage.common.db.entity.VisaBase05Data">
        select  * from kc_visa_base_05_data where id > #{maxId} and file_name = #{fileName}
        <if test="visa05FileIds != null and visa05FileIds.size > 0">
                  and id IN
            <foreach collection="visa05FileIds" item="fileId" open="(" separator="," close=")">
                #{fileId}
            </foreach>
        </if>
        order by id asc limit #{batchSelectTotal}
    </select>

<!--    and card_acceptor_identification_code = #{cardAcceptorId}-->
    <select id="selectVccCallbackInfoByParam" resultType="com.kun.linkage.clearing.vo.visa.AuthFlowVo">
        select  * from ${authTableName}
        where  status = 'SUCC'
            and card_id = #{processorCardId}
            and approve_code = #{authCode}
            and acquiring_institution_identification_code = #{acquiringId}
            and date_time_local_transaction between concat(#{startTransactionDate}, '000000') and concat(#{endTransactionDate}, '235959')
            <if test="transactionCode != null and transactionCode !=''">
                <choose>
                    <when test="transactionCode == '05'">
                        and auth_type in('010000','010001','010002','010005')
                    </when>
                    <when test="transactionCode == '06'">
                        and auth_type in('010004','010006')
                    </when>
                    <when test="transactionCode == '07'">
                        and auth_type in('010003')
                    </when>
                </choose>
            </if>
            order by create_time asc
            limit 1
    </select>

    <select id="selectVisa33DataFileList" resultType="com.kun.linkage.common.db.entity.VisaBase33Data">
        select  * from kc_visa_base_33_data where id > #{maxId} and file_name = #{fileName}
        order by id asc limit #{batchSelectTotal}
    </select>
    <select id="selectKcAccountAssociationName"
            resultType="com.kun.linkage.clearing.vo.KcAccountAssociationVo">
        SELECT account_no,account_name
        FROM kc_account_association_info
        WHERE account_type = 'COIN_ACCOUNT'
    </select>

    <update id="updateAuthFlow">
        update ${tableName}
        set remain_frozen_amt = #{updateAuthFlowVo.remainFrozenAmt},
            remain_auth_amt = #{updateAuthFlowVo.remainAuthAmt},
            remain_bill_amt = #{updateAuthFlowVo.remainBillAmt},
            remain_bill_amt_with_markup = #{updateAuthFlowVo.remainBillAmtWithMarkup},
            clear_status =#{updateAuthFlowVo.clearStatus},
            update_time = #{updateAuthFlowVo.updateTime}
        where id = #{updateAuthFlowVo.id}
    </update>

</mapper>
