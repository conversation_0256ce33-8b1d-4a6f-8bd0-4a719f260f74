package com.kun.linkage.clearing.enums;

import lombok.Getter;

/**
 * title: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 * @since 2025/5/15 18:54
 */
@Getter
public enum BpcTransTypeEnum {
    //1 授权；2授权撤销；3退款；4退款撤销；6授权查询；8 VISADirect; 30 冲正交易 99其他交易
    CONSUME(1, "授权"),
    CONSUME_REVERSAL(2, "授权撤销"),
    REFUND(3, "退款"),
    REFUND_REVERSAL(4, "退款撤销"),
    AUTH_QUERY(6, "授权查询"),
    VISADIRECT(8, "VISADirect"),
    REVERSAL(30, "冲正交易"),
    OTHER(99, "其他交易"),
    ;
    private final Integer code;
    private final String message;

    BpcTransTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
