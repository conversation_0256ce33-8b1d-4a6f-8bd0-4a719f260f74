package com.kun.linkage.clearing.enums;

public enum CalculationEnum {

    ADDITION("ADD", "+"),
    SUBTRACTION("SUB", "-"),
    MULTIPLICATION("MUL", "*"),
    DIVISION("DIV", "/");

    private final String operator;
    private final String description;

    // 构造方法
    CalculationEnum(String operator, String description) {
        this.operator = operator;
        this.description = description;
    }

    // 获取操作符
    public String getOperator() {
        return operator;
    }

    // 获取描述
    public String getDescription() {
        return description;
    }

    // 根据操作符获取枚举项
    public static CalculationEnum fromOperator(String operator) {
        for (CalculationEnum calculation : CalculationEnum.values()) {
            if (calculation.operator.equals(operator)) {
                return calculation;
            }
        }
        return null;
    }

}
