package com.kun.linkage.clearing.service.boss.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kun.linkage.clearing.constant.ExportFileConstant;
import com.kun.linkage.clearing.facade.vo.boss.ExportFileRecordPageQueryVO;
import com.kun.linkage.clearing.facade.vo.boss.ExportFileRecordVO;
import com.kun.linkage.clearing.service.boss.IExportFileRecordService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KCExportFileRecord;
import com.kun.linkage.common.db.mapper.ExportFileRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * <p>
 * 导出文件记录服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
public class ExportFileRecordServiceImpl extends ServiceImpl<ExportFileRecordMapper, KCExportFileRecord> implements IExportFileRecordService {

    @Override
    public KCExportFileRecord createFileRecord(String fileName, String fileType) {
        KCExportFileRecord record = new KCExportFileRecord();
        record.setFileName(fileName);
        record.setFileType(fileType);
        record.setFileStatus(ExportFileConstant.FileStatus.PROCESSING);
        record.setCreatedTime(LocalDateTime.now());
        record.setUpdatedTime(LocalDateTime.now());

        this.save(record);
        return record;
    }

    @Override
    public void updateFileRecordSuccess(String fileRecordId, String s3Url, Long fileSize) {
        KCExportFileRecord record = new KCExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setFileStatus(ExportFileConstant.FileStatus.SUCCESS);
        record.setS3Url(s3Url);
        record.setFileSize(fileSize);
        record.setUpdatedTime(LocalDateTime.now());

        this.updateById(record);
    }

    @Override
    public void updateFileRecordFailed(String fileRecordId, String errorMessage) {
        KCExportFileRecord record = new KCExportFileRecord();
        record.setFileRecordId(fileRecordId);
        record.setFileStatus(ExportFileConstant.FileStatus.FAILED);
        record.setErrorMessage(errorMessage);
        record.setUpdatedTime(LocalDateTime.now());

        this.updateById(record);
    }

    @Override
    public Result<PageResult<ExportFileRecordVO>> pageList(ExportFileRecordPageQueryVO pageQueryVO) {
        LambdaQueryWrapper<KCExportFileRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (pageQueryVO.getExportStartDate() != null) {
            queryWrapper.ge(KCExportFileRecord::getCreatedTime, pageQueryVO.getExportStartDate().atStartOfDay());
        }
        if (pageQueryVO.getExportEndDate() != null) {
            queryWrapper.le(KCExportFileRecord::getCreatedTime, pageQueryVO.getExportEndDate().atTime(23, 59, 59));
        }
        if (StringUtils.isNotBlank(pageQueryVO.getFileType())) {
            queryWrapper.eq(KCExportFileRecord::getFileType, pageQueryVO.getFileType());
        }
        if (StringUtils.isNotBlank(pageQueryVO.getFileStatus())) {
            queryWrapper.eq(KCExportFileRecord::getFileStatus, pageQueryVO.getFileStatus());
        }

        queryWrapper.orderByDesc(KCExportFileRecord::getCreatedTime);

        PageResult<ExportFileRecordVO> page = PageHelperUtil.getPage(pageQueryVO, () ->
                this.list(queryWrapper).stream().map(this::convertToVO).collect(Collectors.toList()));
        return Result.success(page);
    }

    @Override
    public Result<String> getDownloadUrl(String fileRecordId) {
        KCExportFileRecord record = this.getById(fileRecordId);
        if (record == null) {
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }

        if (!ExportFileConstant.FileStatus.SUCCESS.equals(record.getFileStatus())) {
            return Result.fail("文件未生成成功，无法下载");
        }

        if (StringUtils.isBlank(record.getS3Url())) {
            return Result.fail("文件下载地址不存在");
        }

        return Result.success(record.getS3Url());
    }

    /**
     * 转换为VO
     */
    private ExportFileRecordVO convertToVO(KCExportFileRecord record) {
        ExportFileRecordVO vo = new ExportFileRecordVO();
        BeanUtils.copyProperties(record, vo);
        return vo;
    }
}
