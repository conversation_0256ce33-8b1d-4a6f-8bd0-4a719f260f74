package com.kun.linkage.clearing.service.kunlinkage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.clearing.facade.vo.boss.ClearingInquiryDetailVO;
import com.kun.linkage.clearing.facade.vo.boss.ClearingInquiryPageVO;
import com.kun.linkage.clearing.facade.vo.boss.ClearingInquiryRequestVO;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLClearingTrans;
import com.kun.linkage.common.db.mapper.KLClearingTransMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KLClearingTransService {

    private static final Logger log = LoggerFactory.getLogger(KLClearingTransService.class);

    @Resource
    private KLClearingTransMapper clearingTransMapper;

    public PageResult<ClearingInquiryPageVO> pageList(ClearingInquiryRequestVO requestVO) {
        LambdaQueryWrapper<KLClearingTrans> wrapper = Wrappers.lambdaQuery();
        wrapper.ge(KLClearingTrans::getCreateTime, DateTimeUtils.truncateToSecond(DateUtils.parseDate(requestVO.getClearingDateFrom().trim() + " 00:00:00", DateUtils.DATETIME_PATTERN)));
        wrapper.le(KLClearingTrans::getCreateTime, DateTimeUtils.truncateToSecond(DateUtils.parseDate(requestVO.getClearingDateUntil().trim() + " 23:59:59", DateUtils.DATETIME_PATTERN)));
        if (requestVO.getClearingId() != null) {
            wrapper.eq(KLClearingTrans::getClearingId, requestVO.getClearingId());
        }
        if (requestVO.getOriginalClearingId() != null) {
            wrapper.eq(KLClearingTrans::getOriginalClearingId, requestVO.getOriginalClearingId());
        }
        if (requestVO.getProcessor() != null) {
            wrapper.eq(KLClearingTrans::getProcessor, requestVO.getProcessor());
        }
        if (requestVO.getAuthorizationDateFrom() != null) {
            wrapper.ge(KLClearingTrans::getTransTime, DateTimeUtils.truncateToSecond(DateUtils.parseDate(requestVO.getAuthorizationDateFrom().trim()+" 00:00:00", DateUtils.DATETIME_PATTERN)));
        }
        if (requestVO.getAuthorizationDateUntil() != null) {
            wrapper.le(KLClearingTrans::getTransTime, DateTimeUtils.truncateToSecond(DateUtils.parseDate(requestVO.getAuthorizationDateUntil()+" 23:59:59", DateUtils.DATETIME_PATTERN)));
        }
        if (requestVO.getMerchantNo() != null) {
            wrapper.eq(KLClearingTrans::getMerchantNo, requestVO.getMerchantNo());
        }
        if (requestVO.getTransType() != null) {
            wrapper.eq(KLClearingTrans::getTransType, requestVO.getTransType());
        }
        if (requestVO.getAcquireReferenceNo() != null) {
            wrapper.eq(KLClearingTrans::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (requestVO.getArn() != null) {
            wrapper.eq(KLClearingTrans::getArn, requestVO.getArn());
        }
        if (requestVO.getGatewayCardId() != null) {
            wrapper.eq(KLClearingTrans::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (requestVO.getApproveCode() != null) {
            wrapper.eq(KLClearingTrans::getApproveCode, requestVO.getApproveCode());
        }
        if (StringUtils.isNotBlank(requestVO.getClearingStatus())) {
            wrapper.eq(KLClearingTrans::getStatus, requestVO.getClearingStatus());
        }
        if (StringUtils.isNotBlank(requestVO.getCardNoSuffix())) {
            wrapper.eq(KLClearingTrans::getMaskedCardNo, requestVO.getCardNoSuffix());
        }
        if (StringUtils.isNotBlank(requestVO.getClearingType())) {
            wrapper.eq(KLClearingTrans::getClearingType, requestVO.getClearingType());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayRequestId())) {
            wrapper.eq(KLClearingTrans::getGatewayRequestId, requestVO.getGatewayRequestId());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayClearingId())) {
            wrapper.eq(KLClearingTrans::getGatewayClearingId, requestVO.getGatewayClearingId());
        }
        if (StringUtils.isNotBlank(requestVO.getOriginalGatewayClearingId())) {
            wrapper.eq(KLClearingTrans::getOriginalGatewayClearingId, requestVO.getOriginalGatewayClearingId());
        }
        wrapper.orderByDesc(KLClearingTrans::getCreateTime);
        PageResult<KLClearingTrans> pageResult =
                PageHelperUtil.getPage(requestVO, () -> clearingTransMapper.selectList(wrapper));
        PageResult<ClearingInquiryPageVO> result = convertToPageResult(pageResult);
        return result;
    }

    private PageResult<ClearingInquiryPageVO> convertToPageResult(PageResult<KLClearingTrans> pageResult) {
        List<ClearingInquiryPageVO> list = pageResult.getData().stream().map(clearingTrans -> {
            ClearingInquiryPageVO vo = new ClearingInquiryPageVO();
            vo.setClearingId(clearingTrans.getClearingId());
            vo.setProcessor(clearingTrans.getProcessor());
            vo.setClearingDate(DateUtils.formatDate(clearingTrans.getCreateTime(), DateUtils.DAY_PATTERN));
            vo.setAuthorizationTime(clearingTrans.getTransTime() == null ? null : DateUtils.formatDate(clearingTrans.getTransTime(), DateUtils.DATETIME_PATTERN));
            vo.setMerchantNo(clearingTrans.getMerchantNo());
            vo.setMerchantName(clearingTrans.getMerchantName());
            vo.setGatewayCardId(clearingTrans.getGatewayCardId());
            vo.setTransType(clearingTrans.getTransType());
            vo.setTransCurrency(clearingTrans.getTransCurrency());
            vo.setTransCurrencyExponent(clearingTrans.getTransCurrencyExponent());
            vo.setTransAmount(clearingTrans.getTransAmount());
            vo.setCardholderCurrency(clearingTrans.getCardholderBillingCurrency());
            vo.setCardholderCurrencyExponent(clearingTrans.getCardholderCurrencyExponent());
            vo.setCardholderAmount(clearingTrans.getCardholderBillingAmount());
            vo.setCardholderMarkupAmount(clearingTrans.getCardholderMarkupBillingAmount());
            vo.setClearingStatus(clearingTrans.getStatus());
            vo.setAcquireReferenceNo(clearingTrans.getAcquireReferenceNo());
            vo.setApproveCode(clearingTrans.getApproveCode());
            vo.setArn(clearingTrans.getArn());
            vo.setAuthTransId(clearingTrans.getAuthFlowId());
            vo.setCardNoSuffix(clearingTrans.getMaskedCardNo());
            vo.setClearingType(clearingTrans.getClearingType());
            vo.setGatewayRequestId(clearingTrans.getGatewayRequestId());
            vo.setGatewayClearingId(clearingTrans.getGatewayClearingId());
            vo.setOriginalGatewayClearingId(clearingTrans.getOriginalGatewayClearingId());
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<ClearingInquiryPageVO>(list, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotal(),
                pageResult.getExtraInfo()
        );
    }

    public ClearingInquiryDetailVO detail(String clearingId, String clearingDate) {
        LambdaQueryWrapper<KLClearingTrans> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(KLClearingTrans::getClearingId, clearingId);
        wrapper.ge(KLClearingTrans::getCreateTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(clearingDate.trim() + " 00:00:00",
                        DateUtils.DATETIME_PATTERN)));
        wrapper.le(KLClearingTrans::getCreateTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(clearingDate.trim() + " 23:59:59",
                        DateUtils.DATETIME_PATTERN)));

        KLClearingTrans clearingTrans = clearingTransMapper.selectOne(wrapper);
        if (clearingTrans != null) {
            ClearingInquiryDetailVO detail = new ClearingInquiryDetailVO();
            detail.setClearingId(clearingTrans.getClearingId());
            detail.setProcessor(clearingTrans.getProcessor());
            detail.setClearingDate(DateUtils.formatDate(clearingTrans.getCreateTime(), DateUtils.DAY_PATTERN));
            detail.setAuthorizationTime(DateUtils.formatDate(clearingTrans.getTransTime(), DateUtils.DATETIME_PATTERN));
            detail.setMerchantNo(clearingTrans.getMerchantNo());
            detail.setMerchantName(clearingTrans.getMerchantName());
            detail.setGatewayCardId(clearingTrans.getGatewayCardId());
            detail.setTransType(clearingTrans.getTransType());
            detail.setTransCurrency(clearingTrans.getTransCurrency());
            detail.setTransCurrencyExponent(clearingTrans.getTransCurrencyExponent());
            detail.setTransAmount(clearingTrans.getTransAmount());
            detail.setCardholderCurrency(clearingTrans.getCardholderBillingCurrency());
            detail.setCardholderCurrencyExponent(clearingTrans.getCardholderCurrencyExponent());
            detail.setCardholderAmount(clearingTrans.getCardholderBillingAmount());
            detail.setCardholderMarkupAmount(clearingTrans.getCardholderMarkupBillingAmount());
            detail.setClearingStatus(clearingTrans.getStatus());
            detail.setAcquireReferenceNo(clearingTrans.getAcquireReferenceNo());
            detail.setApproveCode(clearingTrans.getApproveCode());
            detail.setArn(clearingTrans.getArn());
            detail.setAuthTransId(clearingTrans.getAuthFlowId());
            detail.setCustomerId(clearingTrans.getCustomerId());
            detail.setCardProductCode(clearingTrans.getCardProductCode());
            detail.setAcquirerTid(clearingTrans.getCardAcceptorTid());
            detail.setMarkupAmount(clearingTrans.getMarkupAmount());
            detail.setMarkupRate(clearingTrans.getMarkupRate());
            detail.setConversionRate(clearingTrans.getConversionRateCardholderBilling());
            detail.setAcquirerMerchantNo(clearingTrans.getCardAcceptorId());
            detail.setAcquirerMerchantName(clearingTrans.getCardAcceptorName());
            detail.setClearingType(clearingTrans.getClearingType());
            detail.setGatewayRequestId(clearingTrans.getGatewayRequestId());
            detail.setGatewayClearingId(clearingTrans.getGatewayClearingId());
            detail.setOriginalGatewayClearingId(clearingTrans.getOriginalGatewayClearingId());
            return  detail;
        }
        return null;
    }
}
