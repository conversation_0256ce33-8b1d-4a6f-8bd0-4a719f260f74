package com.kun.linkage.clearing.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.clearing.facade.vo.boss.ExportFileRecordPageQueryVO;
import com.kun.linkage.clearing.facade.vo.boss.ExportFileRecordVO;
import com.kun.linkage.clearing.service.boss.IExportFileRecordService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "导出文件记录管理", description = "导出文件记录管理")
@RestController
@RequestMapping("/boss/exportFileRecord")
public class ExportFileRecordController extends BaseVccBossController {

    @Autowired
    private IExportFileRecordService exportFileRecordService;

    /**
     * 分页查询导出文件记录
     *
     * @param pageQueryVO
     * @return
     */
    @Operation(description = "分页查询导出文件记录", summary = "分页查询导出文件记录")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-export-file-record"})
    @PostMapping("/pageList")
    public Result<PageResult<ExportFileRecordVO>> pageList(@RequestBody ExportFileRecordPageQueryVO pageQueryVO) {
        return exportFileRecordService.pageList(pageQueryVO);
    }

    /**
     * 获取文件下载URL
     * @param fileRecordId
     * @return
     */
    @Operation(description = "获取文件下载URL", summary = "获取文件下载URL")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-export-file-record"})
    @GetMapping("/downloadUrl")
    public Result<String> getDownloadUrl(@RequestParam(name = "fileRecordId") String fileRecordId) {
        return exportFileRecordService.getDownloadUrl(fileRecordId);
    }
}
