package com.kun.linkage.clearing.service.kunlinkage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.clearing.constant.ClearingApplicationConstant;
import com.kun.linkage.common.db.entity.OrganizationCustomerCardInfo;
import com.kun.linkage.common.db.mapper.OrganizationCustomerCardInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class KLOrganizationCustomerCardInfoService {

    @Resource
    private OrganizationCustomerCardInfoMapper organizationCustomerCardInfoMapper;

    /**
     * 获取组织客户卡信息
     *
     * @param organizationNo 机构号
     * @param processor      通道
     * @param cardId         卡ID
     * @return OrganizationCustomerCardInfo
     */
    @Cacheable(value = ClearingApplicationConstant.APPLICATION_NAME,
        key = "'organizationCustomerCardInfo:' + #organizationNo + ':' + #processor + ':' + #cardId",
        unless = "#result == null")
    public OrganizationCustomerCardInfo getOrganizationCustomerCardInfo(String organizationNo, String processor,
        String cardId) {
        if (organizationNo == null || processor == null || cardId == null) {
            log.warn("获取组织客户卡信息时参数为空，organizationNo: {}, processor: {}, cardId: {}", organizationNo, processor, cardId);
            return null;
        }
        return organizationCustomerCardInfoMapper.selectOne(
            new LambdaQueryWrapper<OrganizationCustomerCardInfo>().eq(OrganizationCustomerCardInfo::getOrganizationNo,
                    organizationNo).eq(OrganizationCustomerCardInfo::getProcessor, processor)
                .eq(OrganizationCustomerCardInfo::getCardId, cardId));
    }
}
