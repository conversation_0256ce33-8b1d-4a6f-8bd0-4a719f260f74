package com.kun.linkage.clearing.service.impl;

import com.kun.linkage.clearing.service.visa.VisaBaseFileReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
@Service
public class TestServiceImpl {

    @Resource
    private VisaBaseFileReadService visaBaseFileReadService;
    /**
     * 返回上传文件链接
     * @param file 上传文件
     * @return
     */
    public String uploadFile(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String fileName = null;

        if (originalFilename != null) {
            // 提取文件名（不包括路径）
            Path path = Paths.get(originalFilename);
            fileName = path.getFileName().toString();
        }

        File tempFile = null;
        try {

            String suffix = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "tmp";
            // 创建临时文件，使用原文件的后缀
            tempFile = File.createTempFile("upload_", "." + suffix);
            // 将上传的文件写入临时文件
            file.transferTo(tempFile);
            visaBaseFileReadService.testProcessFile(tempFile.getPath());
            //返回文件名称
            return tempFile.getName();
        }catch (Exception e) {
            log.error("文件上传失败:", e);
            return "文件上传失败";
        }finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }


}
