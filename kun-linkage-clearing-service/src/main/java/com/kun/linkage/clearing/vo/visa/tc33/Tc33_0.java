package com.kun.linkage.clearing.vo.visa.tc33;

import com.kun.linkage.clearing.annotation.VisaFileField;
import com.kun.linkage.clearing.vo.visa.MultiLineTCData;

public class Tc33_0 extends MultiLineTCData {

    @VisaFileField(position = 5, length = 6, fieldName = "Destination Identifier")
    private String destinationIdentifier;

    @VisaFileField(position = 11, length = 6, fieldName = "Source Identifier")
    private String sourceIdentifier;

    @VisaFileField(position = 17, length = 3, fieldName = "VCR Record Identifier")
    private String vcrRecordIdentifier;

    @VisaFileField(position = 20, length = 2, fieldName = "Dispute Status")
    private String disputeStatus;

    @VisaFileField(position = 22, length = 2, fieldName = "Dispute Transaction Code")
    private String disputeTransactionCode;

    @VisaFileField(position = 24, length = 1, fieldName = "Dispute Transaction Code Qualifier")
    private String disputeTransactionCodeQualifier;

    @VisaFileField(position = 25, length = 1, fieldName = "Originator Recipient Indicator")
    private String originatorRecipientIndicator;

    @VisaFileField(position = 26, length = 16, fieldName = "Account Number")
    private String accountNumber;

    @VisaFileField(position = 42, length = 3, fieldName = "Account Number Extension")
    private String accountNumberExtension;

    @VisaFileField(position = 45, length = 23, fieldName = "Acquirer Reference Number")
    private String acquirerReferenceNumber;

    @VisaFileField(position = 68, length = 4, fieldName = "Purchase Date (MMDD)")
    private String purchaseDate;

    @VisaFileField(position = 72, length = 12, fieldName = "Source Amount")
    private String sourceAmount;

    @VisaFileField(position = 84, length = 3, fieldName = "Source Currency Code")
    private String sourceCurrencyCode;

    @VisaFileField(position = 87, length = 25, fieldName = "Merchant Name")
    private String merchantName;

    @VisaFileField(position = 112, length = 13, fieldName = "Merchant City")
    private String merchantCity;

    @VisaFileField(position = 125, length = 3, fieldName = "Merchant Country Code")
    private String merchantCountryCode;

    @VisaFileField(position = 128, length = 4, fieldName = "Merchant Category Code")
    private String merchantCategoryCode;

    @VisaFileField(position = 132, length = 3, fieldName = "Merchant State/Province Code")
    private String merchantStateProvinceCode;

    @VisaFileField(position = 135, length = 5, fieldName = "Merchant ZIP Code")
    private String merchantZIPCode;

    @VisaFileField(position = 140, length = 1, fieldName = "Requested Payment Service")
    private String requestedPaymentService;

    @VisaFileField(position = 141, length = 6, fieldName = "Authorization Code")
    private String authorizationCode;

    @VisaFileField(position = 147, length = 2, fieldName = "POS Entry Mode")
    private String posEntryMode;

    @VisaFileField(position = 149, length = 4, fieldName = "Central Processing Date (YYDD)")
    private String centralProcessingDate;

    @VisaFileField(position = 153, length = 15, fieldName = "Card Acceptor ID")
    private String cardAcceptorID;

    @VisaFileField(position = 168, length = 1, fieldName = "Reimbursement Attribute")
    private String reimbursementAttribute;

    public String getDestinationIdentifier() {
        return destinationIdentifier;
    }

    public void setDestinationIdentifier(String destinationIdentifier) {
        this.destinationIdentifier = destinationIdentifier;
    }

    public String getSourceIdentifier() {
        return sourceIdentifier;
    }

    public void setSourceIdentifier(String sourceIdentifier) {
        this.sourceIdentifier = sourceIdentifier;
    }

    public String getVcrRecordIdentifier() {
        return vcrRecordIdentifier;
    }

    public void setVcrRecordIdentifier(String vcrRecordIdentifier) {
        this.vcrRecordIdentifier = vcrRecordIdentifier;
    }

    public String getDisputeStatus() {
        return disputeStatus;
    }

    public void setDisputeStatus(String disputeStatus) {
        this.disputeStatus = disputeStatus;
    }

    public String getDisputeTransactionCode() {
        return disputeTransactionCode;
    }

    public void setDisputeTransactionCode(String disputeTransactionCode) {
        this.disputeTransactionCode = disputeTransactionCode;
    }

    public String getDisputeTransactionCodeQualifier() {
        return disputeTransactionCodeQualifier;
    }

    public void setDisputeTransactionCodeQualifier(String disputeTransactionCodeQualifier) {
        this.disputeTransactionCodeQualifier = disputeTransactionCodeQualifier;
    }

    public String getOriginatorRecipientIndicator() {
        return originatorRecipientIndicator;
    }

    public void setOriginatorRecipientIndicator(String originatorRecipientIndicator) {
        this.originatorRecipientIndicator = originatorRecipientIndicator;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAccountNumberExtension() {
        return accountNumberExtension;
    }

    public void setAccountNumberExtension(String accountNumberExtension) {
        this.accountNumberExtension = accountNumberExtension;
    }

    public String getAcquirerReferenceNumber() {
        return acquirerReferenceNumber;
    }

    public void setAcquirerReferenceNumber(String acquirerReferenceNumber) {
        this.acquirerReferenceNumber = acquirerReferenceNumber;
    }

    public String getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(String purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public String getSourceAmount() {
        return sourceAmount;
    }

    public void setSourceAmount(String sourceAmount) {
        this.sourceAmount = sourceAmount;
    }

    public String getSourceCurrencyCode() {
        return sourceCurrencyCode;
    }

    public void setSourceCurrencyCode(String sourceCurrencyCode) {
        this.sourceCurrencyCode = sourceCurrencyCode;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantCity() {
        return merchantCity;
    }

    public void setMerchantCity(String merchantCity) {
        this.merchantCity = merchantCity;
    }

    public String getMerchantCountryCode() {
        return merchantCountryCode;
    }

    public void setMerchantCountryCode(String merchantCountryCode) {
        this.merchantCountryCode = merchantCountryCode;
    }

    public String getMerchantCategoryCode() {
        return merchantCategoryCode;
    }

    public void setMerchantCategoryCode(String merchantCategoryCode) {
        this.merchantCategoryCode = merchantCategoryCode;
    }

    public String getMerchantStateProvinceCode() {
        return merchantStateProvinceCode;
    }

    public void setMerchantStateProvinceCode(String merchantStateProvinceCode) {
        this.merchantStateProvinceCode = merchantStateProvinceCode;
    }

    public String getMerchantZIPCode() {
        return merchantZIPCode;
    }

    public void setMerchantZIPCode(String merchantZIPCode) {
        this.merchantZIPCode = merchantZIPCode;
    }

    public String getRequestedPaymentService() {
        return requestedPaymentService;
    }

    public void setRequestedPaymentService(String requestedPaymentService) {
        this.requestedPaymentService = requestedPaymentService;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public String getPosEntryMode() {
        return posEntryMode;
    }

    public void setPosEntryMode(String posEntryMode) {
        this.posEntryMode = posEntryMode;
    }

    public String getCentralProcessingDate() {
        return centralProcessingDate;
    }

    public void setCentralProcessingDate(String centralProcessingDate) {
        this.centralProcessingDate = centralProcessingDate;
    }

    public String getCardAcceptorID() {
        return cardAcceptorID;
    }

    public void setCardAcceptorID(String cardAcceptorID) {
        this.cardAcceptorID = cardAcceptorID;
    }

    public String getReimbursementAttribute() {
        return reimbursementAttribute;
    }

    public void setReimbursementAttribute(String reimbursementAttribute) {
        this.reimbursementAttribute = reimbursementAttribute;
    }
}
