package com.kun.linkage.clearing.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageQueryResVO;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageQueryVO;
import com.kun.linkage.clearing.facade.vo.boss.VisaRepotPageVO;
import com.kun.linkage.clearing.service.bpcFile.IClearingInfoService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.ClearingInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@Tag(name = "bpc清分文件解析数据", description = "bpc清分文件解析数据")
@RestController
@RequestMapping("/boss/clearingInfo")
public class KCClearingInfoController extends BaseVccBossController {

    @Autowired
    private IClearingInfoService clearingInfoService;

    /**
     * 分页查询bpc清分数据
     *
     * @param pageQueryVO
     * @return
     */
    @Operation(description = "分页查询bpc清分数据", summary = "分页查询bpc清分数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-clearing-info"})
    @PostMapping("/pageList")
    public Result<PageResult<KCClearingPageQueryResVO>> pageList(@RequestBody KCClearingPageQueryVO pageQueryVO) {
        return clearingInfoService.pageList(pageQueryVO);
    }

    /**
     * 清分详情
     * @param clearingId
     * @param clearingDate
     * @return
     */
    @Operation(description = "查询bpc清分详情", summary = "查询bpc清分详情")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-clearing-info"})
    @GetMapping("/detail")
    public Result<ClearingInfo> detail(@RequestParam(name = "clearingId") String clearingId,
                                       @RequestParam(name = "clearingDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate clearingDate) {
        return clearingInfoService.detail(clearingId,clearingDate);
    }

    /**
     * 异步导出清分数据
     * @param pageQueryVO
     * @return
     */
    @Operation(description = "异步导出清分数据", summary = "异步导出清分数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-clearing-info"})
    @PostMapping("/asyncExport")
    public Result<String> asyncExport(@RequestBody KCClearingPageQueryVO pageQueryVO) {
        return clearingInfoService.asyncExport(pageQueryVO);
    }
}
