package com.kun.linkage.clearing.service.kunlinkage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.auth.facade.constant.AuthReleaseFlagEnum;
import com.kun.linkage.auth.facade.constant.TransactionStatusEnum;
import com.kun.linkage.auth.facade.constant.TransactionTypeEnum;
import com.kun.linkage.clearing.ext.mapper.AuthFlowMapperExt;
import com.kun.linkage.clearing.facade.constant.KunLinkageClearingResponseCodeEnum;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.common.base.constants.CommonConstant;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthFlow;
import com.kun.linkage.common.db.mapper.AuthFlowMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class KLAuthFlowService {

    @Resource
    private AuthFlowMapper authFlowMapper;

    @Resource
    private AuthFlowMapperExt authFlowMapperExt;

    public List<AuthFlow> getAuthFlowsByClearingRequest(PostTransRequestVO postTransRequestVO) {
        if (StringUtils.isNotBlank(postTransRequestVO.getProcessorTransId())) {
            List<AuthFlow> authFlows = authFlowMapper.selectList(
                new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getProcessor, postTransRequestVO.getProcessor())
                    .eq(AuthFlow::getProcessorTransId, postTransRequestVO.getProcessorTransId())
                    .between(AuthFlow::getCreateTime,
                        DateTimeUtils.truncateToSecond(DateUtils.addDays(new Date(), -30)),
                        DateTimeUtils.getCurrentDateTime()));
            return authFlows;
        }
        if (StringUtils.isNotBlank(postTransRequestVO.getMerchantNo()) && StringUtils.isNotBlank(
            postTransRequestVO.getMti()) && StringUtils.isNotBlank(
            postTransRequestVO.getProcessingCode()) && StringUtils.isNotBlank(postTransRequestVO.getCardAcceptorId())) {
            List<AuthFlow> authFlows = authFlowMapper.selectList(
                new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getProcessor, postTransRequestVO.getProcessor())
                    .eq(AuthFlow::getMerchantNo, postTransRequestVO.getMerchantNo())
                    .eq(AuthFlow::getCardAcceptorId, postTransRequestVO.getCardAcceptorId()).in(AuthFlow::getTransType,
                        Arrays.asList(TransactionTypeEnum.AUTHORIZATION, TransactionTypeEnum.PRE_AUTH,
                            TransactionTypeEnum.PRE_AUTH_COMPLETION, TransactionTypeEnum.CASH_ADVANCE,
                            TransactionTypeEnum.REFUND, TransactionTypeEnum.TRANSFER_OUT, TransactionTypeEnum.TRANSFER_IN))
                    .eq(AuthFlow::getMti, postTransRequestVO.getMti())
                    .eq(AuthFlow::getProcessingCode, postTransRequestVO.getProcessingCode())
                    .between(AuthFlow::getCreateTime,
                        DateTimeUtils.truncateToSecond(DateUtils.addDays(new Date(), -30)),
                        DateTimeUtils.getCurrentDateTime()).orderByAsc(AuthFlow::getCreateTime));
            return authFlows;
        }
        return null;
    }

    public void updateClearingData(String id, Date createTime, BigDecimal clearingTransAmount,
        BigDecimal cardholderBillingAmount, BigDecimal cardholderMarkupBillingAmount, String clearFlag,
        String releaseFlag, String lockedFlag, Date updatedTime, String clearAccountingDate, boolean reversalFlag) {
        log.debug("reversalFlag:{}", reversalFlag);
        int row = authFlowMapperExt.updateClearingData(DateUtils.formatDate(createTime, CommonConstant.YYYYMM), id,
            clearingTransAmount, cardholderBillingAmount, cardholderMarkupBillingAmount, clearFlag, releaseFlag, lockedFlag,
            updatedTime, clearAccountingDate, reversalFlag);
        if (row <= 0) {
            log.error(
                "更新授权流水剩余交易金额失败，id: {}, 交易创建时间: {}, 清分交易金额: {}, 清分账单金额(不含markup):{}, 清分账单金额(含markup):{}, 清分标记: {}, 更新时间: {}",
                id, DateUtils.formatDate(createTime, DateUtils.DAY_PATTERN), clearingTransAmount, cardholderBillingAmount, cardholderMarkupBillingAmount, clearFlag,
                DateUtils.formatDate(updatedTime, DateUtils.DATETIME_PATTERN));
            throw new BusinessException(KunLinkageClearingResponseCodeEnum.TRANSACTION_AMOUNT_EXCEEDS_LIMIT.getCode());
        } else {
            log.info(
                "更新授权流水剩余交易金额成功，id: {}, createTime: {}, 清分交易金额: {}, 清分账单金额(不含markup):{}, 清分账单金额(含markup):{}, clearFlag: {}, updatedTime: {}",
                id, DateUtils.formatDate(createTime, DateUtils.DAY_PATTERN), clearingTransAmount, cardholderBillingAmount, cardholderMarkupBillingAmount, clearFlag,
                DateUtils.formatDate(updatedTime, DateUtils.DATETIME_PATTERN));
        }
    }

    public AuthFlow queryAuthFlowByProcessorTransId(String processor, String merchantNo, String processorCardId,
        String processorTransId) {
        return authFlowMapper.selectOne(
            new LambdaQueryWrapper<AuthFlow>()
                .eq(AuthFlow::getProcessor, processor)
                .eq(AuthFlow::getMerchantNo, merchantNo)
                .eq(AuthFlow::getProcessorCardId, processorCardId)
                .eq(AuthFlow::getProcessorTransId, processorTransId)
                .in(AuthFlow::getTransType,
                    Arrays.asList(TransactionTypeEnum.AUTHORIZATION.getCode(), TransactionTypeEnum.PRE_AUTH.getCode(),
                        TransactionTypeEnum.PRE_AUTH_COMPLETION.getCode(), TransactionTypeEnum.CASH_ADVANCE.getCode(),
                        TransactionTypeEnum.REFUND.getCode(), TransactionTypeEnum.TRANSFER_OUT.getCode(), TransactionTypeEnum.TRANSFER_IN.getCode()))
                .between(AuthFlow::getCreateTime,
                    DateTimeUtils.truncateToSecond(DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -30)),
                    DateTimeUtils.getCurrentDateTime())
                .last("limit 1"));
    }

    public void updateReleaseAuthFlow(String id, BigDecimal remainingTransAmount, BigDecimal remainingBillingAmount,
        BigDecimal remainingMarkupBillingAmount, BigDecimal amt, String cardholderBillingCurrency,
        BigDecimal cardholderMarkupBillingAmount, Date createTime) {
        AuthFlow updateAuthFlow = new AuthFlow();
        updateAuthFlow.setId(id);
        updateAuthFlow.setRemainingTransAmount(BigDecimal.ZERO);
        updateAuthFlow.setRemainingBillingAmount(BigDecimal.ZERO);
        updateAuthFlow.setRemainingMarkupBillingAmount(BigDecimal.ZERO);
        updateAuthFlow.setReleaseFlag(AuthReleaseFlagEnum.RELEASED.getValue());
        updateAuthFlow.setReleaseTime(DateTimeUtils.getCurrentDateTime());
        updateAuthFlow.setUpdateTime(updateAuthFlow.getReleaseTime());
        updateAuthFlow.setReleaseTransAmount(amt);
        updateAuthFlow.setReleaseMarkupBillingAmount(cardholderMarkupBillingAmount);
        int update = authFlowMapper.update(updateAuthFlow,
            new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getId, id).eq(AuthFlow::getCreateTime, createTime)
                .eq(AuthFlow::getReleaseFlag, AuthReleaseFlagEnum.LOCKED.getValue()));
        if (update <= 0) {
            log.error(
                "更新授权流水失败，id: {}, 剩余交易金额: {}, 剩余账单金额(不含markup): {}, 剩余账单金额(含markup): {}, 请求释放的交易金额: {}, 账单币种: {}, 请求释放的账单金额(含markup): {}, 交易创建时间: {}",
                id, remainingTransAmount, remainingBillingAmount, remainingMarkupBillingAmount, amt,
                cardholderBillingCurrency, cardholderMarkupBillingAmount,
                DateUtils.formatDate(createTime, DateUtils.DATETIME_PATTERN));
            throw new BusinessException(KunLinkageClearingResponseCodeEnum.AUTH_RELEASE_FAIL.getCode());
        } else {
            log.info(
                "更新授权流水成功，id: {}, 剩余交易金额: {}, 剩余账单金额(不含markup): {}, 剩余账单金额(含markup): {}, 请求释放的交易金额: {}, 账单币种: {}, 请求释放的账单金额(含markup): {}, 交易创建时间: {}",
                id, remainingTransAmount, remainingBillingAmount, remainingMarkupBillingAmount, amt,
                cardholderBillingCurrency, cardholderMarkupBillingAmount,
                DateUtils.formatDate(createTime, DateUtils.DATETIME_PATTERN));
        }
    }

    public List<AuthFlow> getFirstAuthFlowsByAuthFlow(AuthFlow authFlow) {
        if (authFlow == null) {
            return new ArrayList<>();
        }
        List<AuthFlow> authFlows = authFlowMapper.selectList(
            new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getProcessor, authFlow.getProcessor())
                .ne(AuthFlow::getId, authFlow.getId())// Exclude the current auth flow
                .eq(AuthFlow::getMerchantNo, authFlow.getMerchantNo())
                .eq(AuthFlow::getCardAcceptorId, authFlow.getCardAcceptorId())
                .eq(AuthFlow::getProcessorCardId, authFlow.getProcessorCardId())
                .eq(AuthFlow::getStatus, TransactionStatusEnum.SUCCESS.getCode())
                .eq(AuthFlow::getApproveCode, authFlow.getApproveCode()).in(AuthFlow::getTransType,
                    Arrays.asList(TransactionTypeEnum.AUTHORIZATION.getCode(), TransactionTypeEnum.PRE_AUTH.getCode(),
                        TransactionTypeEnum.PRE_AUTH_COMPLETION.getCode(), TransactionTypeEnum.CASH_ADVANCE.getCode(),
                        TransactionTypeEnum.TRANSFER_OUT.getCode())).between(AuthFlow::getCreateTime,
                    DateTimeUtils.truncateToSecond(DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -30)),
                    DateTimeUtils.getCurrentDateTime()).orderByAsc(AuthFlow::getCreateTime));
        return authFlows;
    }
}
