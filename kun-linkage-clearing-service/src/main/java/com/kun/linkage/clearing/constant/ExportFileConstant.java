package com.kun.linkage.clearing.constant;

/**
 * 导出文件相关常量
 */
public class ExportFileConstant {

    /**
     * 文件类型
     */
    public static class FileType {
        /** VISA清分详情 */
        public static final String VISA_GW_SETTLEMENT_DATA = "VISA_GW_SETTLEMENT_DATA";
    }

    /**
     * 文件状态
     */
    public static class FileStatus {
        /** 处理中 */
        public static final String PROCESSING = "PROCESSING";
        /** 成功 */
        public static final String SUCCESS = "SUCCESS";
        /** 失败 */
        public static final String FAILED = "FAILED";
    }

    /**
     * CSV文件头部
     */
    public static class CsvHeaders {
        /** VISA清分详情CSV头部 */
        public static final String[] VISA_SETTLEMENT_DETAIL = {
            "业务系统",
            "通道", 
            "清算日期",
            "授权日期时间",
            "商户号",
            "商户名称",
            "Card ID",
            "交易类型",
            "交易币种",
            "交易清算金额",
            "持卡人币种",
            "持卡人清算金额",
            "清算状态",
            "参考号",
            "授权码",
            "ARN",
            "清算流水号",
            "原清算流水号",
            "系统授权号"
        };
    }

    /**
     * 文件名模板
     */
    public static class FileNameTemplate {
        /** VISA清分详情文件名模板 */
        public static final String VISA_SETTLEMENT_DETAIL = "VISA_SettlementDetail_%s.csv";
    }

    /**
     * S3文件夹路径
     */
    public static class S3FolderPath {
        /** 清分导出文件夹 */
        public static final String CLEARING_EXPORT = "/clearing-export";
    }

    /**
     * 日期范围限制
     */
    public static class DateRangeLimit {
        /** 最大查询天数 */
        public static final int MAX_QUERY_DAYS = 31;
    }
}
