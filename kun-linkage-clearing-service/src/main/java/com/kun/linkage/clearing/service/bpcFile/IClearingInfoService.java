package com.kun.linkage.clearing.service.bpcFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageQueryResVO;
import com.kun.linkage.clearing.facade.vo.boss.KCClearingPageQueryVO;
import com.kun.linkage.clearing.facade.vo.boss.VisaRepotPageVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.ClearingInfo;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface IClearingInfoService extends IService<ClearingInfo> {

    /**
     * 更新清分剩余可用金额
     * @param remainingClearAmount 清分剩余可用金额
     * @param clearingId 清分id
     * @param clearingDate 清分日期
     * @return 成功或者失败
     */
    Boolean updateRemainingClearAmountById(BigDecimal remainingClearAmount,Long clearingId,LocalDate clearingDate);

    /**
     * 更新通知结果
     * @param clearingId
     * @param clearingDate
     * @return
     */
    Boolean updateNotifyResultsById(Long clearingId,LocalDate clearingDate,Integer notifyResults);

    /**
     * 根据arn查询近60天的清分数据
     * @param acqArn
     * @param transCode
     * @return
     */
    ClearingInfo selectOriginalClearingInfoByArn(String acqArn, String transCode, LocalDate startClearingDate, LocalDate endClearingDate);

    /**
     * 分页查询清分数据
     * @param pageQueryVO 分页查询
     * @return
     */
    Result<PageResult<KCClearingPageQueryResVO>> pageList(KCClearingPageQueryVO pageQueryVO);

    /**
     * 查询清分详情数据
     * @param clearingId 清分主键id
     * @param clearingDate 清分日期
     * @return
     */
    Result<ClearingInfo> detail(String clearingId, LocalDate clearingDate);

    /**
     * 异步导出清分数据
     * @param pageQueryVO 查询条件
     * @return 文件记录ID
     */
    Result<String> asyncExport(KCClearingPageQueryVO pageQueryVO);

}
