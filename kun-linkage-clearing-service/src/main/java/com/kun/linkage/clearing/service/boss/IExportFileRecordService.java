package com.kun.linkage.clearing.service.boss;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.KCExportFileRecord;
import com.kun.linkage.clearing.facade.vo.boss.ExportFileRecordPageQueryVO;
import com.kun.linkage.clearing.facade.vo.boss.ExportFileRecordVO;

/**
 * <p>
 * 导出文件记录服务接口
 * </p>
 *
 * @since 2025-07-30
 */
public interface IExportFileRecordService extends IService<KCExportFileRecord> {

    /**
     * 创建文件记录
     * @param fileName 文件名
     * @param fileType 文件类型
     * @return 文件记录
     */
    KCExportFileRecord createFileRecord(String fileName, String fileType);

    /**
     * 更新文件记录为成功状态
     * @param fileRecordId 文件记录ID
     * @param s3Url S3文件URL
     * @param fileSize 文件大小
     */
    void updateFileRecordSuccess(String fileRecordId, String s3Url, Long fileSize);

    /**
     * 更新文件记录为失败状态
     * @param fileRecordId 文件记录ID
     * @param errorMessage 错误信息
     */
    void updateFileRecordFailed(String fileRecordId, String errorMessage);

    /**
     * 分页查询导出文件记录
     * @param pageQueryVO 查询条件
     * @return 分页结果
     */
    Result<PageResult<ExportFileRecordVO>> pageList(ExportFileRecordPageQueryVO pageQueryVO);

    /**
     * 根据文件记录ID获取下载URL
     * @param fileRecordId 文件记录ID
     * @return 下载URL
     */
    Result<String> getDownloadUrl(String fileRecordId);
}
