package com.kun.linkage.clearing.service.bpcFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kun.linkage.common.db.entity.CurrencyInfo;

public interface CurrencyInfoService extends IService<CurrencyInfo> {


    /**
     * 根据币种查询visa卡的精度
     * @param currencyNo 3位数字币种
     * @return
     */
    public Integer getCurrencyPrecisionByVisaCurrencyNoFromCache(String currencyNo);

    /**
     * 根据visa code 获取币种信息
     * @param currencyNo 三位的数字
     * @return
     */
    CurrencyInfo selectByVisaCurrencyNoFromCache(String currencyNo);
}
