package com.kun.linkage.clearing.service.kunlinkage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.clearing.constant.ClearingApplicationConstant;
import com.kun.linkage.common.db.entity.OrganizationCustomerAccountInfo;
import com.kun.linkage.common.db.mapper.OrganizationCustomerAccountInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class KLOrganizationCustomerAccountInfoService {

    @Resource
    private OrganizationCustomerAccountInfoMapper organizationCustomerAccountInfoMapper;

    /**
     * 获取组织机构客户账户信息
     *
     * @param organizationNo            组织机构编号
     * @param customerId                客户ID
     * @param cardholderCurrency 持卡人结算币种
     * @param status                    状态
     * @return 组织机构客户账户信息列表
     */
    @Cacheable(value = ClearingApplicationConstant.APPLICATION_NAME,
        key = "'organizationCustomerAccountInfo:' + #organizationNo + ':' + #customerId + ':' + #cardholderCurrency+ ':' + #status",
        unless = "#result == null")
    public List<OrganizationCustomerAccountInfo> getOrganizationCustomerAccountInfo(String organizationNo,
        String customerId, String cardholderCurrency, String status) {
        if (organizationNo == null || customerId == null || status == null) {
            log.warn("获取组织机构客户账户信息时参数为空，organizationNo: {}, customerId: {}, status: {}",
                organizationNo, customerId, status);
            return null;
        }
        return organizationCustomerAccountInfoMapper.selectList(
            new LambdaQueryWrapper<OrganizationCustomerAccountInfo>().eq(
                    OrganizationCustomerAccountInfo::getOrganizationNo, organizationNo)
                .eq(OrganizationCustomerAccountInfo::getCustomerId, customerId)
                .eq(OrganizationCustomerAccountInfo::getCurrencyCode, cardholderCurrency)
                .eq(OrganizationCustomerAccountInfo::getStatus, status));
    }
}
