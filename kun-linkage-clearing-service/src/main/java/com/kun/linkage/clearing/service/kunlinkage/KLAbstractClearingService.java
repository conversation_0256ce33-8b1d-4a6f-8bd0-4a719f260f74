package com.kun.linkage.clearing.service.kunlinkage;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.common.util.uid.DateUtils;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.account.facade.api.AccountTransactionFacade;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.constants.AccountTipConstant;
import com.kun.linkage.account.facade.enums.AccountingActionEnum;
import com.kun.linkage.auth.facade.constant.TransactionStatusEnum;
import com.kun.linkage.clearing.dto.TransactionClearingContext;
import com.kun.linkage.clearing.facade.constant.KLClearingExceptionReasonEnum;
import com.kun.linkage.clearing.facade.constant.KLClearingExceptionStatusEnum;
import com.kun.linkage.clearing.facade.constant.KunLinkageClearingResponseCodeEnum;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransResponseVO;
import com.kun.linkage.clearing.service.*;
import com.kun.linkage.clearing.utils.I18nMessageService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonConstant;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.DirectionEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.AuthAccountLogMapper;
import com.kun.linkage.common.db.mapper.ClearingExceptionTransMapper;
import com.kun.linkage.common.db.mapper.KLClearingTransMapper;
import com.kun.linkage.customer.facade.enums.BusinessAccountTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public abstract class KLAbstractClearingService {

    private static final Logger log = LoggerFactory.getLogger(KLAbstractClearingService.class);

    /** 国际化消息服务 */
    @Resource
    protected I18nMessageService i18nMessageService;
    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;

    @Resource
    protected KLClearingTransMapper clearingTransMapper;

    @Resource
    protected KLAuthFlowService authFlowService;

    @Resource
    protected KLOrganizationBasicInfoService organizationBasicInfoService;

    @Resource
    protected KLOrganizationCustomerCardInfoService organizationCustomerCardInfoService;

    @Resource
    protected KLOrganizationCustomerAccountInfoService organizationCustomerAccountInfoService;

    @Resource
    protected KLAuthFlowExtService authFlowExtService;

    @Resource
    protected ChMarkupService chMarkupService;

    @Resource
    protected AccountTransactionFacade accountTransactionFacade;

    @Resource
    protected AuthAccountLogMapper authAccountLogMapper;

    @Resource
    protected ClearingExceptionTransMapper clearingExceptionTransMapper;

    /**
     * 保存请求日志
     * <p>
     * 生成唯一清分ID，保存请求上下文到数据库日志表。 使用雪花算法生成唯一ID，确保分布式环境下的ID唯一性。
     *
     * @param context 清分上下文，包含请求信息和响应信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePostTransRequest(TransactionClearingContext context) {
        long snowId = uidGenerator.getUID();
        context.setClearingId(String.valueOf(snowId));
        KLClearingTrans clearingTrans = new KLClearingTrans();
        clearingTrans.setClearingId(context.getClearingId());
        if (context.getAuthFlow() != null) {
            clearingTrans.setAuthFlowId(context.getAuthFlow().getId());
        }
        clearingTrans.setGatewayRequestId(context.getPostTransRequestVO().getRequestId());
        clearingTrans.setGatewayClearingId(context.getPostTransRequestVO().getGatewayClearingId());
        clearingTrans.setOriginalGatewayClearingId(context.getPostTransRequestVO().getOriginalGatewayClearingId());
        clearingTrans.setClearingType(context.getPostTransRequestVO().getClearingType());
        clearingTrans.setProcessor(context.getPostTransRequestVO().getProcessor());
        clearingTrans.setProcessorRequestId(context.getPostTransRequestVO().getProcessorRequestId());
        clearingTrans.setProcessorTransId(context.getPostTransRequestVO().getProcessorTransId());
        clearingTrans.setOriginalProcessorTransId(context.getPostTransRequestVO().getOriginalProcessorTransId());
        clearingTrans.setMerchantNo(context.getPostTransRequestVO().getMerchantNo());
        clearingTrans.setMerchantName(context.getOrganizationBasicInfo() == null ? null : context.getOrganizationBasicInfo().getOrganizationName());
        clearingTrans.setCustomerId(context.getOrganizationCustomerCardInfo() == null ? null : context.getOrganizationCustomerCardInfo().getCustomerId());
        clearingTrans.setStatus(TransactionStatusEnum.PENDING.getCode());
        clearingTrans.setMti(context.getPostTransRequestVO().getMti());
        clearingTrans.setProcessingCode(context.getPostTransRequestVO().getProcessingCode());
        clearingTrans.setSystemsTraceAuditNumber(context.getPostTransRequestVO().getSystemsTraceAuditNumber());
        clearingTrans.setGatewayCardId(context.getPostTransRequestVO().getGatewayCardId());
        clearingTrans.setProcessorCardId(context.getPostTransRequestVO().getProcessorCardId());
        clearingTrans.setIssuerCardId(context.getPostTransRequestVO().getIssuerCardId());
        clearingTrans.setMaskedCardNo(context.getPostTransRequestVO().getMaskedCardNo());
        clearingTrans.setTransType(context.getPostTransRequestVO().getTransType());
        clearingTrans.setCardProductCode(context.getPostTransRequestVO().getCardProductCode());
        clearingTrans.setTransCurrency(context.getPostTransRequestVO().getTransCurrency());
        clearingTrans.setTransCurrencyExponent(context.getPostTransRequestVO().getTransCurrencyExponent());
        clearingTrans.setTransAmount(context.getPostTransRequestVO().getTransAmount());
        clearingTrans.setTransFee(context.getPostTransRequestVO().getTransFee());
        clearingTrans.setCardholderBillingCurrency(context.getPostTransRequestVO().getCardholderBillingCurrency());
        clearingTrans.setCardholderCurrencyExponent(context.getPostTransRequestVO().getCardholderCurrencyExponent());
        clearingTrans.setCardholderBillingAmount(context.getPostTransRequestVO().getCardholderBillingAmount());
        clearingTrans.setCardholderMarkupBillingAmount(
            context.getPostTransRequestVO().getCardholderMarkupBillingAmount());
        clearingTrans.setMarkupRate(context.getPostTransRequestVO().getMarkupRate());
        clearingTrans.setMarkupAmount(context.getPostTransRequestVO().getMarkupAmount());
        clearingTrans.setPosEntryMode(context.getPostTransRequestVO().getPosEntryMode());
        clearingTrans.setTransactionLocalDatetime(context.getPostTransRequestVO().getTransactionLocalDatetime());
        clearingTrans.setConversionRateCardholderBilling(
            context.getPostTransRequestVO().getConversionRateCardholderBilling());
        clearingTrans.setApproveCode(context.getPostTransRequestVO().getApproveCode());
        clearingTrans.setAcquireReferenceNo(context.getPostTransRequestVO().getAcquireReferenceNo());
        clearingTrans.setCardAcceptorName(context.getPostTransRequestVO().getCardAcceptorName());
        clearingTrans.setCardAcceptorId(context.getPostTransRequestVO().getCardAcceptorId());
        clearingTrans.setCardAcceptorTid(context.getPostTransRequestVO().getCardAcceptorTid());
        clearingTrans.setCardAcceptorCountryCode(context.getPostTransRequestVO().getCardAcceptorCountryCode());
        clearingTrans.setCardAcceptorPostalCode(context.getPostTransRequestVO().getCardAcceptorPostalCode());
        clearingTrans.setCardAcceptorRegion(context.getPostTransRequestVO().getCardAcceptorRegion());
        clearingTrans.setCardAcceptorCity(context.getPostTransRequestVO().getCardAcceptorCity());
        clearingTrans.setCardAcceptorStreet(context.getPostTransRequestVO().getCardAcceptorStreet());
        clearingTrans.setMcc(context.getPostTransRequestVO().getMcc());
        clearingTrans.setProcessorExt1(context.getPostTransRequestVO().getProcessorExt1());
        clearingTrans.setClearFlag("Y");
        clearingTrans.setTransDate(context.getPostTransRequestVO().getTransDate());
        clearingTrans.setClearingDate(DateUtils.formatDate(DateTimeUtils.getCurrentDateTime(), "yyyyMMdd"));
        clearingTrans.setClearAmount(context.getPostTransRequestVO().getClearAmount());
        clearingTrans.setFeeInterchangeSign(context.getPostTransRequestVO().getFeeInterchangeSign());
        clearingTrans.setFeeInterchangeAmount(context.getPostTransRequestVO().getFeeInterchangeAmount());
        clearingTrans.setIssuerCharge(context.getPostTransRequestVO().getIssuerCharge());
        clearingTrans.setNationalReimbFee(context.getPostTransRequestVO().getNationalReimbFee());
        clearingTrans.setIngFeeId(context.getPostTransRequestVO().getIngFeeId());
        clearingTrans.setInterFeeIndicator(context.getPostTransRequestVO().getInterFeeIndicator());
        clearingTrans.setFeeProgramIndicator(context.getPostTransRequestVO().getFeeProgramIndicator());
        clearingTrans.setOverseasFlag(context.getPostTransRequestVO().getOverseasFlag());
        clearingTrans.setConversionDate(context.getPostTransRequestVO().getConversionDate());
        clearingTrans.setReversalFlag(context.getPostTransRequestVO().getReversalFlag());
        clearingTrans.setArn(context.getPostTransRequestVO().getArn());
        clearingTrans.setAcqBin(context.getPostTransRequestVO().getAcqBin());
        clearingTrans.setUsageCode(context.getPostTransRequestVO().getUsageCode());
        clearingTrans.setReasonCode(context.getPostTransRequestVO().getReasonCode());
        clearingTrans.setCardSchemaProductId(context.getPostTransRequestVO().getCardSchemaProductId());
        clearingTrans.setCreateTime(DateTimeUtils.getCurrentDateTime());
        if (context.getOriginalClearingTrans() != null) {
            clearingTrans.setOriginalClearingId(context.getOriginalClearingTrans().getClearingId());
        }
        int insert = clearingTransMapper.insert(clearingTrans);
        log.info("保存请求记录: {}, 影响行数: {}", JSON.toJSONString(clearingTrans), insert);
        context.setClearingTrans(clearingTrans);
    }

    public PostTransResponseVO verifyParameterFromDBAndQueryReferData(TransactionClearingContext context) {
        // 1. 重复性校验（24小时内同一请求ID+处理方）
        List<KLClearingTrans> gatewayRequestNoList = clearingTransMapper.selectList(
            new LambdaQueryWrapper<KLClearingTrans>().eq(KLClearingTrans::getGatewayRequestId,
                    context.getPostTransRequestVO().getRequestId())
                .gt(KLClearingTrans::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -1))
                .lt(KLClearingTrans::getCreateTime, DateTimeUtils.getCurrentDateTime()));
        if (gatewayRequestNoList.size() > 0) {
            log.warn("重复请求ID, 网关请求ID: {}, 处理方: {}, 重复次数: {}", context.getPostTransRequestVO().getGatewayClearingId(),
                context.getPostTransRequestVO().getProcessor(), gatewayRequestNoList.size());
            PostTransResponseVO responseVO = context.getPostTransResponseVO();
            responseVO.setCode(KunLinkageClearingResponseCodeEnum.DUPLICATE_TRANSACIION.getCode());
            responseVO.setMessage(i18nMessageService.getMessage(responseVO.getCode()));
            responseVO.setRequestId(context.getPostTransRequestVO().getRequestId());
            return responseVO;
        }
        List<KLClearingTrans> gatewayClearingIdList = clearingTransMapper.selectList(
            new LambdaQueryWrapper<KLClearingTrans>().eq(KLClearingTrans::getGatewayClearingId,
                    context.getPostTransRequestVO().getGatewayClearingId())
                .gt(KLClearingTrans::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -1))
                .lt(KLClearingTrans::getCreateTime, DateTimeUtils.getCurrentDateTime()));
        if (gatewayClearingIdList.size() > 0) {
            log.warn("重复清分ID, 网关清分ID: {}, 处理方: {}, 重复次数: {}", context.getPostTransRequestVO().getGatewayClearingId(),
                context.getPostTransRequestVO().getProcessor(), gatewayClearingIdList.size());
            PostTransResponseVO responseVO = context.getPostTransResponseVO();
            responseVO.setCode(KunLinkageClearingResponseCodeEnum.DUPLICATE_TRANSACIION.getCode());
            responseVO.setMessage(i18nMessageService.getMessage(responseVO.getCode()));
            responseVO.setRequestId(context.getPostTransRequestVO().getRequestId());
            return responseVO;
        }
        if (this.processOrganizationInfo(context, context.getPostTransRequestVO()) != null){
            return context.getPostTransResponseVO();
        }
        // 3. 检查卡ID是否存在及卡状态
        if (this.processCustomerCardInfo(context, context.getPostTransRequestVO()) != null){
            return context.getPostTransResponseVO();
        }
        if (processCustomerAccountInfo(context, context.getOrganizationCustomerCardInfo()) != null){
            return context.getPostTransResponseVO();
        }
        // 4. 检查授权是否存在
        if (StringUtils.isNotBlank(context.getPostTransRequestVO().getProcessorTransId())){
            AuthFlow authFlow = authFlowService.queryAuthFlowByProcessorTransId(
                context.getPostTransRequestVO().getProcessor(),
                context.getPostTransRequestVO().getMerchantNo(),
                context.getPostTransRequestVO().getProcessorCardId(),
                context.getPostTransRequestVO().getProcessorTransId());
            if (authFlow == null) {
                log.warn("未找到对应的授权交易, 处理方: {}, 商户号: {}, 卡ID: {}, processor交易交易ID: {}",
                    context.getPostTransRequestVO().getProcessor(),
                    context.getPostTransRequestVO().getMerchantNo(),
                    context.getPostTransRequestVO().getProcessorCardId(),
                    context.getPostTransRequestVO().getProcessorTransId());
            } else {
                log.info("找到对应的授权交易, 处理方: {}, 商户号: {}, 卡ID: {}, processor交易交易ID: {}",
                    context.getPostTransRequestVO().getProcessor(),
                    context.getPostTransRequestVO().getMerchantNo(),
                    context.getPostTransRequestVO().getProcessorCardId(),
                    context.getPostTransRequestVO().getProcessorTransId());
                context.setAuthFlow(authFlow);
                // 获取并设置当前授权流程的扩展信息
                context.setAuthFlowExt(authFlowExtService.getAuthFlowExtByAuthFlowId(context.getAuthFlow().getId(), context.getAuthFlow().getCreateTime()));
            }
        }
        if (StringUtils.isNotBlank(context.getPostTransRequestVO().getOriginalGatewayClearingId())){
            KLClearingTrans originalClearingTrans = clearingTransMapper.selectOne(
                new LambdaQueryWrapper<KLClearingTrans>()
                    .eq(KLClearingTrans::getGatewayClearingId,
                        context.getPostTransRequestVO().getOriginalGatewayClearingId())
                    .eq(KLClearingTrans::getProcessor, context.getPostTransRequestVO().getProcessor())
                    .eq(KLClearingTrans::getMerchantNo, context.getPostTransRequestVO().getMerchantNo())
                    .gt(KLClearingTrans::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -30))
                    .lt(KLClearingTrans::getCreateTime, DateTimeUtils.getCurrentDateTime()));
            if (originalClearingTrans == null) {
                log.warn("未找到对应的原始清分交易, 网关清分ID: {}, 处理方: {}, 商户号: {}",
                    context.getPostTransRequestVO().getOriginalGatewayClearingId(),
                    context.getPostTransRequestVO().getProcessor(),
                    context.getPostTransRequestVO().getMerchantNo());
            } else {
                log.info("找到对应的原始清分交易, 网关清分ID: {}, 处理方: {}, 商户号: {}",
                    context.getPostTransRequestVO().getOriginalGatewayClearingId(),
                    context.getPostTransRequestVO().getProcessor(),
                    context.getPostTransRequestVO().getMerchantNo());
                context.setOriginalClearingTrans(originalClearingTrans);
            }
        }
        return null;
    }

    @Nullable
    private PostTransResponseVO processCustomerAccountInfo(TransactionClearingContext context,
        OrganizationCustomerCardInfo organizationCustomerCardInfo) {
        List<OrganizationCustomerAccountInfo> organizationCustomerAccountInfos =
            organizationCustomerAccountInfoService.getOrganizationCustomerAccountInfo(
                organizationCustomerCardInfo.getOrganizationNo(), organizationCustomerCardInfo.getCustomerId(),
                context.getPostTransRequestVO().getCardholderBillingCurrency(),
                ValidStatusEnum.VALID.getValue());
        if (organizationCustomerAccountInfos != null && organizationCustomerAccountInfos.size() > 0) {
            boolean hasBasicAccount = false;
            // 预留
            boolean hasCreditAccount = false;
            // 预留
            boolean hasCryptoAccount = false;
            for (OrganizationCustomerAccountInfo customerAccountInfo : organizationCustomerAccountInfos) {
                if (BusinessAccountTypeEnum.BASIC.getValue().equals(customerAccountInfo.getAccountType())) {
                    context.setCustomerBasicAccount(customerAccountInfo);
                    hasBasicAccount = true;
                } else if (BusinessAccountTypeEnum.CREDIT.getValue().equals(customerAccountInfo.getAccountType())) {
                    context.setCustomerCreditAccount(customerAccountInfo);
                    hasCreditAccount = true;
                } else if (BusinessAccountTypeEnum.CRYPTO.getValue().equals(customerAccountInfo.getAccountType())) {
                    context.setCustomerCryptoAccount(customerAccountInfo);
                    hasCryptoAccount = true;
                }
            }
            if (!hasBasicAccount) {
                log.error("商户没有基本账户: {}", organizationCustomerCardInfo.getOrganizationNo());
                PostTransResponseVO responseVO = context.getPostTransResponseVO();
                responseVO.setCode(KunLinkageClearingResponseCodeEnum.CAN_NOT_BE_ACCOUNTING.getCode());
                responseVO.setMessage(i18nMessageService.getMessage(responseVO.getCode()));
                return responseVO;
            }
        } else {
            log.error("客户没有账户信息, 商户号:{}, 客户号: {}, 币种: {}", organizationCustomerCardInfo.getOrganizationNo(), organizationCustomerCardInfo.getCustomerId(), context.getPostTransRequestVO().getCardholderBillingCurrency());
            PostTransResponseVO responseVO = context.getPostTransResponseVO();
            responseVO.setCode(KunLinkageClearingResponseCodeEnum.CAN_NOT_BE_ACCOUNTING.getCode());
            responseVO.setMessage(i18nMessageService.getMessage(responseVO.getCode()));
            return responseVO;
        }
        return null;
    }

    /**
     * 处理组织机构信息 查询并设置组织基本信息到上下文中
     *
     * @param context 交易清分上下文
     * @param request 交易请求对象
     */
    protected PostTransResponseVO processOrganizationInfo(TransactionClearingContext context, PostTransRequestVO request) {
        OrganizationBasicInfo orgInfo = organizationBasicInfoService.getOrganizationBasicInfo(request.getMerchantNo());
        if (orgInfo == null) {
            log.error("清分处理失败交易，未查询到机构信息，商户号：{}", request.getMerchantNo());
            PostTransResponseVO responseVO = context.getPostTransResponseVO();
            responseVO.setCode(KunLinkageClearingResponseCodeEnum.CARD_NOT_FOUND.getCode());
            responseVO.setMessage(i18nMessageService.getMessage(responseVO.getCode()));
            return responseVO;
        }
        context.setOrganizationBasicInfo(orgInfo);
        log.info("清分处理成功查询到组织机构信息，商户号：{}, 组织机构号：{}", request.getMerchantNo(),
            orgInfo.getOrganizationNo());
        return null;
    }

    /**
     * 处理客户卡信息 查询并设置客户卡信息到上下文中
     *
     * @param context 交易清分上下文
     * @param request 交易请求对象
     */
    protected PostTransResponseVO processCustomerCardInfo(TransactionClearingContext context, PostTransRequestVO request) {
        OrganizationCustomerCardInfo cardInfo =
            organizationCustomerCardInfoService.getOrganizationCustomerCardInfo(request.getMerchantNo(),
                request.getProcessor(), request.getGatewayCardId());
        if (cardInfo == null) {
            log.error("清分处理失败交易，未查询到客户卡信息，商户号：{}, 处理器卡号：{}", request.getMerchantNo(),
                request.getGatewayCardId());
            PostTransResponseVO responseVO = context.getPostTransResponseVO();
            responseVO.setCode(KunLinkageClearingResponseCodeEnum.CARD_NOT_FOUND.getCode());
            responseVO.setMessage(i18nMessageService.getMessage(responseVO.getCode()));
            return responseVO;
        }
        context.setOrganizationCustomerCardInfo(cardInfo);
        log.info("清分处理成功查询到客户卡信息，商户号：{}, 处理器卡号：{}", request.getMerchantNo(),
            request.getProcessorCardId());
        return null;
    }

    protected String getAccountingAction(TransactionClearingContext context) {
        return DirectionEnum.CREDIT.equals(context.getClearIngTypeEnum().getDirection()) ? AccountingActionEnum.CREDIT.name() : AccountingActionEnum.DEBIT.name();
    }

    protected void saveAccountLog(TransactionClearingContext context, AccountChangeBalanceReq req,
        Result<AccountChangeBalanceRes> result) {
        new Thread(
            () -> {
                try {
                    AuthAccountLog accountLoglog = new AuthAccountLog();
                    accountLoglog.setId(String.valueOf(uidGenerator.getUID()));
                    accountLoglog.setAuthFlowId(Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(context.getClearingTrans().getClearingId()));
                    accountLoglog.setAccountType(req.getBusinessType());
                    accountLoglog.setAccountNo(req.getAccountNo());
                    accountLoglog.setRequestJson(JSON.toJSONString(req));
                    accountLoglog.setRequestNo(req.getRequestNo());
                    accountLoglog.setResponseCode(result.getCode());
                    accountLoglog.setResponseMessage(StringUtils.isBlank(result.getMessage()) ? null : (result.getMessage().length() >255 ? result.getMessage().substring(0, 255) : result.getMessage()));
                    accountLoglog.setResponseJson(JSON.toJSONString(result));
                    accountLoglog.setCreateTime(DateTimeUtils.getCurrentDateTime());
                    accountLoglog.setUpdateTime(accountLoglog.getCreateTime());
                    int insertRows = authAccountLogMapper.insert(accountLoglog);
                    log.info("保存账户请求记录: {}, 影响行数: {}, 请求流水号: {}", JSON.toJSONString(accountLoglog), insertRows, accountLoglog.getRequestNo());
                } catch (Exception e) {
                    log.error("保存账户请求记录异常, 交易Id: {}, 账户请求记录请求参数: {}, 账户请求响应结果:{}, error: {}",
                        Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(context.getClearingTrans().getClearingId()), JSON.toJSONString(req),
                        JSON.toJSONString(result), e.getMessage(), e);
                }
            }
        ).start();
    }

    protected void handleAccountingFailure(TransactionClearingContext context, Result<AccountChangeBalanceRes> result) {
        // 记录清分异常
        this.recordClearingExceptionTrans(context, result.getCode(), result.getMessage());
        context.setNeedToRecordExceptionClearingTrans(true);
        context.setExceptionClearingTransRecorded(true);
    }

    protected void recordClearingExceptionTrans(TransactionClearingContext context, String errorCode, String errorMessage) {
        try {
            KLClearingExceptionTrans clearingExceptionTrans = new KLClearingExceptionTrans();
            clearingExceptionTrans.setId(String.valueOf(uidGenerator.getUID()));
            clearingExceptionTrans.setClearingId(context.getClearingId());
            clearingExceptionTrans.setProcessor(context.getPostTransRequestVO().getProcessor());
            clearingExceptionTrans.setProcessorRequestId(context.getPostTransRequestVO().getProcessorRequestId());
            clearingExceptionTrans.setProcessorTransId(context.getPostTransRequestVO().getProcessorTransId());
            clearingExceptionTrans.setOriginalProcessorTransId(context.getPostTransRequestVO().getOriginalProcessorTransId());
            clearingExceptionTrans.setMerchantNo(context.getPostTransRequestVO().getMerchantNo());
            clearingExceptionTrans.setMerchantName(context.getOrganizationBasicInfo() == null ? null : context.getOrganizationBasicInfo().getOrganizationName());
            clearingExceptionTrans.setCustomerId(context.getOrganizationCustomerCardInfo() == null ? null : context.getOrganizationCustomerCardInfo().getCustomerId());
            clearingExceptionTrans.setStatus(context.getAuthFlow() == null ? null : context.getAuthFlow().getStatus());
            clearingExceptionTrans.setMti(context.getPostTransRequestVO().getMti());
            clearingExceptionTrans.setProcessingCode(context.getPostTransRequestVO().getProcessingCode());
            clearingExceptionTrans.setGatewayCardId(context.getPostTransRequestVO().getGatewayCardId());
            clearingExceptionTrans.setProcessorCardId(context.getPostTransRequestVO().getProcessorCardId());
            clearingExceptionTrans.setIssuerCardId(context.getPostTransRequestVO().getIssuerCardId());
            clearingExceptionTrans.setMaskedCardNo(context.getPostTransRequestVO().getMaskedCardNo());
            clearingExceptionTrans.setTransType(context.getPostTransRequestVO().getTransType());
            clearingExceptionTrans.setCardProductCode(context.getPostTransRequestVO().getCardProductCode());
            clearingExceptionTrans.setTransCurrency(context.getPostTransRequestVO().getTransCurrency());
            clearingExceptionTrans.setTransAmount(context.getPostTransRequestVO().getTransAmount());
            clearingExceptionTrans.setTransFee(context.getPostTransRequestVO().getTransFee());
            clearingExceptionTrans.setCardholderBillingCurrency(context.getPostTransRequestVO().getCardholderBillingCurrency());
            clearingExceptionTrans.setCardholderBillingAmount(context.getPostTransRequestVO().getCardholderBillingAmount());
            clearingExceptionTrans.setCardholderMarkupBillingAmount(
                context.getPostTransRequestVO().getCardholderMarkupBillingAmount());
            clearingExceptionTrans.setMarkupRate(context.getPostTransRequestVO().getMarkupRate());
            clearingExceptionTrans.setMarkupAmount(context.getPostTransRequestVO().getMarkupAmount());
            clearingExceptionTrans.setPosEntryMode(context.getPostTransRequestVO().getPosEntryMode());
            clearingExceptionTrans.setTransactionLocalDatetime(
                context.getPostTransRequestVO().getTransactionLocalDatetime());
            clearingExceptionTrans.setConversionRateCardholderBilling(
                context.getPostTransRequestVO().getConversionRateCardholderBilling());
            clearingExceptionTrans.setApproveCode(context.getPostTransRequestVO().getApproveCode());
            clearingExceptionTrans.setAcquireReferenceNo(context.getPostTransRequestVO().getAcquireReferenceNo());
            clearingExceptionTrans.setCardAcceptorName(context.getPostTransRequestVO().getCardAcceptorName());
            clearingExceptionTrans.setCardAcceptorId(context.getPostTransRequestVO().getCardAcceptorId());
            clearingExceptionTrans.setCardAcceptorTid(context.getPostTransRequestVO().getCardAcceptorTid());
            clearingExceptionTrans.setCardAcceptorCountryCode(context.getPostTransRequestVO().getCardAcceptorCountryCode());
            clearingExceptionTrans.setCardAcceptorPostalCode(context.getPostTransRequestVO().getCardAcceptorPostalCode());
            clearingExceptionTrans.setCardAcceptorRegion(context.getPostTransRequestVO().getCardAcceptorRegion());
            clearingExceptionTrans.setCardAcceptorCity(context.getPostTransRequestVO().getCardAcceptorCity());
            clearingExceptionTrans.setCardAcceptorStreet(context.getPostTransRequestVO().getCardAcceptorStreet());
            clearingExceptionTrans.setMcc(context.getPostTransRequestVO().getMcc());
            clearingExceptionTrans.setArn(context.getPostTransRequestVO().getArn());
            clearingExceptionTrans.setProcessorExt1(context.getPostTransRequestVO().getProcessorExt1());
            clearingExceptionTrans.setOriginalAuthFlowId(context.getAuthFlow() == null ? null : context.getAuthFlow().getOriginalId());
            clearingExceptionTrans.setOriginalProcessorRequestId(context.getAuthFlow() == null ? null : context.getAuthFlow().getOriginalProcessorRequestId());
            clearingExceptionTrans.setOriginalTransTime(context.getAuthFlow() == null ? null : context.getAuthFlow().getOriginalTransTime());
            clearingExceptionTrans.setExceptionReason(convertClearingExceptionReason(errorCode, null));// 响应中的原因不能直接记，熔断的时候会导致超过字段长度
            clearingExceptionTrans.setTransDate(context.getPostTransRequestVO().getTransDate());
            clearingExceptionTrans.setTransTime(context.getAuthFlow() == null ? DateUtils.parseDate(context.getPostTransRequestVO().getTransactionLocalDatetime(),
                CommonConstant.YYYYMMDDHHMMSS) : context.getAuthFlow().getCreateTime());
            clearingExceptionTrans.setClearingDate(context.getPostTransRequestVO().getClearingDate());
            clearingExceptionTrans.setClearAmount(context.getPostTransRequestVO().getClearAmount());
            clearingExceptionTrans.setProcessFlag(KLClearingExceptionStatusEnum.TO_BE_PROCESSED.getValue());
            clearingExceptionTrans.setCreateTime(DateTimeUtils.getCurrentDateTime());
            clearingExceptionTrans.setUpdateTime(clearingExceptionTrans.getCreateTime());
            int insertRows = clearingExceptionTransMapper.insert(clearingExceptionTrans);
            log.error("保存清分异常记录: {}, 影响行数: {}", JSON.toJSONString(clearingExceptionTrans), insertRows);
        } catch (Exception e) {
            log.error("保存清分异常记录失败, 交易Id: {}, 错误码: {}, 错误信息: {}, 异常: {}",
                context.getClearingId(), errorCode, errorMessage, e.getMessage(), e);
        }
    }

    private String convertClearingExceptionReason(String errorCode, String errorMessage) {
        if (StringUtils.isBlank(errorCode)) {
            return KLClearingExceptionReasonEnum.UNKNOWN_ERROR.getValue();
        }
        switch (errorCode) {
            case AccountTipConstant.INSUFFICIENT_ACCOUNT_BALANCE:
            case CommonTipConstant.REQUEST_TIMEOUT:
                return KLClearingExceptionReasonEnum.ACCOUNTING_FAIL.getValue();
            default:
                return StringUtils.isBlank(errorMessage) ? errorCode : errorMessage;
        }
    }

    public void updateClearingTransFail(TransactionClearingContext context, BusinessException e) {
        try {
            KLClearingTrans clearingTrans = context.getClearingTrans();
            if (clearingTrans == null) {
                return;
            }
            if (TransactionStatusEnum.FAILED.getCode().equals(clearingTrans.getStatus())) {
                return;
            }
            context.getClearingTrans().setStatus(TransactionStatusEnum.FAILED.getCode());
            KLClearingTrans updateClearingTrans = new KLClearingTrans();
            updateClearingTrans.setStatus(TransactionStatusEnum.FAILED.getCode());
            updateClearingTrans.setUpdateTime(DateTimeUtils.getCurrentDateTime());
            int update = clearingTransMapper.update(updateClearingTrans,
                new LambdaQueryWrapper<KLClearingTrans>().eq(KLClearingTrans::getClearingId,
                    clearingTrans.getClearingId()).eq(KLClearingTrans::getCreateTime, clearingTrans.getCreateTime()));
            log.info("更新清分交易状态为失败记录数:{}, 清分Id: {}", update, clearingTrans.getClearingId());
        } catch (Exception ex) {
            log.error("更新清分交易失败, 清分Id: {}, 错误信息: {}, 异常: {}", context.getClearingId(), ex.getMessage(),
                ex);
        }
    }
}
