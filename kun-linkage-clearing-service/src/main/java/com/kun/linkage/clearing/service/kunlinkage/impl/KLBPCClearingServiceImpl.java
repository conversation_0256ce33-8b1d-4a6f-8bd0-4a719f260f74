package com.kun.linkage.clearing.service.kunlinkage.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.enums.BusinessActionEnum;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.account.facade.enums.BusinessTypeEnum;
import com.kun.linkage.auth.facade.constant.*;
import com.kun.linkage.clearing.dto.TransactionClearingContext;
import com.kun.linkage.clearing.enums.ClearIngTypeEnum;
import com.kun.linkage.clearing.enums.YesFlagEnum;
import com.kun.linkage.clearing.facade.constant.KunLinkageClearingResponseCodeEnum;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.clearing.service.fee.ClearingFeeCalculationService;
import com.kun.linkage.clearing.service.kunlinkage.KLAbstractClearingService;
import com.kun.linkage.clearing.service.kunlinkage.KLClearingService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonConstant;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * BPC清分服务实现类 负责处理BPC系统的清分交易，包括清分初始化、相关数据查询、构建清分交易等功能
 */
@Slf4j
@Service
public class KLBPCClearingServiceImpl extends KLAbstractClearingService implements KLClearingService {

    @Resource
    private ClearingFeeCalculationService clearingFeeCalculationService;

    @Resource
    private LarkAlarmUtil larkAlarmUtil;

    /**
     * 处理清分流程的主方法 包括初始化清分交易、判断交易是否成功、查询相关数据、构建清分交易等步骤
     *
     * @param context 交易清分上下文，包含清分所需的所有数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processClearing(TransactionClearingContext context) {
        try {
            // 查询相关数据
            queryRelatedData(context);
            context.setNeedToRecordExceptionClearingTrans(true);
            // 更新授权数据
            updateAuthFlowData(context);
            // 更新记账
            updateAccountingData(context);
            // 更新清分数据
            updateClearingTrans(context);

            // 异步处理清算手续费
            try {
                clearingFeeCalculationService.processTransactionFeeAsync(context);
                log.info("异步清算手续费处理已启动，清分ID：{}", context.getClearingId());
            } catch (Exception feeException) {
                // 手续费处理失败不影响主清算流程
                log.error("启动异步清算手续费处理失败，清分ID：{}, 错误信息：{}",
                    context.getClearingId(), feeException.getMessage(), feeException);
            }

            log.info("BPC清分处理成功，清分ID：{}, 商户号：{}, 处理器请求号：{}", context.getClearingTrans().getClearingId(),
                context.getPostTransRequestVO().getMerchantNo(), context.getPostTransRequestVO().getProcessorRequestId());
        } catch (BusinessException e) {
            log.error("BPC清分处理失败，错误码：{}, 错误信息：{}", e.getErrorCode(), e.getMessage());
            if (context.isNeedToRecordExceptionClearingTrans()){
                if (context.isExceptionClearingTransRecorded()){
                    context.getPostTransResponseVO().setCode(e.getErrorCode());
                    context.getPostTransResponseVO().setMessage(e.getMessage());
                    return;
                } else {
                    // 记录异常清分交易
                    super.recordClearingExceptionTrans(context, e.getErrorCode(), e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("BPC清分处理异常，错误信息：{}", e.getMessage(), e);
            if (context.isNeedToRecordExceptionClearingTrans()){
                if (context.isExceptionClearingTransRecorded()){
                    context.getPostTransResponseVO().setCode(KunLinkageClearingResponseCodeEnum.UNKNOWN_ERROR.getCode());
                    context.getPostTransResponseVO().setMessage(KunLinkageClearingResponseCodeEnum.UNKNOWN_ERROR.getCode());
                    return;
                } else {
                    // 记录异常清分交易
                    super.recordClearingExceptionTrans(context, KunLinkageClearingResponseCodeEnum.UNKNOWN_ERROR.getCode(), null);
                }
            }
            throw new RuntimeException(e);
        } finally {
            // 更新清分交易状态
            if (context.getClearingTrans() != null && !TransactionStatusEnum.SUCCESS.getCode().equals(context.getClearingTrans().getStatus())){
                super.updateClearingTransFail(context, null);
            }
        }
    }

    private void updateAccountingData(TransactionClearingContext context) {
        if (Arrays.asList(ClearIngTypeEnum.REFUND).contains(context.getClearIngTypeEnum())) {
            boolean accountingCustomerAccount = isAccountingFlagEnabled(context.getAuthFlowExt(), context.getOrganizationBasicInfo());
            if (accountingCustomerAccount) {
                // 补充记账
                BusinessActionEnum businessAction = getBusinessAction(context.getClearIngTypeEnum());
                AccountChangeBalanceReq req = new AccountChangeBalanceReq();
                req.setBusinessSystem(BusinessSystemEnum.KL.getValue());
                req.setBusinessOrganizationNo(context.getPostTransRequestVO().getMerchantNo());
                req.setAccountNo(context.getCustomerBasicAccount().getAccountNo());
                req.setBusinessType(BusinessTypeEnum.CLEARING.getValue());
                req.setBusinessAction(businessAction.getValue());
                req.setAccountingAction(getAccountingAction(context));

                String requestNo = context.getClearingId();// 使用清分ID作为记账请求号
                log.info("记账请求，商户号：{}, 交易ID：{}, 清分id: {}, 请求号: {}",
                    context.getPostTransRequestVO().getMerchantNo(),
                    Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId(), context.getClearingId());
                if (context.getAuthFlowExt() != null){
                    int row = authFlowExtService.updateCardholderAccountRequestNo(context.getAuthFlowExt().getAuthFlowId(), requestNo, context.getAuthFlowExt().getCreateTime());
                    if (row <= 0) {
                        log.error("更新授权流程扩展信息记账流水号失败，商户号：{}, 交易ID：{}, 清分id: {}, 请求号: {}",
                            context.getPostTransRequestVO().getMerchantNo(),
                            Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId(), requestNo);
                        throw new BusinessException(KunLinkageClearingResponseCodeEnum.UNKNOWN_ERROR.getCode());
                    } else {
                        log.info("更新授权流程扩展信息记账流水号成功，商户号：{}, 交易ID：{}, 清分id: {}, 请求号: {}",
                            context.getPostTransRequestVO().getMerchantNo(),
                            Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId(), requestNo);
                    }
                }
                req.setRequestNo(requestNo);
                req.setBusinessTransactionNo(context.getClearingTrans().getClearingId());
                req.setCurrencyCode(context.getPostTransRequestVO().getCardholderBillingCurrency());
                req.setAmount(context.getPostTransRequestVO().getCardholderMarkupBillingAmount());
                req.setRemark(req.getBusinessTransactionNo());
                Result<AccountChangeBalanceRes> result =
                    accountTransactionFacade.changeBalance(req);
                log.info("Account change balance result: {}", JSON.toJSONString(result));
                super.saveAccountLog(context, req, result);
                if (!result.isSuccess()) {
                    super.handleAccountingFailure(context, result);
                    log.error("BPC清分处理交易，记账失败，商户号：{}, 交易ID：{}, 清分id: {}, 错误码: {}, 错误信息: {}",
                        context.getPostTransRequestVO().getMerchantNo(),
                        Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId(),
                        result.getCode(), result.getMessage());
                    throw new BusinessException(KunLinkageClearingResponseCodeEnum.ACCOUNTING_FAIL.getCode());
                }
            } else {
                log.warn("BPC清分处理交易，未启用记账，商户号：{}, 交易ID：{}, 清分id: {}",
                    context.getPostTransRequestVO().getMerchantNo(),
                    Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId());
            }
            return;
        }
        boolean accountingCustomerAccount = isAccountingFlagEnabled(context.getAuthFlowExt(), context.getOrganizationBasicInfo());
        if (!accountingCustomerAccount) {
            log.warn("BPC清分处理交易，未启用记账，商户号：{}, 交易ID：{}, 清分id: {}",
                context.getPostTransRequestVO().getMerchantNo(),
                Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId());
            return;
        }
        BigDecimal markupBillingAmountOffset = context.getPostTransRequestVO().getCardholderMarkupBillingAmountOffset();
        int i = markupBillingAmountOffset.compareTo(BigDecimal.ZERO);
        if (i > 0) {
//            if (YesFlagEnum.YES.getValue().equals(context.getPostTransRequestVO().getReversalFlag()) && context.getOriginalClearingTrans() != null && context.getOriginalClearingTrans().getAuthFlowId() != null) {
//                // 如果是撤销交易且原始清分交易存在授权流程ID，则不需要补充记账
//                log.info("BPC清分处理交易，撤销交易，商户号：{}, 交易ID：{}, 清分id: {}, 不需要补充记账",
//                    context.getPostTransRequestVO().getMerchantNo(),
//                    Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId());
//                return;
//            }
            // 补充记账
            BusinessActionEnum businessAction = getBusinessAction(context.getClearIngTypeEnum());
            String requestNo = context.getClearingTrans().getClearingId();
            AccountChangeBalanceReq req = new AccountChangeBalanceReq();
            req.setBusinessSystem(BusinessSystemEnum.KL.getValue());
            req.setBusinessOrganizationNo(context.getPostTransRequestVO().getMerchantNo());
            req.setAccountNo(context.getCustomerBasicAccount().getAccountNo());
            req.setBusinessType(BusinessTypeEnum.CLEARING.getValue());
            req.setBusinessAction(businessAction.getValue());
            req.setAccountingAction(getAccountingAction(context));
            req.setRequestNo(requestNo);

            log.info("记账请求，商户号：{}, 交易ID：{}, 清分id: {}, 请求号: {}",
                context.getPostTransRequestVO().getMerchantNo(),
                Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId(), requestNo);
            int row = authFlowExtService.updateCardholderAccountRequestNo(
                Optional.ofNullable(context.getAuthFlowExt()).map(AuthFlowExt::getAuthFlowId).orElse(null), requestNo,
                Optional.ofNullable(context.getAuthFlowExt()).map(AuthFlowExt::getCreateTime).orElse(null));
            if (row <= 0) {
                log.warn("更新授权流程扩展信息记账流水号失败，商户号：{}, 交易ID：{}, 清分id: {}, 请求号: {}",
                    context.getPostTransRequestVO().getMerchantNo(),
                    Optional.ofNullable(context.getAuthFlow()).map(AuthFlow::getId).orElse(null), context.getClearingId(), requestNo);
            }
            req.setBusinessTransactionNo(context.getClearingTrans().getClearingId());
            req.setCurrencyCode(context.getPostTransRequestVO().getCardholderBillingCurrency());
            req.setAmount(context.getPostTransRequestVO().getCardholderMarkupBillingAmountOffset().abs());
            req.setRemark(req.getBusinessTransactionNo());
            Result<AccountChangeBalanceRes> result =
                accountTransactionFacade.changeBalance(req);
            log.info("Account change balance result: {}", JSON.toJSONString(result));
            super.saveAccountLog(context, req, result);

            //当清算时，扣除到卡片可用金额<0时，需要在lark里进行预警
            if(null != result && Result.isSuccess(result) && null != result.getData() ){
                AccountChangeBalanceRes data = result.getData();
                //最终可用余额
                BigDecimal finalAvailableAmount = data.getFinalAvailableAmount();
                if(finalAvailableAmount.compareTo(BigDecimal.ZERO) < 0){
                    //机构名称
                    String organizationName = context.getOrganizationBasicInfo().getOrganizationName();
                    String msg = String.format("[清算账户动账]商户:{%s},:{%s}，CardID:{%s}, 当前余额小于零:{%s}, 卡片余额:{%s}, 请求流水号:{%s}",
                            req.getBusinessOrganizationNo(),organizationName,context.getPostTransRequestVO().getGatewayCardId(),
                            finalAvailableAmount,requestNo);
                    larkAlarmUtil.sendTextAlarm(msg);
                }
            }

        } else if (i < 0) {
            // 只有退货需要冲账，其余的等自动释放

        } else {
            // 无需处理
        }
    }

    private BusinessActionEnum getBusinessAction(ClearIngTypeEnum clearIngType) {
        switch (clearIngType) {
            case CONSUMPTION:
                return BusinessActionEnum.SALES;
            case WITHDRAWAL:
            case TRANSFER_OUT:
                return BusinessActionEnum.AFT;
            case REFUND:
                return BusinessActionEnum.REFUND;
            case TRANSFER_IN:
                return BusinessActionEnum.OCT;
            case CONSUMPTION_VOID:
            case REFUND_REVERSAL:
            case WITHDRAWAL_REVERSAL:
            case TRANSFER_OUT_REVERSAL:
            case TRANSFER_IN_REVERSAL:
                return BusinessActionEnum.VOID;
            default:
                return null;
        }
    }

    private boolean isAccountingFlagEnabled(AuthFlowExt authFlowExt, OrganizationBasicInfo organizationBasicInfo) {
        if (authFlowExt == null) {
            return organizationBasicInfo != null && organizationBasicInfo.getCheckCustomerAccountFlag() != null && organizationBasicInfo.getCheckCustomerAccountFlag() == 1;
        } else {
            return authFlowExt.getCardholderAccountFlag() != null && authFlowExt.getCardholderAccountFlag() == 1;
        }
    }

    private void updateAuthFlowData(TransactionClearingContext context) {
        AuthFlow authFlow = context.getAuthFlow();
        if (authFlow != null) {
            AuthFlow updatedAuthFlow = new AuthFlow();
            if (!ClearFlagEnum.CLEARED.getValue().equals(authFlow.getClearFlag())) {
                updatedAuthFlow.setClearFlag(ClearFlagEnum.CLEARED.getValue());
                updatedAuthFlow.setClearAccountingDate(DateUtils.formatDate(DateTimeUtils.getCurrentDateTime(), CommonConstant.YYYYMMDD));
                authFlow.setClearFlag(updatedAuthFlow.getClearFlag());
                authFlow.setClearAccountingDate(updatedAuthFlow.getClearAccountingDate());
            }
            updatedAuthFlow.setUpdateTime(DateTimeUtils.getCurrentDateTime());
            try {
                authFlowService.updateClearingData(authFlow.getId(), authFlow.getCreateTime(),
                    context.getPostTransRequestVO().getClearAmount(),
                    context.getPostTransRequestVO().getCardholderBillingAmount(),
                    context.getPostTransRequestVO().getCardholderMarkupBillingAmount(), updatedAuthFlow.getClearFlag(),
                    AuthReleaseFlagEnum.NONE.getValue(), AuthReleaseFlagEnum.LOCKED.getValue(),
                    DateTimeUtils.getCurrentDateTime(), updatedAuthFlow.getClearAccountingDate(), YesFlagEnum.YES.getNumValue().toString().equals(context.getPostTransRequestVO().getReversalFlag()));
            } catch (Exception e) {
                context.setNeedToRecordExceptionClearingTrans(true);
                context.setExceptionClearingTransRecorded(false);
                throw e;
            }
        } else {
            // 如果没有授权流程，直接计算持卡人markup费用(KCard算好送过来)
//            MarkupFeeCalculateDto chMarkupTemplateFee =
//                chMarkupService.getChMarkupTemplateFee(context.getPostTransRequestVO().getProcessor(),
//                    context.getPostTransRequestVO().getMerchantNo(), null,
//                    context.getOrganizationCustomerCardInfo().getCardProductCode(),
//                    context.getPostTransRequestVO().getTransCurrency(),
//                    context.getPostTransRequestVO().getCardholderBillingAmount(),
//                    context.getPostTransRequestVO().getCardholderBillingCurrency());
//            context.getClearingTrans().setMarkupRate(chMarkupTemplateFee.getMarkupFeeRate());
//            context.getClearingTrans()
//                .setCardholderMarkupBillingAmount(chMarkupTemplateFee.getCardholderBillingAmountWithMarkup());
//            context.getClearingTrans().setMarkupAmount(context.getClearingTrans().getCardholderMarkupBillingAmount()
//                .subtract(context.getClearingTrans().getCardholderBillingAmount()));
//            context.getPostTransRequestVO().setMarkupRate(chMarkupTemplateFee.getMarkupFeeRate());
//            context.getPostTransRequestVO()
//                .setCardholderMarkupBillingAmount(chMarkupTemplateFee.getCardholderBillingAmountWithMarkup());
//            context.getPostTransRequestVO().setMarkupAmount(
//                context.getPostTransRequestVO().getCardholderMarkupBillingAmount()
//                    .subtract(context.getPostTransRequestVO().getCardholderBillingAmount()));
            return;
        }
        // 更新第一笔交易剩余授权金额、剩余账单金额(不含markup)、剩余账单金额(含markup)、清分金额
        if (context.getFirstAuthFlow() != null && !context.getFirstAuthFlow().getId().equals(context.getAuthFlow().getId())) {
            AuthFlow firstAuthFlow = context.getFirstAuthFlow();
            AuthFlow updatedAuthFlow = new AuthFlow();
            if (!ClearFlagEnum.CLEARED.getValue().equals(context.getFirstAuthFlow().getClearFlag())) {
                updatedAuthFlow.setClearFlag(ClearFlagEnum.CLEARED.getValue());
                updatedAuthFlow.setClearAccountingDate(DateUtils.formatDate(DateTimeUtils.getCurrentDateTime(), DateUtils.DAY_PATTERN));
                firstAuthFlow.setClearFlag(updatedAuthFlow.getClearFlag());
                firstAuthFlow.setClearAccountingDate(updatedAuthFlow.getClearAccountingDate());
            }
            updatedAuthFlow.setUpdateTime(DateTimeUtils.getCurrentDateTime());
            authFlowService.updateClearingData(firstAuthFlow.getId(), firstAuthFlow.getCreateTime(),
                context.getPostTransRequestVO().getClearAmount(),
                context.getPostTransRequestVO().getCardholderBillingAmount(),
                context.getPostTransRequestVO().getCardholderMarkupBillingAmount(), updatedAuthFlow.getClearFlag(),
                AuthReleaseFlagEnum.NONE.getValue(), AuthReleaseFlagEnum.LOCKED.getValue(),
                DateTimeUtils.getCurrentDateTime(), updatedAuthFlow.getClearAccountingDate(), YesFlagEnum.YES.getNumValue().toString().equals(context.getPostTransRequestVO().getReversalFlag()));
        }
    }

    /**
     * 查询清分相关的数据 包括授权流程、组织信息、客户卡信息和客户账户信息
     *
     * @param context 交易清分上下文
     */
    private void queryRelatedData(TransactionClearingContext context) {
        PostTransRequestVO request = context.getPostTransRequestVO();
        processAuthFlows(context, request);
    }

    /**
     * 处理授权流程数据 查询并设置相关的授权流程信息到上下文中
     *
     * @param context 交易清分上下文
     * @param request 交易请求对象
     */
    private void processAuthFlows(TransactionClearingContext context, PostTransRequestVO request) {
       if (context.getAuthFlow() == null){
           log.warn("BPC清分处理交易，未查询到授权流程，商户号：{}, 处理器请求号：{}", request.getMerchantNo(), Optional.ofNullable(request.getProcessorRequestId()).orElse(null));
           return;
       }
       if (Arrays.asList(TransactionTypeEnum.REFUND.getCode(), TransactionTypeEnum.TRANSFER_IN.getCode())
            .contains(context.getAuthFlow().getTransType())) {
            // 退货和转入交易不需要查询最开始的授权交易
            return;
        }
        if (context.getAuthFlow().getOriginalId() == null){
            context.setFirstAuthFlow(context.getAuthFlow());
            log.warn("BPC清分处理交易，授权流程没有原始ID，商户号：{}, 处理器请求号：{}", request.getMerchantNo(), Optional.ofNullable(request.getProcessorRequestId()).orElse(null));
            return;
        }
        if (context.getAuthFlow().getOriginalId() !=null){
            // 查询第一笔授权
            List<AuthFlow> authFlows = authFlowService.getFirstAuthFlowsByAuthFlow(context.getAuthFlow());
            if (authFlows == null || authFlows.isEmpty()) {
                context.setFirstAuthFlow(context.getAuthFlow());
                log.warn("BPC清分处理交易，未查询到第一笔授权流程，商户号：{}, 处理器请求号：{}", request.getMerchantNo(), Optional.ofNullable(request.getProcessorRequestId()).orElse(null));
                return;
            }
            // 设置第一个授权流程
            context.setFirstAuthFlow(authFlows.get(0));
            // 如果存在原始授权ID，查找并设置原始授权流程
            Optional.ofNullable(context.getAuthFlow().getOriginalId()).flatMap(
                    originalId -> authFlows.stream().filter(flow -> StringUtils.equals(flow.getId(), originalId)).findFirst())
                .ifPresent(context::setOriginalAuthFlow);
        } else {
            // 如果没有原始ID，直接使用当前授权流程作为第一个授权流程
            context.setFirstAuthFlow(context.getAuthFlow());
        }
        context.setFirstAuthFlowExt( authFlowExtService.getAuthFlowExtByAuthFlowId(context.getFirstAuthFlow().getId(),
            context.getFirstAuthFlow().getCreateTime()));

        // 如果存在原始授权流程，查询并设置原始授权流程扩展信息
        if (context.getOriginalAuthFlow() != null){
            context.setOriginalAuthFlowExt(
                authFlowExtService.getAuthFlowExtByAuthFlowId(context.getOriginalAuthFlow().getId(),
                    context.getOriginalAuthFlow().getCreateTime()));
        }

    }

    /**
     * 构建清分交易 根据上下文中的数据，构建清分交易对象
     *
     * @param context 交易清分上下文
     */
    private void updateClearingTrans(TransactionClearingContext context) {
        KLClearingTrans updateClearingTrans = new KLClearingTrans();
        if (context.getAuthFlow() != null) {
            updateClearingTrans.setAuthFlowId(context.getAuthFlow().getId());
            context.getClearingTrans().setAuthFlowId(updateClearingTrans.getAuthFlowId());
            updateClearingTrans.setProcessorRequestId(context.getAuthFlow().getProcessorRequestId());
            context.getClearingTrans().setProcessorRequestId(updateClearingTrans.getProcessorRequestId());
            if (context.getOriginalAuthFlow() != null && !context.getAuthFlow().getId().equals(context.getOriginalAuthFlow().getId())) {
                updateClearingTrans.setOriginalAuthFlowId(context.getOriginalAuthFlow().getId());
                context.getClearingTrans().setOriginalAuthFlowId(updateClearingTrans.getOriginalAuthFlowId());
                updateClearingTrans.setOriginalProcessorRequestId(
                    context.getOriginalAuthFlow().getProcessorRequestId());
                context.getClearingTrans().setOriginalProcessorRequestId(updateClearingTrans.getOriginalProcessorRequestId());
            }
            updateClearingTrans.setTransDate(
                DateUtils.formatDate(context.getAuthFlow().getCreateTime(), CommonConstant.YYYYMMDD));
            updateClearingTrans.setTransTime(context.getAuthFlow().getCreateTime());
        } else {
            updateClearingTrans.setTransTime(DateTimeUtils.getCurrentDateTime());
            context.getClearingTrans().setTransTime(updateClearingTrans.getTransTime());
            updateClearingTrans.setTransDate(
                DateUtils.formatDate(updateClearingTrans.getTransTime(), CommonConstant.YYYYMMDD));
            context.getClearingTrans().setTransDate(updateClearingTrans.getTransDate());
        }
        updateClearingTrans.setClearingId(context.getClearingTrans().getClearingId());
        updateClearingTrans.setStatus(TransactionStatusEnum.SUCCESS.getCode());
        context.getClearingTrans().setStatus(TransactionStatusEnum.SUCCESS.getCode());
        updateClearingTrans.setUpdateTime(DateTimeUtils.getCurrentDateTime());
        context.getClearingTrans().setUpdateTime(updateClearingTrans.getUpdateTime());
        updateClearingTrans.setClearTime(updateClearingTrans.getUpdateTime());
        context.getClearingTrans().setClearTime(updateClearingTrans.getClearTime());
        clearingTransMapper.update(updateClearingTrans,
            new LambdaQueryWrapper<KLClearingTrans>().eq(KLClearingTrans::getClearingId,
                    context.getClearingTrans().getClearingId())
                .eq(KLClearingTrans::getCreateTime, context.getClearingTrans().getCreateTime()));
    }

}
