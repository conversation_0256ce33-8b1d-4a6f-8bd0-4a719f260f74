package com.kun.linkage.clearing.service.bpcFile.impl;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.clearing.config.BpcFileconfig;
import com.kun.linkage.clearing.constant.MqTopicConstant;
import com.kun.linkage.clearing.enums.PeripheralFileStatusEnum;
import com.kun.linkage.clearing.enums.PeripheralFileTypeEnum;
import com.kun.linkage.clearing.service.boss.VisaFileService;
import com.kun.linkage.clearing.service.bpcFile.IPeripheralFileLogService;
import com.kun.linkage.clearing.service.bpcFile.IVisaBase05DataService;
import com.kun.linkage.clearing.service.bpcFile.IVisaBase10DataService;
import com.kun.linkage.clearing.service.bpcFile.IVisaBase33DataService;
import com.kun.linkage.clearing.service.visa.VisaBaseFileReadService;
import com.kun.linkage.clearing.vo.req.BpcBaseFileReq;
import com.kun.linkage.common.db.entity.VisaBase05Data;
import com.kun.linkage.common.db.entity.VisaBase10Data;
import com.kun.linkage.common.db.entity.VisaBase33Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * visa base ii 文件解析
 */
@Slf4j
@Service
public class VisaBaseFileAnalysisServiceImpl {

    @Resource
    private BpcFileconfig bpcFileconfig;
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private IPeripheralFileLogService peripheralFileLogService;
    @Resource
    private VisaBaseFileReadService visaBaseFileReadService;
    @Resource
    private IVisaBase05DataService visaBase05DataService;
    @Resource
    private IVisaBase10DataService visaBase10DataService;
    @Resource
    private IVisaBase33DataService visaBase33DataService;
    @Resource
    private VisaFileService visaFileService;

    /**
     * 读取文件
     * @param downloadDate 下载的日期 yyyymmdd
     */
    public Boolean readFile(String downloadDate) throws IOException {


        //删掉15天前的visabseII 文件
        visaBase05DataService.deleteByCreateTime(15);

        // 创建 S3 客户端 (支持路径风格)
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(bpcFileconfig.getAccessKeyId(), bpcFileconfig.getSecretAccessKey());
        AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(bpcFileconfig.getUrl(), bpcFileconfig.getRegion()))
                .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                .withPathStyleAccessEnabled(true) // 启用路径风格访问
                .build();

        PeripheralFileTypeEnum bpcBaseFile = PeripheralFileTypeEnum.BPC_BASE_File;
        //查询当天已经跑过的文件
        List<String> existingFileNameList = peripheralFileLogService.selectPeripheralFileLogByParam(downloadDate, bpcBaseFile);
        //获取当天的文件,排除已经存在的
        List<String> bpcFilePathList = this.getBpcFileName(s3Client,downloadDate,existingFileNameList);
        if(null ==bpcFilePathList ||bpcFilePathList.size() <= 0){
            log.info("当天没有pbc文件,downloadDate:{}", downloadDate);
            return true;
        }

        Date date = new Date();

        int totalSize =bpcFilePathList.size();
        int successNum = 0;
        int failNum = 0;

        for (String filePath :bpcFilePathList) {
            List<VisaBase05Data> visaTransaction05DataInfoList = new ArrayList<>();
            List<VisaBase10Data> visaBase10DataList = new ArrayList<>();
            List<VisaBase33Data> visaBase33DataList = new ArrayList<>();
            String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
            //把BPC S3服务器上文件上传到自己S3服务器
            String localFilePath = filePath;
            try {
                localFilePath = visaFileService.downloadFromSourceS3AndUploadToLocalS3(s3Client,bpcFileconfig.getBucketName(), filePath,"VISABaseII");
            } catch (Exception e) {
                log.error("从visaS3服务器下载VisaBaseII文件到本地S3服务器失败;文件名称fileName:{}", fileName, e);
            }
            Long peripheralFileId = peripheralFileLogService.createAndSavePeripheralFileLog(downloadDate, bpcBaseFile, localFilePath, fileName, filePath, date);
            Integer errorRows = null;

            //try (BufferedReader br = new BufferedReader(new FileReader("/Users/<USER>/Downloads/EPIN_BPCPS_714442_20250623_194400_TEST.TXT"))) {
            S3Object parseS3Object = s3Client.getObject(new GetObjectRequest(bpcFileconfig.getBucketName(), filePath));
            try (BufferedReader br = new BufferedReader(new InputStreamReader(parseS3Object.getObjectContent()))) {
                errorRows = visaBaseFileReadService.parseAndFormat(br, visaTransaction05DataInfoList, visaBase10DataList, visaBase33DataList, fileName);
                if(errorRows == null){
                    //更新状态
                    peripheralFileLogService.updatePeripheralFileStatusById(peripheralFileId, PeripheralFileStatusEnum.SUCCESS,null);
                    successNum = successNum + 1;
                    //成功之后，拿到对应的文件发送mq对账
                    BpcBaseFileReq voReq = new BpcBaseFileReq(fileName);
                    log.info("bpc visa base 文件mq:{}", voReq);
                    SendResult sendResult05 = rocketMqService.syncSend(MqTopicConstant.BPC_VISA_BASE_FILE_05_TOPIC, voReq);
                    SendResult sendResult33 = rocketMqService.syncSend(MqTopicConstant.BPC_VISA_BASE_FILE_33_TOPIC, voReq);
                }else {
                    failNum = failNum + 1;
                    log.error("文件读取失败;filePath:{},errorRows:{}", filePath,errorRows);
                    handleBpcFailFile(fileName, peripheralFileId, errorRows);
                }

            }catch (Exception e) {
                failNum = failNum + 1;
                log.error("文件读取失败;filePath:{},errorRows:{}", filePath,errorRows,e);
                handleBpcFailFile(fileName, peripheralFileId, errorRows);
            }finally {
                // 关闭第二次的S3Object
                if (parseS3Object != null) {
                    parseS3Object.close();
                }
            }
        }
        log.info("=====================VisaBaseFileAnalysisServiceImpl Read Bpc BaseII files finished! totalSize:{},successNum:{},failNum:{}",totalSize,successNum,failNum);
        if(totalSize == failNum){
            log.error("VisaBaseFileAnalysisServiceImpl Read Read Bpc BaseII failed totalSize:{},failNum:{}",totalSize,failNum);
            return false;
        }
        return true;
    }

    /**
     * 处理bpc 失败文件
     * @param fileName
     */
    private void handleBpcFailFile(String fileName,Long peripheralFileId,Integer errorRows){
        //更新文件读取记录
        peripheralFileLogService.updatePeripheralFileStatusById(peripheralFileId, PeripheralFileStatusEnum.FAILURE,errorRows.toString());
        //删除读取的数据
        visaBase05DataService.deleteByFileName(fileName);
        visaBase10DataService.deleteByFileName(fileName);
        visaBase33DataService.deleteByFileName(fileName);
    }

    /**
     * 获取bpc visa base ii 文件,排除存在的文件
     * @param s3Client
     * @param downloadDate
     * @param existingFileNameList
     * @return
     */
    private List<String> getBpcFileName(AmazonS3 s3Client,String downloadDate,List<String> existingFileNameList){
        List<String> fileNameList = new ArrayList<>();

        // 列出并遍历 bucket 下所有文件
        ListObjectsV2Request listReq = new ListObjectsV2Request()
                .withBucketName(bpcFileconfig.getBucketName())
                .withPrefix(bpcFileconfig.getPrefix());

        ListObjectsV2Result listRes = s3Client.listObjectsV2(listReq);
        boolean checkFileExists = false;
        for (S3ObjectSummary summary : listRes.getObjectSummaries()) {
            String filePath = summary.getKey();
            log.info("FilePath: {}", filePath);
            String serviceFileName = filePath.substring(filePath.lastIndexOf('/') + 1);
            log.info("FileName: {}", serviceFileName);
            //EPIN_BPCPS_714442_20250524_164406_TEST.EPIN.TXT
            if(serviceFileName.contains(downloadDate)){
                if(!existingFileNameList.contains(serviceFileName) ){
                    fileNameList.add(filePath);
                    log.info("需要处理的文件名:{}",serviceFileName);
                }else {
                    log.info("当天已经跑过的文件名:{}",serviceFileName);
                }
            }
        }
        return fileNameList;
    }


}
