package com.kun.linkage.clearing.listener;

import com.kun.linkage.clearing.constant.MqGroupConstant;
import com.kun.linkage.clearing.constant.MqTopicConstant;
import com.kun.linkage.clearing.service.bpcFile.impl.BpcVisaBaseFileHandleServiceImpl;
import com.kun.linkage.clearing.vo.req.BpcBaseFileReq;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = MqGroupConstant.BPC_VISA_BASE_FILE_33_GROUP, topic = MqTopicConstant.BPC_VISA_BASE_FILE_33_TOPIC,messageModel = MessageModel.CLUSTERING)
public class BpcBaseFile33Listener implements RocketMQListener<BpcBaseFileReq> {

    @Resource
    private BpcVisaBaseFileHandleServiceImpl bpcVisaBaseFileHandleService;
    @Resource
    private RedissonLockUtil redissonLockUtil;

    @Override
    public void onMessage(BpcBaseFileReq bpcBaseFileReq) {
        log.info("mq接收BPCBaseII 33文件请求:{}", bpcBaseFileReq);
        String fileName = bpcBaseFileReq.getFileName();
        String lockKey = "kun:linkage:clearing:BPC_BASE_FILE_33:" + fileName;
        RLock lock = null;
        try {
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.error("mq已经收到BPC visa base ii 33文件处理请求，不用重复申请:{}", bpcBaseFileReq);
            }else {
                bpcVisaBaseFileHandleService.hand33FileDate(bpcBaseFileReq);
            }
        }catch (Exception e){
            log.error("BPC visa base ii 33文件;处理失败:{}, 异常信息:", bpcBaseFileReq, e);
        }finally {
            redissonLockUtil.unlock(lock);
        }

    }
}
