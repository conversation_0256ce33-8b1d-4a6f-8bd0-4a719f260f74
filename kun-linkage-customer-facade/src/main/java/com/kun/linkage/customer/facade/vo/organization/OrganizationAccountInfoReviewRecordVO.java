package com.kun.linkage.customer.facade.vo.organization;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.kun.linkage.common.base.utils.PlainBigDecimalSerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class OrganizationAccountInfoReviewRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 审核id
     */
    private String reviewId;

    /**
     * 操作类型:Add,Modify
     */
    private String operatorType;

    /**
     * 机构账户信息表id,修改时有值
     */
    private String organizationAccountInfoId;

    /**
     * 机构号
     */
    private String organizationNo;

    /**
     * 机构号名称
     */
    private String organizationName;

    /**
     * 账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户
     */
    private String accountType;

    /**
     * 币种字母码
     */
    private String currencyCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 最小余额
     */
    @JsonSerialize(using = PlainBigDecimalSerializer.class)
    private BigDecimal minimumAmount;

    /**
     * 所属方
     */
    private String network;

    /**
     * 账户唯一标识
     */
    private String accountNo;

    /**
     * 审核状态
     */
    private String reviewStatus;

    /**
     * 审核备注
     */
    private String reviewReason;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 提交人id
     */
    private String submitUserId;

    /**
     * 提交人名称
     */
    private String submitUserName;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核人id
     */
    private String reviewUserId;

    /**
     * 审核人名称
     */
    private String reviewUserName;

    public String getReviewId() {
        return reviewId;
    }

    public void setReviewId(String reviewId) {
        this.reviewId = reviewId;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public String getOrganizationAccountInfoId() {
        return organizationAccountInfoId;
    }

    public void setOrganizationAccountInfoId(String organizationAccountInfoId) {
        this.organizationAccountInfoId = organizationAccountInfoId;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }

    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }

    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }

    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }

    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }

    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }

    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }
}
