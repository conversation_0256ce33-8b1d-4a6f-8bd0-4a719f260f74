package com.kun.linkage.customer.facade.enums;

public enum KycCaseTypeEnum {
    PRE_CARD_APPLICATION("PRE_CARD_APPLICATION", "申卡前置报送"),
    KYC_REPORT("KYC_REPORT", "KYC报送"),
    KYC_UPGRADE("UPGRADE_KYC", "KYC升级"),
    INFORMATION_MODIFICATION("MODIFY_INFO", "资料修改");

    private final String value;
    private final String label;
    private final String dictType = "KYC_CASE_TYPE";


    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    KycCaseTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static KycCaseTypeEnum fromCode(String value) {
        for (KycCaseTypeEnum type : values()) {
            if (type.getValue().equalsIgnoreCase(value))
                return type;
        }
        return null;
    }
}

