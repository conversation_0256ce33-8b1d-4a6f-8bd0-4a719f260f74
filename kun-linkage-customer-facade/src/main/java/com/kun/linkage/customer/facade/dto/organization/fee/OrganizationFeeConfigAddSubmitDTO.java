package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 机构费率配置率信息新增提交DTO
 */
public class OrganizationFeeConfigAddSubmitDTO extends OrganizationFeeConfigBean {
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    /**
     * 卡产品编码
     */
    @Schema(description = "卡产品编码")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String cardProductCode;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
}
