package com.kun.linkage.customer.facade.api.bean.req;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.customer.facade.api.bean.ApiBaseBean;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

public class QueryBalanceByCardIdReq extends ApiBaseBean {

    @Schema(description = "cardId")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String cardId;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
}
