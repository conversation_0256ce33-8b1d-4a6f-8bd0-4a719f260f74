package com.kun.linkage.customer.facade.vo;

import java.io.Serializable;

public class CustomerKycLevel1RecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private String customerId;

    /**
     * 机构号
     */
    private String organizationNo;

    /**
     * 案件号
     */
    private String caseNo;

    /**
     * 姓
     */
    private String lastName;

    /**
     * 中间名
     */
    private String middleName;

    /**
     * 名
     */
    private String firstName;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 加密,证件号
     */
    private String idNo;

    /**
     * 脱敏的证件号
     */
    private String maskedIdNo;

    /**
     * 出生日期;yyyy/mm/dd
     */
    private String birthDate;

    /**
     * 性别;1:男;2:女
     */
    private Integer gender;

    /**
     * 证件签发日
     */
    private String idIssueDate;

    /**
     * 证件有效期
     */
    private String idExpiryDate;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 国家代码,3位字母
     */
    private String countryCode;

    /**
     * 国家地区代码,3位数字
     */
    private String countryNo;

    /**
     * 证件正面地址
     */
    private String idCardFrontImage;

    /**
     * 人脸图片地址
     */
    private String facePhotoImage;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机区号
     */
    private String phoneArea;

    /**
     * 手机号码
     */
    private String mobileNo;

    /**
     * 居住地国家/地区;3位字母
     */
    private String residenceCountryCode;

    /**
     * 居住地州/省
     */
    private String residenceStateProvince;

    /**
     * 居住地城市
     */
    private String residenceCity;

    /**
     * 居住地详细地址
     */
    private String residenceAddressDetail;

    /**
     * 邮编
     */
    private String postalCode;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getMaskedIdNo() {
        return maskedIdNo;
    }

    public void setMaskedIdNo(String maskedIdNo) {
        this.maskedIdNo = maskedIdNo;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getIdIssueDate() {
        return idIssueDate;
    }

    public void setIdIssueDate(String idIssueDate) {
        this.idIssueDate = idIssueDate;
    }

    public String getIdExpiryDate() {
        return idExpiryDate;
    }

    public void setIdExpiryDate(String idExpiryDate) {
        this.idExpiryDate = idExpiryDate;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryNo() {
        return countryNo;
    }

    public void setCountryNo(String countryNo) {
        this.countryNo = countryNo;
    }

    public String getIdCardFrontImage() {
        return idCardFrontImage;
    }

    public void setIdCardFrontImage(String idCardFrontImage) {
        this.idCardFrontImage = idCardFrontImage;
    }

    public String getFacePhotoImage() {
        return facePhotoImage;
    }

    public void setFacePhotoImage(String facePhotoImage) {
        this.facePhotoImage = facePhotoImage;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoneArea() {
        return phoneArea;
    }

    public void setPhoneArea(String phoneArea) {
        this.phoneArea = phoneArea;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getResidenceCountryCode() {
        return residenceCountryCode;
    }

    public void setResidenceCountryCode(String residenceCountryCode) {
        this.residenceCountryCode = residenceCountryCode;
    }

    public String getResidenceStateProvince() {
        return residenceStateProvince;
    }

    public void setResidenceStateProvince(String residenceStateProvince) {
        this.residenceStateProvince = residenceStateProvince;
    }

    public String getResidenceCity() {
        return residenceCity;
    }

    public void setResidenceCity(String residenceCity) {
        this.residenceCity = residenceCity;
    }

    public String getResidenceAddressDetail() {
        return residenceAddressDetail;
    }

    public void setResidenceAddressDetail(String residenceAddressDetail) {
        this.residenceAddressDetail = residenceAddressDetail;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
}
