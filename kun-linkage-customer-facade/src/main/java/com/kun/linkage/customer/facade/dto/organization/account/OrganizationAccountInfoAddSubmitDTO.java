package com.kun.linkage.customer.facade.dto.organization.account;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.customer.facade.enums.BusinessAccountTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 机构账户信息提交DTO
 */
public class OrganizationAccountInfoAddSubmitDTO extends OrganizationAccountInfoBean {
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    /**
     * 业务账户类型
     */
    @Schema(description = "业务账户类型")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = BusinessAccountTypeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String accountType;

    /**
     * 币种字母码
     */
    @Schema(description = "币种字母码")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String currencyCode;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
}
