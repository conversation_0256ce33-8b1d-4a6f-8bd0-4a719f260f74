package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 持卡人费率信息新增提交DTO
 */
public class CardholderFeeAddSubmitDTO extends CardholderFeeBean {
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    /**
     * 卡产品编码
     */
    @Schema(description = "卡产品编码")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String cardProductCode;

    /*
    * 持卡人费率明细信息集合
     */
    @Valid
    @Schema(description = "持卡人费率明细信息集合")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Size(min = 4, max = 4, message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private List<CardholderFeeDetailAddSubmitDTO> cardholderFeeDetailList;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }

    public List<CardholderFeeDetailAddSubmitDTO> getCardholderFeeDetailList() {
        return cardholderFeeDetailList;
    }

    public void setCardholderFeeDetailList(List<CardholderFeeDetailAddSubmitDTO> cardholderFeeDetailList) {
        this.cardholderFeeDetailList = cardholderFeeDetailList;
    }
}
