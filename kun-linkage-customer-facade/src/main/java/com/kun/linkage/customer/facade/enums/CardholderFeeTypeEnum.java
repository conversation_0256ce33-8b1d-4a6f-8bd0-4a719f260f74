package com.kun.linkage.customer.facade.enums;

public enum CardholderFeeTypeEnum {
    /**
     * 充值FX Markup
     */
    RECHARGE_FX_MARKUP_FEE("01", "充值FX Markup"),
    /**
     * 充值承兑费
     */
    RECHARGE_ACCEPTANCE_FEE("02", "充值承兑费"),
    /**
     * 小额交易手续费
     */
    SMALL_TRANSACTION_FEE("03", "小额交易手续费"),
    /**
     * 交易手续费
     */
    TRANSACTION_FEE("04", "交易手续费"),
//    /**
//     * 交易FX Markup
//     */
//    TRANSACTION_FX_MARKUP_FEE("05", "交易FX Markup"),
//    /**
//     * 卡片年费
//     */
//    CARD_ANNUAL_FEE("06", "卡片年费"),
//    /**
//     * 实体卡制卡费
//     */
//    PHYSICAL_CARD_PRODUCTION_FEE("07", "实体卡制卡费"),
    ;

    private final String value;
    private final String desc;
    private final String dictType = "KL_CARDHOLDER_FEE_TYPE";

    CardholderFeeTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CardholderFeeTypeEnum getEnumByValue(String value) {
        for (CardholderFeeTypeEnum cardholderFeeTypeEnum : CardholderFeeTypeEnum.values()) {
            if (cardholderFeeTypeEnum.getValue().equals(value)) {
                return cardholderFeeTypeEnum;
            }
        }
        return null;
    }
}
