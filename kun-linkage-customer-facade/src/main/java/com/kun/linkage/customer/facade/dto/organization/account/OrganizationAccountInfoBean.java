package com.kun.linkage.customer.facade.dto.organization.account;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机构账户信息bean
 */
public class OrganizationAccountInfoBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 状态
     */
    @Schema(description = "状态;Valid:有效,Invalid:无效")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = ValidStatusEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String status;

    /**
     * 最小余额
     */
    @Schema(description = "最小余额")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private BigDecimal minimumAmount;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getMinimumAmount() {
        return minimumAmount;
    }

    public void setMinimumAmount(BigDecimal minimumAmount) {
        this.minimumAmount = minimumAmount;
    }
}
