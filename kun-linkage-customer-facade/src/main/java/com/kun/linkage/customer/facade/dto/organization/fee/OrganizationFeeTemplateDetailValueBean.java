package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机构费用模版明细值bean
 */
public class OrganizationFeeTemplateDetailValueBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 金额区间:最小金额
     */
    @Schema(description = "金额区间:最小金额")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal minAmount;

    /**
     * 金额区间:最大金额
     */
    @Schema(description = "金额区间:最大金额")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal maxAmount;

    /**
     * 比例
     */
    @Schema(description = "比例(前端展示需乘以100,送后端需除以100)")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.0000", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @DecimalMax(value = "1.0000", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal proportionRate;

    /**
     * 比例的保底金额
     */
    @Schema(description = "比例的保底金额")
    private BigDecimal proportionMinAmount;

    /**
     * 比例的封顶金额
     */
    @Schema(description = "比例的封顶金额")
    private BigDecimal proportionMaxAmount;

    /**
     * 固定值
     */
    @Schema(description = "固定值")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DecimalMin(value = "0.00", message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private BigDecimal fixedAmount;

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public BigDecimal getProportionRate() {
        return proportionRate;
    }

    public void setProportionRate(BigDecimal proportionRate) {
        this.proportionRate = proportionRate;
    }

    public BigDecimal getProportionMinAmount() {
        return proportionMinAmount;
    }

    public void setProportionMinAmount(BigDecimal proportionMinAmount) {
        this.proportionMinAmount = proportionMinAmount;
    }

    public BigDecimal getProportionMaxAmount() {
        return proportionMaxAmount;
    }

    public void setProportionMaxAmount(BigDecimal proportionMaxAmount) {
        this.proportionMaxAmount = proportionMaxAmount;
    }

    public BigDecimal getFixedAmount() {
        return fixedAmount;
    }

    public void setFixedAmount(BigDecimal fixedAmount) {
        this.fixedAmount = fixedAmount;
    }
}
