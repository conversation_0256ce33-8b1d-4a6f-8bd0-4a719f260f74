package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageParam;
import com.kun.linkage.customer.facade.enums.OrganizationFeeCollectionStatusEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 分页查询机构手续费明细参数
 */
@Schema(name = "OrganizationFeeDetailPageQueryReq", description = "机构手续费明细分页查询参数")
public class OrganizationFeeDetailPageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @Schema(description = "机构号", example = "12345")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String organizationNo;
    /**
     * 费用开始日期
     */
    @Schema(description = "费用开始日期", example = "2025-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private LocalDate feeDateStart;
    /**
     * 费用结束日期
     */
    @Schema(description = "费用结束日期", example = "2025-12-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private LocalDate feeDateEnd;
    /**
     * 手续费类型
     */
    @Schema(description = "手续费类型:字典:KL_ORGANIZATION_FEE_TYPE")
    @EnumValue(enumClass = OrganizationFeeTypeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String feeType;
    /**
     * 关联交易id
     */
    @Schema(description = "关联交易id", example = "TXN123456")
    private String relatedTransactionId;
    /**
     * 费用收取状态
     */
    @Schema(description = "费用收取状态", example = "0")
    @EnumValue(enumClass = OrganizationFeeCollectionStatusEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String feeCollectionStatus;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public LocalDate getFeeDateStart() {
        return feeDateStart;
    }

    public void setFeeDateStart(LocalDate feeDateStart) {
        this.feeDateStart = feeDateStart;
    }

    public LocalDate getFeeDateEnd() {
        return feeDateEnd;
    }

    public void setFeeDateEnd(LocalDate feeDateEnd) {
        this.feeDateEnd = feeDateEnd;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getRelatedTransactionId() {
        return relatedTransactionId;
    }

    public void setRelatedTransactionId(String relatedTransactionId) {
        this.relatedTransactionId = relatedTransactionId;
    }

    public String getFeeCollectionStatus() {
        return feeCollectionStatus;
    }

    public void setFeeCollectionStatus(String feeCollectionStatus) {
        this.feeCollectionStatus = feeCollectionStatus;
    }
}
