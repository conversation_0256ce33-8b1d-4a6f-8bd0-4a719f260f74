package com.kun.linkage.customer.facade.enums;

public enum EmploymentStatusEnum {

    FULL_TIME("full_time", "全职", "Full-time"),
    PART_TIME("part_time", "兼职", "Part-time"),
    INTERNSHIP("internship", "实习", "Internship"),
    NO_EMPLOYER("no_employer", "无雇主", "No Employer");

    private String value;
    private String chineseDescription;
    private String englishDescription;

    /**
     * 字段的type
     */
    private static final String dictType ="Employment Status";

    EmploymentStatusEnum(String value, String chineseDescription, String englishDescription) {
        this.value = value;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }

    public String getValue() {
        return value;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }
}
