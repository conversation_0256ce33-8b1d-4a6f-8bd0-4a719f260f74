package com.kun.linkage.customer.facade.enums;

public enum MpcWalletWebhookRiskSuggestEnum {
    PASS("PASS", "通过"),
    BLOCK("BLOCK", "阻断"),
    CONFIRM("CONFIRM", "待确认");

    private final String value;
    private final String desc;

    MpcWalletWebhookRiskSuggestEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MpcWalletWebhookRiskSuggestEnum getEnumByValue(String value) {
        for (MpcWalletWebhookRiskSuggestEnum mpcWalletWebhookRiskSuggestEnum : MpcWalletWebhookRiskSuggestEnum.values()) {
            if (mpcWalletWebhookRiskSuggestEnum.getValue().equals(value)) {
                return mpcWalletWebhookRiskSuggestEnum;
            }
        }
        return null;
    }

    public static boolean contains(String value) {
        return getEnumByValue(value) != null;
    }
}
