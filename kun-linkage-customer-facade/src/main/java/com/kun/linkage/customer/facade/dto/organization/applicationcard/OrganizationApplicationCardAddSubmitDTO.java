package com.kun.linkage.customer.facade.dto.organization.applicationcard;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 机构卡产品信息提交DTO
 */
public class OrganizationApplicationCardAddSubmitDTO extends OrganizationApplicationCardBean {
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
}
