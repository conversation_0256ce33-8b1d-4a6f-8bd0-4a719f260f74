package com.kun.linkage.customer.facade.dto.organization.account;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 分页查询机构账户信息DTO
 */
public class OrganizationAccountInfoPageQueryDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户号
     */
    @Schema(description = "账户号")
    private String accountNo;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    private String organizationName;

    /**
     * 业务账户类型
     */
    @Schema(description = "业务账户类型")
    private String accountType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
