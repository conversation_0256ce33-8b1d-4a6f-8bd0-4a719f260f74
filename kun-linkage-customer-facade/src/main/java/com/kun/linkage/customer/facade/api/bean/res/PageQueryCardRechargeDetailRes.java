package com.kun.linkage.customer.facade.api.bean.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PageQueryCardRechargeDetailRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 卡id
     */
    @Schema(description = "卡id")
    private String cardId;
    /**
     * 充值日期时间
     */
    @Schema(description = "充值日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rechargeDatetime;
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private BigDecimal rechargeAmount;
    /**
     * 充值币种(卡币种)
     */
    @Schema(description = "充值币种(卡币种)")
    private String rechargeCurrencyCode;
    /**
     * 充值币种精度
     */
    @Schema(description = "充值币种精度")
    private Integer rechargeCurrencyPrecision;
    /**
     * 扣除金额(扣除机构的资金)
     */
    @Schema(description = "扣除金额(扣除机构的资金)")
    private BigDecimal deductAmount;
    /**
     * 扣除币种(扣除机构资金的币种)
     */
    @Schema(description = "扣除币种(扣除机构资金的币种)")
    private String deductCurrencyCode;
    /**
     * 扣除币种精度
     */
    @Schema(description = "扣除币种精度")
    private Integer deductCurrencyPrecision;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public LocalDateTime getRechargeDatetime() {
        return rechargeDatetime;
    }

    public void setRechargeDatetime(LocalDateTime rechargeDatetime) {
        this.rechargeDatetime = rechargeDatetime;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public String getRechargeCurrencyCode() {
        return rechargeCurrencyCode;
    }

    public void setRechargeCurrencyCode(String rechargeCurrencyCode) {
        this.rechargeCurrencyCode = rechargeCurrencyCode;
    }

    public Integer getRechargeCurrencyPrecision() {
        return rechargeCurrencyPrecision;
    }

    public void setRechargeCurrencyPrecision(Integer rechargeCurrencyPrecision) {
        this.rechargeCurrencyPrecision = rechargeCurrencyPrecision;
    }

    public BigDecimal getDeductAmount() {
        return deductAmount;
    }

    public void setDeductAmount(BigDecimal deductAmount) {
        this.deductAmount = deductAmount;
    }

    public String getDeductCurrencyCode() {
        return deductCurrencyCode;
    }

    public void setDeductCurrencyCode(String deductCurrencyCode) {
        this.deductCurrencyCode = deductCurrencyCode;
    }

    public Integer getDeductCurrencyPrecision() {
        return deductCurrencyPrecision;
    }

    public void setDeductCurrencyPrecision(Integer deductCurrencyPrecision) {
        this.deductCurrencyPrecision = deductCurrencyPrecision;
    }
}
