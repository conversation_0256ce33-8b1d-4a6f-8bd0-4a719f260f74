package com.kun.linkage.customer.facade.dto;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

/**
 * 持卡人费率明细修改提交DTO
 */
public class CardholderFeeDetailModifySubmitDTO extends CardholderFeeDetailBean {
    /**
     * 持卡人费率明细ID(修改时需上送)
     */
    @Schema(description = "持卡人费率明细ID(修改时需上送)")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private Long feeDetailId;

    public Long getFeeDetailId() {
        return feeDetailId;
    }

    public void setFeeDetailId(Long feeDetailId) {
        this.feeDetailId = feeDetailId;
    }
}
