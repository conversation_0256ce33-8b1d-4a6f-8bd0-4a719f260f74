package com.kun.linkage.customer.facade.api.bean.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

public class QueryBalanceByCardIdRsp {

    @Schema(description = "cardId")
    private String cardId;
    /**
     * 币种字母码
     */
    @Schema(description = "币种字母码")
    private String currencyCode;
    /**
     * 币种精度
     */
    @Schema(description = "币种精度")
    private Integer currencyPrecision;
    /**
     * 总余额
     */
    @Schema(description = "总余额")
    private BigDecimal totalBalanceAmount;
    /**
     * 冻结金额
     */
    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;
    /**
     * 可用余额
     */
    @Schema(description = "可用余额")
    private BigDecimal availableAmount;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Integer getCurrencyPrecision() {
        return currencyPrecision;
    }

    public void setCurrencyPrecision(Integer currencyPrecision) {
        this.currencyPrecision = currencyPrecision;
    }

    public BigDecimal getTotalBalanceAmount() {
        return totalBalanceAmount;
    }

    public void setTotalBalanceAmount(BigDecimal totalBalanceAmount) {
        this.totalBalanceAmount = totalBalanceAmount;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }

    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }
}
