package com.kun.linkage.customer.facade.enums;

public enum OrganizationFeeCollectionStatusEnum {
    NOT_COLLECTED(0, "未收"),
    COLLECTED(1, "已收");

    private final Integer value;
    private final String desc;
    private final String dictType = "KL_ORG_FEE_COLLECTION_STATUS";

    OrganizationFeeCollectionStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
