package com.kun.linkage.customer.facade.api.bean.req.kyc;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.customer.facade.enums.KycCaseTypeEnum;
import com.kun.linkage.customer.facade.enums.KycLevelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class CustomerKycSubmissionReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求流水号
     */
    @Length(max = 64, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Schema(description = "请求流水号,长度64")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String requestNo;

    /**
     * 用户id
     */
    @Length(max = 64, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Schema(description = "用户id,长度64")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String customerId;

    /**
     * 机构号
     */
    @Length(max = 16, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;

    /**
     * 案件类型 申卡前置报送、KYC报送、kyc升级、资料修改
     */
    @Length(max = 12, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Schema(description = "案件类型 申卡前置报送、KYC报送、kyc升级、资料修改")
    @EnumValue(enumClass = KycCaseTypeEnum.class)
    private String caseType;

    /**
     * KYC等级 无、零级、 一级、二级、三级
     */
    @Length(max = 32, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Schema(description = "KYC等级 无、零级、 一级、二级、三级")
    @EnumValue(enumClass = KycLevelEnum.class)
    private String kycLevel;

    @Length(max = 64, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    @Schema(description = "卡产品编号")
    @JsonAlias({"cardProductNo", "cardProductCode"})// 兼容2个属性名
    private String cardProductNo;

    /**
     * KYC等级一级信息
     */
    @Schema(description = "KYC等级一级信息")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private CustomerKycLevel1Req customerKycLevel1;

    /**
     * KYC等级2级信息
     */
    @Schema(description = "KYC等级2级信息")
    private CustomerKycLevel2Req customerKycLevel2;

    /**
     * KYC等级3级信息
     */
    @Schema(description = "KYC等级3级信息")
    private CustomerKycLevel3Req customerKycLevel3;


    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getKycLevel() {
        return kycLevel;
    }

    public void setKycLevel(String kycLevel) {
        this.kycLevel = kycLevel;
    }

    public String getCardProductNo() {
        return cardProductNo;
    }

    public void setCardProductNo(String cardProductNo) {
        this.cardProductNo = cardProductNo;
    }

    public CustomerKycLevel1Req getCustomerKycLevel1() {
        return customerKycLevel1;
    }

    public void setCustomerKycLevel1(CustomerKycLevel1Req customerKycLevel1) {
        this.customerKycLevel1 = customerKycLevel1;
    }

    public CustomerKycLevel2Req getCustomerKycLevel2() {
        return customerKycLevel2;
    }

    public void setCustomerKycLevel2(CustomerKycLevel2Req customerKycLevel2) {
        this.customerKycLevel2 = customerKycLevel2;
    }

    public CustomerKycLevel3Req getCustomerKycLevel3() {
        return customerKycLevel3;
    }

    public void setCustomerKycLevel3(CustomerKycLevel3Req customerKycLevel3) {
        this.customerKycLevel3 = customerKycLevel3;
    }
}
