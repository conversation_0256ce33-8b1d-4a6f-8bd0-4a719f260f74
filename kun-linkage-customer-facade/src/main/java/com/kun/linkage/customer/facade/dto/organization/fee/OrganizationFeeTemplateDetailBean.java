package com.kun.linkage.customer.facade.dto.organization.fee;

import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.customer.facade.enums.OrganizationFeeBillingDimensionEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeCollectionMethodEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 机构费用模版明细bean
 */
public class OrganizationFeeTemplateDetailBean implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手续费类型
     */
    @Schema(description = "手续费类型:字典:KL_ORGANIZATION_FEE_TYPE")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationFeeTypeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String feeType;

    /**
     * 计费维度
     */
    @Schema(description = "计费维度:字典:KL_ORG_FEE_BILLING_DIMENSION")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationFeeBillingDimensionEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String billingDimension;

    /**
     * 收取方式
     */
    @Schema(description = "收取方式:字典:KL_ORG_FEE_COLLECTION_METHOD")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationFeeCollectionMethodEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String collectionMethod;

    /**
     * 币种
     */
    @Schema(description = "币种")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String currencyCode;

    /*
     * 机构费用模版明细详情信息集合
     */
    @Valid
    @Schema(description = "机构费用模版明细详情信息集合")
    @NotNull(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Size(min = 1, message = CommonTipConstant.REQUEST_PARAM_ERROR )
    private List<OrganizationFeeTemplateDetailValueBean> organizationFeeTemplateDetailValueList;

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getBillingDimension() {
        return billingDimension;
    }

    public void setBillingDimension(String billingDimension) {
        this.billingDimension = billingDimension;
    }

    public String getCollectionMethod() {
        return collectionMethod;
    }

    public void setCollectionMethod(String collectionMethod) {
        this.collectionMethod = collectionMethod;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public List<OrganizationFeeTemplateDetailValueBean> getOrganizationFeeTemplateDetailValueList() {
        return organizationFeeTemplateDetailValueList;
    }

    public void setOrganizationFeeTemplateDetailValueList(List<OrganizationFeeTemplateDetailValueBean> organizationFeeTemplateDetailValueList) {
        this.organizationFeeTemplateDetailValueList = organizationFeeTemplateDetailValueList;
    }
}
