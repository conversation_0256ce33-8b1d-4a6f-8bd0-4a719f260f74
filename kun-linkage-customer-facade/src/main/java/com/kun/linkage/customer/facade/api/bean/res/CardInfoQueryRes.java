package com.kun.linkage.customer.facade.api.bean.res;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

public class CardInfoQueryRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 卡ID
     */
    @Schema(description = "卡id")
    private String cardId;
    /**
     * 加密卡号
     */
    @Schema(description = "加密卡号")
    private String cardNo;
    /**
     * 掩码卡号
     */
    @Schema(description = "脱敏卡号")
    private String cardNoMask;
    /**
     * 卡类型:1:额度卡;2:非额度卡;3:账户卡
     */
    @Schema(description = "卡类型:1:额度卡;2:非额度卡;3:账户卡")
    private String cardType;
    /**
     * 加密CVV
     */
    @Schema(description = "加密CVV")
    private String cvv;
    /**
     * 卡有效期
     */
    @Schema(description = "卡有效期")
    private String expiryDate;
    /**
     * 卡片状态
     */
    @Schema(description = "卡状态")
    private String status;
    /**
     * 卡片激活状态
     */
    @Schema(description = "卡片激活状态")
    private String cardActiveStatus;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardNoMask() {
        return cardNoMask;
    }

    public void setCardNoMask(String cardNoMask) {
        this.cardNoMask = cardNoMask;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCardActiveStatus() {
        return cardActiveStatus;
    }

    public void setCardActiveStatus(String cardActiveStatus) {
        this.cardActiveStatus = cardActiveStatus;
    }
}
