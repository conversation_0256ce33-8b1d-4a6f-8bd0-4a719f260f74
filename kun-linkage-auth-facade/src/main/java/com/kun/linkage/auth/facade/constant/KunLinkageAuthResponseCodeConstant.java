package com.kun.linkage.auth.facade.constant;

/**
 * 统一授权交易响应码常量枚举
 * <p>
 * 定义了授权交易服务的所有标准响应码及其含义。
 */
public enum KunLinkageAuthResponseCodeConstant {

    /** 成功 */
    SUCCESS("0000", "Success"),
    /** 请求参数缺失 */
    PARAMETER_MISSING("KLAU1001", "Request parameter is missing"),
    /** 重复交易 */
    DUPLICATE_TRANSACIION("KLAU1002", "Duplicate transaction"),
    /** 原始交易未找到 */
    ORIGINAL_TRANSACTION_NOT_FOUND("KLAU1003", "Original transaction not found"),
    /** 商户号未找到 */
    MID_NOT_FOUND("KLAU1004", "MID not found"),
    /** 卡片未找到 */
    CARD_NOT_FOUND("KLAU1005", "Card not found"),
    /** 卡片状态异常 */
    CARD_STATUS_ERROR("KLAU1006", "Card is not active"),
    /** 卡片余额不足 */
    INSUFFICIENT_AVAILABLE_BALANCE("KLAU1007", "Insufficient available balance"),
    /** 交易类型不支持 */
    TRANSACTION_NOT_SUPPORTED("KLAU1008", "Transaction not supported"),
    /** 处理方不支持 */
    PROCESSOR_NOT_SUPPORTED("KLAU1009", "Processor not supported"),
    /** 单笔交易金额超限 */
    SINGAL_TRANSACTION_AMOUNT_EXCEEDS_LIMIT("KLAU1010", "Single transaction amount exceeds limit"),
    /** 累计交易金额超限 */
    CUMULATIVE_TRANSACTION_AMOUNT_EXCEEDS_LIMIT("KLAU1011", "Cumulative transaction amount exceeds limit"),
    /** 交易次数超限 */
    TRANSACTION_COUNT_EXCEEDS_LIMIT("KLAU1012", "Transaction count exceeds limit"),
    /** 交易被风控系统拒绝 */
    RISK_CONTROL_REJECTED("KLAU1013", "Transaction rejected by risk control system"),
    /** 交易类型不支持 */
    TRANSACTION_TYPE_NOT_SUPPORTED("KLAU1014","Transaction type not supported"),
    /** 交易金额超限 */
    TRANSACTION_AMOUNT_EXCEEDS_LIMIT("KLAU1015", "Transaction amount exceeds limit"),
    /** 交易金额无效 */
    TRANSACTION_AMOUNT_INVALID("KLAU1016", "Transaction amount invalid"),
    /** 商户状态无效 */
    MID_STATUS_INVALID("KLAU1017", "Merchant status invalid"),
    /** 记账失败 */
    ACCOUNTING_FAIL("KLAU1018", "Accounting fail"),
    /** 无法进行记账 */
    CAN_NOT_BE_ACCOUNTING("KLAU1019", "Config error, can not be accounting"),
    /** 调用KUN失败 */
    CALL_KUN_FAIL("KLAU1020", "Call KUN fail, please try again later"),
    /** 超时 */
    TIMEOUT("KLAU1020", "Request timeout, please try again later"),
    /** 未知错误 */
    UNKNOWN_ERROR("9999", "Unknow error");

    /**
     * 构造方法
     *
     * @param code    响应码
     * @param message 响应信息
     */
    KunLinkageAuthResponseCodeConstant(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /** 响应码 */
    private String code;

    /** 响应信息 */
    private String message;

    /**
     * 获取响应码
     *
     * @return 响应码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取响应信息
     *
     * @return 响应信息
     */
    public String getMessage() {
        return message;
    }
}
