package com.kun.linkage.auth.facade.api;

import com.kun.linkage.auth.facade.api.fallback.KunLinkageAuthTransactionFacadeFallback;
import com.kun.linkage.auth.facade.vo.BaseAuthRequestVO;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "kun-linkage-auth", contextId = "kunLinkageAuthTransactionApi",
    path = "/linkage-auth/api/v1/transaction", fallbackFactory = KunLinkageAuthTransactionFacadeFallback.class)
public interface KunLinkageAuthTransactionFacade {

    /**
     * 授权交易
     *
     * @param authRequestVO
     * @return
     */
    @PostMapping("/auth")
    BaseAuthResponseVO createTransaction(@RequestBody BaseAuthRequestVO authRequestVO);

}
