package com.kun.linkage.auth.facade.vo.bpc;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 服务点日期代码VO
 */
public class PointOfServiceDateCodeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 22域第1位：卡数据输入能力
     * <ul>
     *   <li>0 – Unknown 未知</li>
     *   <li>1 – Manual 手工输入</li>
     *   <li>2 – Magnetic stripe reader 磁条读取</li>
     *   <li>3 – Bar code reader 条码读取</li>
     *   <li>4 – OCR reader OCR读取</li>
     *   <li>5 – EMV compatible ICC reader EMV兼容ICC读取</li>
     *   <li>6 – Key entry 键盘输入</li>
     *   <li>7 – Magnetic stripe reader and key entry 磁条读取+键盘输入</li>
     *   <li>8 – Magnetic stripe reader, key entry and EMV compatible ICC reader 磁条+键盘+EMV ICC</li>
     *   <li>9 – Contactless (ICC and magnetic stripe) read 非接触式(ICC/磁条)</li>
     *   <li>A – Contact ICC and Contactless magnetic stripe read 接触ICC+非接磁条</li>
     *   <li>B – Contactless magnetic stripe read 非接磁条</li>
     *   <li>F – Account data on file 账户数据文件</li>
     *   <li>M – Contact ICC and Contactless read 接触ICC+非接触</li>
     *   <li>S – Software implemented reader (using mobile device capabilities) 软件实现读取</li>
     * </ul>
     */
    private String cardDataInputCapability;

    /**
     * 22域第2位：持卡人认证能力
     * <ul>
     *   <li>0 – No electronic authentication 无电子认证</li>
     *   <li>1 – PIN 密码</li>
     *   <li>2 – Electronic signature analysis 电子签名分析</li>
     *   <li>3 – Biometrics 生物识别</li>
     *   <li>4 – Biographic 个人信息</li>
     *   <li>5 – Electronic authentication 电子认证</li>
     *   <li>6 – Other 其他</li>
     *   <li>9 – Authentication value 认证值</li>
     * </ul>
     */
    private String cardholderAuthenticationCapability;

    /**
     * 22域第3位：卡捕获能力
     * <ul>
     *   <li>0 – None 无</li>
     *   <li>1 – Capture 捕获</li>
     * </ul>
     */
    private String cardCaptureCapabilit;

    /**
     * 22域第4位：操作环境
     * <ul>
     *   <li>0 – no terminal used 无终端</li>
     *   <li>1 – on premises of card acceptor, attended 受理方场所，有人</li>
     *   <li>2 – on premises of card acceptor, untended 受理方场所，无人</li>
     *   <li>3 – Off premises of card acceptor, attended 非受理方场所，有人</li>
     *   <li>4 – Off premises of card acceptor, unattended 非受理方场所，无人</li>
     *   <li>5 – On premises of card cardholder, unattended 持卡人场所，无人</li>
     *   <li>9 – CAT level 9 (Mobile POS), attended</li>
     *   <li>R – CAT level 0, unattended</li>
     *   <li>S – CAT level 1, unattended</li>
     *   <li>T – CAT level 2, unattended</li>
     *   <li>U – CAT level 3, unattended</li>
     *   <li>V – CAT level 4, unattended</li>
     *   <li>W – CAT level 5, unattended</li>
     *   <li>X – CAT level 6, unattended</li>
     *   <li>Y – CAT level 7, unattended</li>
     * </ul>
     */
    private String operatingEnvironmen;

    /**
     * 22域第5位：持卡人存在指示器
     * <ul>
     *   <li>0 – Present 在场</li>
     *   <li>1 – Not present, unspecified 不在场，未说明</li>
     *   <li>2 – Not present, mail order 不在场，邮购</li>
     *   <li>3 – Not present, telephone order 不在场，电话订单</li>
     *   <li>4 – Not present, standing order (recurring payment) 不在场，定期支付</li>
     *   <li>5 – Not present, electronic order 不在场，电子订单</li>
     *   <li>I – Merchant-Initiated Authorization, Credential on File, Standing Instruction – Installment 商户发起授权，分期</li>
     *   <li>R – Merchant-Initiated Authorization, Credential on File, Resubmission 商户发起授权，重提</li>
     *   <li>S – Not present, postponed payment 不在场，延期支付</li>
     * </ul>
     */
    private String cardholderPresenceIndicator;

    /**
     * 22域第6位：卡存在标志
     * <ul>
     *   <li>0 – Not present 不在场</li>
     *   <li>1 – Present 在场</li>
     *   <li>2 – Tokenized card number present 令牌化卡号在场</li>
     * </ul>
     */
    private String cardPresence;

    /**
     * 22域第7位：卡数据输入模式
     * <ul>
     *   <li>0 – Unspecified 未指定</li>
     *   <li>1 – Manual, no terminal 手工，无终端</li>
     *   <li>2 – Magnetic stripe read 磁条读取</li>
     *   <li>3 – BAR read 条码读取</li>
     *   <li>4 – OCR read OCR读取</li>
     *   <li>5 – ICC read ICC读取</li>
     *   <li>6 – Key entered 键盘输入</li>
     *   <li>7 – Contactless ICC read 非接ICC读取</li>
     *   <li>8 – Contactless magnetic stripe read 非接磁条读取</li>
     *   <li>9 – Contactless read 非接读取</li>
     *   <li>D – Digital Secure Remote Payment (DSRP) 数字安全远程支付</li>
     *   <li>E – Token on File 文件令牌</li>
     *   <li>F – Credential on File 文件凭证</li>
     *   <li>G – Map data obtained using the GIRO GIRO地图数据</li>
     *   <li>M – MasterPass</li>
     *   <li>R – E-commerce, including remote chip 电子商务，含远程芯片</li>
     *   <li>S – E-commerce, merchant certificate only 电子商务，仅商户证书</li>
     *   <li>T – E-commerce, merchant and cardholder certificate / 3-D Secure transaction 电子商务，商户和持卡人证书/3DS</li>
     *   <li>U – E-commerce, no security 电子商务，无安全</li>
     *   <li>V – E-commerce, channel encryption 电子商务，通道加密</li>
     *   <li>W – Automatic entry of data previously stored in a third-party system 自动录入第三方数据</li>
     *   <li>X – E-commerce, trusted merchant 电子商务，可信商户</li>
     * </ul>
     */
    private String cardDataInputMode;

    /**
     * 22域第8位：持卡人认证方法
     * <ul>
     *   <li>0 – No authentication 无认证</li>
     *   <li>1 – PIN 密码</li>
     *   <li>2 – Electronic signature analysis 电子签名分析</li>
     *   <li>3 – Offline PIN 脱机PIN</li>
     *   <li>4 – Biographic 个人信息</li>
     *   <li>5 – Manual signature 手写签名</li>
     *   <li>6 – Other manual authentication 其他人工认证</li>
     *   <li>A – Passcode 验证码</li>
     *   <li>B – Biometric 生物识别</li>
     *   <li>C – Device code 设备码</li>
     *   <li>D – Device pattern 设备图案</li>
     *   <li>E – Merchant has chosen to share authentication data within authorization; UCAF data collection not supported 商户选择在授权中共享认证数据，不支持UCAF数据收集</li>
     *   <li>F – In-App transaction (mCommerce) 应用内交易</li>
     *   <li>N – E-commerce External Authentication using approved authentication protocol 电子商务外部认证</li>
     *   <li>O – E-commerce Authentication Outage 电子商务认证中断</li>
     *   <li>P – Partial shipment or recurring payment 部分发货或定期支付</li>
     *   <li>Q – Issuer risk based decision 发卡行风险决策</li>
     *   <li>R – Merchant risk based decision 商户风险决策</li>
     *   <li>S – Static AAV 静态AAV</li>
     *   <li>T – E-commerce, UCAF / 3DS authentication is not supported by merchant 商户不支持UCAF/3DS</li>
     *   <li>U – E-commerce, UCAF / 3DS authentication is supported by merchant but is not provided by issuer 商户支持UCAF/3DS但发卡行未提供</li>
     *   <li>V – E-commerce, UCAF / 3DS authentication is supported by merchant and is provided by issuer 商户和发卡行均支持UCAF/3DS</li>
     *   <li>W – E-commerce, 3DS authentication attempted 电子商务尝试3DS认证</li>
     *   <li>X – E-commerce, authentication by merchant 商户认证</li>
     * </ul>
     */
    private String cardholderAuthenticationMethod;

    /**
     * 22域第9位：终端输出能力
     * <ul>
     *   <li>0 – Not authenticated 未认证</li>
     *   <li>1 – ICC</li>
     *   <li>3 – Authorizing agent, online PIN 授权代理，联机PIN</li>
     *   <li>4 – Merchant/card acceptor, signature 商户/受理方签名</li>
     *   <li>5 – Other 其他</li>
     *   <li>D – Device authentication 设备认证</li>
     *   <li>S – Merchant suspicious 商户可疑</li>
     * </ul>
     */
    private String terminalOutputCapability;

    /**
     * 22域第10位：PIN捕获能力
     * <ul>
     *   <li>0 – Unknown 未知</li>
     *   <li>1 – None 无</li>
     *   <li>2 – Magnetic stripe write 磁条写入</li>
     *   <li>3 – ICC</li>
     * </ul>
     */
    private String pinCaptureCapability;

    /**
     * 22域第11位：卡数据输出能力
     * <ul>
     *   <li>0 – Unknown 未知</li>
     *   <li>1 – None 无</li>
     *   <li>2 – Printing capability only 仅打印</li>
     *   <li>3 – Display capability only 仅显示</li>
     *   <li>4 – Printing and display capability 打印和显示</li>
     *   <li>5 – Merchant terminal 商户终端</li>
     * </ul>
     */
    private String cardDataOutputCapability;

    /**
     * 22域第12位：持卡人认证实体
     * <ul>
     *   <li>0 – No PIN capture capability 无PIN捕获能力</li>
     *   <li>1 – Unknown 未知</li>
     *   <li>4 – PIN capture capability 4 characters maximum 最多4位</li>
     *   <li>5 – PIN capture capability 5 characters maximum 最多5位</li>
     *   <li>6 – PIN capture capability 6 characters maximum 最多6位</li>
     *   <li>7 – PIN capture capability 7 characters maximum 最多7位</li>
     *   <li>8 – PIN capture capability 8 characters maximum 最多8位</li>
     *   <li>9 – PIN capture capability 9 characters maximum 最多9位</li>
     *   <li>A – PIN capture capability 10 characters maximum 最多10位</li>
     *   <li>B – PIN capture capability 11 characters maximum 最多11位</li>
     *   <li>C – PIN capture capability 12 characters maximum 最多12位</li>
     *   <li>S – Software implemented PIN pad (using mobile device capabilities or any other technologies) 软件实现PIN键盘</li>
     * </ul>
     */
    private String cardholderAuthenticationEntity;

    public String getCardDataInputCapability() {
        return cardDataInputCapability;
    }

    public void setCardDataInputCapability(String cardDataInputCapability) {
        this.cardDataInputCapability = cardDataInputCapability;
    }

    public String getCardholderAuthenticationCapability() {
        return cardholderAuthenticationCapability;
    }

    public void setCardholderAuthenticationCapability(String cardholderAuthenticationCapability) {
        this.cardholderAuthenticationCapability = cardholderAuthenticationCapability;
    }

    public String getCardCaptureCapabilit() {
        return cardCaptureCapabilit;
    }

    public void setCardCaptureCapabilit(String cardCaptureCapabilit) {
        this.cardCaptureCapabilit = cardCaptureCapabilit;
    }

    public String getOperatingEnvironmen() {
        return operatingEnvironmen;
    }

    public void setOperatingEnvironmen(String operatingEnvironmen) {
        this.operatingEnvironmen = operatingEnvironmen;
    }

    public String getCardholderPresenceIndicator() {
        return cardholderPresenceIndicator;
    }

    public void setCardholderPresenceIndicator(String cardholderPresenceIndicator) {
        this.cardholderPresenceIndicator = cardholderPresenceIndicator;
    }

    public String getCardPresence() {
        return cardPresence;
    }

    public void setCardPresence(String cardPresence) {
        this.cardPresence = cardPresence;
    }

    public String getCardDataInputMode() {
        return cardDataInputMode;
    }

    public void setCardDataInputMode(String cardDataInputMode) {
        this.cardDataInputMode = cardDataInputMode;
    }

    public String getCardholderAuthenticationMethod() {
        return cardholderAuthenticationMethod;
    }

    public void setCardholderAuthenticationMethod(String cardholderAuthenticationMethod) {
        this.cardholderAuthenticationMethod = cardholderAuthenticationMethod;
    }

    public String getTerminalOutputCapability() {
        return terminalOutputCapability;
    }

    public void setTerminalOutputCapability(String terminalOutputCapability) {
        this.terminalOutputCapability = terminalOutputCapability;
    }

    public String getPinCaptureCapability() {
        return pinCaptureCapability;
    }

    public void setPinCaptureCapability(String pinCaptureCapability) {
        this.pinCaptureCapability = pinCaptureCapability;
    }

    public String getCardDataOutputCapability() {
        return cardDataOutputCapability;
    }

    public void setCardDataOutputCapability(String cardDataOutputCapability) {
        this.cardDataOutputCapability = cardDataOutputCapability;
    }

    public String getCardholderAuthenticationEntity() {
        return cardholderAuthenticationEntity;
    }

    public void setCardholderAuthenticationEntity(String cardholderAuthenticationEntity) {
        this.cardholderAuthenticationEntity = cardholderAuthenticationEntity;
    }

    public String toPlainText() {
        StringBuilder sb = new StringBuilder();
        sb.append(StringUtils.isNotBlank(cardDataInputCapability) ? cardDataInputCapability : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardholderAuthenticationCapability) ? cardholderAuthenticationCapability
            : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardCaptureCapabilit) ? cardCaptureCapabilit : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(operatingEnvironmen) ? operatingEnvironmen : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardholderPresenceIndicator) ? cardholderPresenceIndicator : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardPresence) ? cardPresence : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardDataInputMode) ? cardDataInputMode : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardholderAuthenticationMethod) ? cardholderAuthenticationMethod
            : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardholderAuthenticationEntity) ? cardholderAuthenticationEntity
            : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(cardDataOutputCapability) ? cardDataOutputCapability : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(terminalOutputCapability) ? terminalOutputCapability : StringUtils.SPACE);
        sb.append(StringUtils.isNotBlank(pinCaptureCapability) ? pinCaptureCapability : StringUtils.SPACE);
        return sb.toString();
    }
}