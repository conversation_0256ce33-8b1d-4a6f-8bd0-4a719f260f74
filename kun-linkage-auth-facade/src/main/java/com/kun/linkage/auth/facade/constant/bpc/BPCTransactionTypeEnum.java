package com.kun.linkage.auth.facade.constant.bpc;

public enum BPCTransactionTypeEnum {
    /**
     * POS消费交易
     */
    POS_PURCHASE("00", "774", "POS Purchase"),
    /**
     * E-POS消费交易
     */
    EPOS_PURCHASE("00", "680", "E-POS Purchase"),
    /**
     * 授权Chargeback保护期延长交易
     */
    AUTHORIZATION_CHARGEBACK("00", "468", "Authorization Chargeback Protection Period Extension"),
    /**
     * ATM现金提取交易
     */
    ATM_CASH_WITHDRAWAL("01", "700", "ATM Cash Withdrawal"),
    /**
     * POS现金提取交易
     */
    POS_CASH_ADVANCE("01", "777", "POS Cash Advance"),
    /**
     * E-POS现金提取交易
     */
    EPOS_CASH_ADVANCE("01", "683", "EPOS Cash Advance"),
    CASHBACK_PART_OF_PURCHASE_WITH_CASHBACK_TRANSACTION("01","653","Cashback part of Purchase with Cashback transaction"),
    /**
     * 汇款与无卡赎回
     */
    REMITTANCE_AND_CARDLESS_REDEMPTION("01", "709", "Remittance and Cardless Redemption"),
    /**
     * POS消费撤销
     */
    POS_PURCHASE_CANCELLATION("02", "738", "POS Purchase Cancellation"),
    /**
     * 支票担保交易
     */
    CHECK_GUARANTEE_TRANSACTION("03", "740", "Check guarantee transaction"),
    /**
     * 返现消费交易
     */
    PURCHASE_WITH_CASHBACK("09", "776", "Purchase with Cashback"),
    /**
     * P2P借记
     */
    P2P_DEBIT("11", "781", "P2P Debit"),
    /**
     * 跨行转账借记
     */
    INTERBANK_FUNDS_TRANSFER_DEBIT("12", "610", "Interbank Funds Transfer(IBFT) – Debit part"),
    /**
     * 手工取现
     */
    MANUAL_CASH_WITHDRAWAL("17", "670", "Manual Cash withdrawal"),
    /**
     * 借记账户入账
     */
    DEBIT_ACCOUNT_WITH_PRESENTMENT("17", "659", "Debit account with presentment"),
    /**
     * POS预授权
     */
    POS_PRE_AUTHORIZATION("18", "736", "POS Pre-authorisation"),
    /**
     * E-POS预授权
     */
    EPOS_PRE_AUTHORIZATION("18", "677", "E-POS Pre-authorisation"),
    /**
     * 手工消费
     */
    MANUAL_PURCHASE("18", "671", "Manual Purchase"),
    /**
     * 汇款与无卡发起
     */
    REMITTANCE_AND_CARDLESS_ORIGINATION("18", "714", "Remittance and Cardless origination"),
    /**
     * 无卡预授权
     */
    CARDLESS_PRE_AUTHORIZATION("18", "725", "Cardless pre-authorization"),
    /**
     * POS完成
     */
    POS_COMPLETION("19", "737", "POS Completion"),
    /**
     * E-POS完成
     */
    EPOS_COMPLETION("19", "678", "E-POS Completion"),
    /**
     * 无卡完成
     */
    CARDLESS_COMPLETION("19", "726", "Cardless completion"),
    /**
     * POS退货/退款
     */
    POS_RETURN_OR_REFUND("20", "775", "POS Return or Refund"),
    /**
     * E-POS退货/退款
     */
    EPOS_RETURN_OR_REFUND("20", "681", "E-POS Return or Refund"),
    /**
     * 手工退款完成
     */
    MANUAL_REFUND_COMPLETION("20", "669", "Manual Refund Completion"),
    /**
     * 支票现金存款
     */
    CHECK_CASH_DEPOSIT("21", "475", "CHECK Cash Deposit"),
    /**
     * ATM现金存款
     */
    ATM_CASH_DEPOSIT("21", "618", "ATM Cash Deposit"),
    /**
     * 存款
     */
    DEPOSIT("21", "705", "Deposit"),
    /**
     * POS现金存款
     */
    POS_CASH_DEPOSIT("21", "712", "POS Cash Deposit"),
    /**
     * E-POS现金存款
     */
    EPOS_CASH_DEPOSIT("21", "711", "E-POS Cash Deposit"),
    /**
     * 信用调整
     */
    CREDIT_ADJUSTMENT("22", "750", "Credit Adjustment"),
    /**
     * 外部账户现金存款
     */
    EXTERNAL_ACCOUNT_CASH_DEPOSIT("23", "443", "External Account Cash Deposit"),
    /**
     * 跨行转账贷记
     */
    INTERBANK_FUNDS_TRANSFER_CREDIT("26", "609", "Interbank Funds Transfer(IBFT) – Credit part"),
    /**
     * 入账账户入账
     */
    CREDIT_ACCOUNT_WITH_PRESENTMENT("27", "760", "Credit account with presentment"),
    /**
     * 政府到个人支付
     */
    GOVERNMENT_TO_CONSUMER_PAYMENT("28", "554", "Government to Consumer payment - G2C_CREDIT"),
    /**
     * 信用支付
     */
    CREDIT_PAYMENT("28", "698", "Credit Payment"),
    /**
     * 其他来源到个人支付
     */
    OTHER_SOURCE_TO_CONSUMER_PAYMENT("28", "898", "Other source to consumer payment - D2C_CREDIT"),
    /**
     * P2P贷记
     */
    P2P_CREDIT("29", "785", "P2P Credit"),
    /**
     * 快速退款
     */
    FAST_REFUND("29", "813", "Fast Refund"),
    /**
     * 余额查询
     */
    BALANCE_INQUIRY("31", "702", "Balance Inquiry"),
    /**
     * POS余额查询
     */
    POS_BALANCE_INQUIRY("31", "784", "POS Balance Inquiry"),
    /**
     * 屏幕余额查询
     */
    BALANCE_INQUIRY_ON_SCREEN("31", "757", "Balance Inquiry on Screen"),
    /**
     * E-POS余额查询
     */
    EPOS_BALANCE_INQUIRY("31", "676", "E-POS Balance Inquiry"),
    /**
     * 转账查询
     */
    TRANSFER_INQUIRY("36", "708", "Transfer Inquiry"),
    /**
     * 转账资金查询
     */
    TRANSFER_INQUIRY_FOR_FUNDS_TRANSFER("36", "703", "Transfer Inquiry for Funds Transfer"),
    /**
     * 转账到外部账户查询
     */
    TRANSFER_INQUIRY_FOR_TRANSFER_TO_EXTERNAL_ACCOUNT("36", "566", "Transfer Inquiry for Transfer to External Account"),
    /**
     * 转账到外币账户查询
     */
    TRANSFER_INQUIRY_FOR_TRANSFER_TO_FOREIGN_ACCOUNT("36", "692", "Transfer Inquiry for Transfer to Foreign Account"),
    /**
     * 提前还款信息
     */
    ADVANCED_REPAYMENT_INFORMATION("37", "684", "Advanced Repayment Information"),
    /**
     * 账户验证
     */
    ACCOUNT_VERIFICATION("38", "796", "Account Verification"),
    /**
     * 小额对账单
     */
    MINI_STATEMENT("39", "704", "Mini-Statement"),
    /**
     * 资金转账
     */
    FUNDS_TRANSFER("40", "703", "Funds Transfer"),
    /**
     * 行内转账(ITFT)
     */
    INTRA_BANK_FUNDS_TRANSFER("40", "805", "Intra Bank Funds Transfer (ITFT)"),
    /**
     * 跨行转账(IBFT) Acquirer=Issuer=Beneficiary
     */
    INTERBANK_FUNDS_TRANSFER_ISSUER_BENEFICIARY("44", "805", "Interbank Funds Transfer(IBFT) Acquirer = Issuer = Beneficiary"),
    /**
     * 跨行转账(IBFT) Acquirer<>Issuer=Beneficiary
     */
    INTERBANK_FUNDS_TRANSFER_ACQUIRER_ISSUER_BENEFICIARY("44", "613", "Interbank Funds Transfer(IBFT) Acquirer <> Issuer = Beneficiary"),
    /**
     * 跨行转账(IBFT) Acquirer=Issuer<>Beneficiary
     */
    INTERBANK_FUNDS_TRANSFER_ISSUER_BENEFICIARY_ACQUIRER("44", "806", "Interbank Funds Transfer(IBFT) Acquirer = Issuer <> Beneficiary"),
    /**
     * 跨行转账(IBFT) Acquirer=Beneficiary<>Issuer
     */
    INTERBANK_FUNDS_TRANSFER_BENEFICIARY_ISSUER_ACQUIRER("44", "807", "Interbank Funds Transfer(IBFT) Acquirer = Beneficiary <> Issuer"),
    /**
     * 跨行转账(IBFT) Acquirer<>Issuer<>Beneficiary
     */
    INTERBANK_FUNDS_TRANSFER_ACQUIRER_ISSUER_BENEFICIARY_MULTI("44", "808", "Interbank Funds Transfer(IBFT) Acquirer <> Issuer <> Beneficiary, only used in case of multiple processing in the system or for notification purposes from acquiring side"),
    /**
     * 转账到外部账户
     */
    TRANSFER_TO_EXTERNAL_ACCOUNT("50", "530", "Transfer to external account"),
    /**
     * 转账到外币账户
     */
    TRANSFER_TO_FOREIGN_ACCOUNT("50", "692", "Transfer to foreign account"),
    /**
     * 公用事业缴费
     */
    UTILITY_PAYMENT("50", "508", "Utility Payment"),
    /**
     * 预定义支付
     */
    PREDEFINED_PAYMENT("50", "510", "Predefined Payment"),
    /**
     * 借记账单支付
     */
    DEBIT_BILLS_PAYMENT("50", "691", "Debit bills payment"),
    /**
     * 全额提前还款
     */
    FULL_ADVANCED_REPAYMENT("51", "588", "Full Advanced Repayment"),
    /**
     * 信封支付
     */
    PAYMENT_FROM_ENVELOPE("58", "707", "Payment from Envelope"),
    /**
     * 跨行转账(IBFT)查询
     */
    INTERBANK_FUNDS_TRANSFER_INQUIRY("61", "809", "Interbank Funds Transfer(IBFT) – Inquiry"),
    /**
     * 跨行转账(IBFT)验证
     */
    INTERBANK_FUNDS_TRANSFER_VERIFICATION("62", "810", "Interbank Funds Transfer(IBFT) – Verification"),
    /**
     * 跨行转账(IBFT)检查
     */
    INTERBANK_FUNDS_TRANSFER_CHECK("62", "811", "Interbank Funds Transfer(IBFT) – Check"),
    /**
     * 卡片挂失
     */
    BLOCK_CARD("91", "493", "Block Card"),
    /**
     * 激活卡片
     */
    ACTIVATE_CARD("91", "644", "Activate Card"),
    /**
     * 更改卡片状态
     */
    CHANGE_CARD_STATUS("91", "672", "Change Card Status"),
    /**
     * 验证卡片
     */
    VALIDATE_CARD("91", "782", "Validate Card"),
    /**
     * 服务开启
     */
    SERVICE_ON("92", "797", "Service On"),
    /**
     * 服务关闭
     */
    SERVICE_OFF("92", "798", "Service Off"),
    /**
     * 强制PIN变更
     */
    FORCED_PIN_CHANGE("93", "603", "Forced PIN Change"),
    /**
     * PIN变更
     */
    PIN_CHANGE("93", "751", "PIN Change"),
    /**
     * 新PIN重输
     */
    NEW_PIN_REENTRY("93", "753", "New PIN Reentry"),
    /**
     * PIN变更确认
     */
    PIN_CHANGE_CONFIRMATION("93", "534", "PIN change confirmation"),
    /**
     * 更改账户状态
     */
    CHANGE_ACCOUNT_STATUS("94", "474", "Change Account Status"),
    /**
     * PIN计数重置
     */
    PIN_COUNT_RESET("95", "748", "PIN count reset"),
    /**
     * Tokenization授权请求
     */
    TOKENIZATION_AUTHORIZATION_REQUEST("96", "555", "Tokenization Authorization Request, TAR"),
    /**
     * 激活码通知
     */
    ACTIVATION_CODE_NOTIFICATION("97", "556", "Activation Code Notification, ACN"),
    /**
     * Tokenization完成通知
     */
    TOKENIZATION_COMPLETE_NOTIFICATION("98", "557", "Tokenization Complete Notification, TCN")
    ;

    private String transactionTypeCode;

    private String svfeCode;

    private String description;

    BPCTransactionTypeEnum(String transactionTypeCode, String svfeCode, String description) {
        this.transactionTypeCode = transactionTypeCode;
        this.svfeCode = svfeCode;
        this.description = description;
    }

    public String getTransactionTypeCode() {
        return transactionTypeCode;
    }

    public String getSvfeCode() {
        return svfeCode;
    }

    public String getDescription() {
        return description;
    }
}
