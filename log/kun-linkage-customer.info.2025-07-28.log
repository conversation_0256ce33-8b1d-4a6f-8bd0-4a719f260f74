2025-07-28 14:15:43.772 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-28 14:15:43.838 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-28 14:15:44.445 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 14:15:44.445 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 14:15:46.175 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-28 14:15:46.224 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-28 14:15:47.428 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 14:15:47.433 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 14:15:47.472 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-07-28 14:15:47.695 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-28 14:15:48.070 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=864c8e2d-af60-38bc-a877-59d74b6e2a2a
2025-07-28 14:15:48.190 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:48.191 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:48.192 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/472736752] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:48.192 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:48.195 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:48.199 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:49.402 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$8ff84994] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 14:15:50.368 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-28 14:15:50.368 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 4128 ms
2025-07-28 14:16:01.021 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-28 14:16:07.015 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:07.115 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:07.129 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:08.001 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-28 14:16:08.001 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-28 14:16:08.001 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-28 14:16:14.452 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:14.484 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:15.131 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-28 14:16:16.406 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-28 14:16:19.081 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-28 14:16:25.984 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:26.507 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-28 14:16:26.976 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-28 14:16:28.641 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-28 14:16:28.691 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 14:16:28.691 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-28 14:16:28.702 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-28 14:16:28.704 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 14:16:28.705 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 14:16:28.705 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-28 14:16:28.705 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@22881b1c
2025-07-28 14:16:31.830 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-28 14:16:32.517 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5d4838fc[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$676b6f4#mpcWalletEventRetryNotifyTask]
2025-07-28 14:16:32.517 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:organizationSMSFeeCalculateTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@58989a4c[class com.kun.linkage.customer.task.OrganizationSMSFeeCalculateTask$$EnhancerBySpringCGLIB$$57c1a976#organizationSMSFeeCalculateTask]
2025-07-28 14:16:32.518 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@21833d72[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$e4143daf#syncCustomerInfoTask]
2025-07-28 14:16:32.530 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:32.549 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 14:16:37.883 [Thread-137] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-28 14:16:44.135 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 14:16:44.136 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-28 14:16:55.309 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 14:16:55.309 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-28 14:17:06.516 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 14:17:06.517 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-28 14:17:17.691 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 14:17:17.691 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-28 14:17:25.868 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 14:17:25.869 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-28 14:17:25.994 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-28 14:17:26.030 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-28 14:17:26.047 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-28 14:17:26.128 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-28 14:17:26.219 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-28 14:17:26.268 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 14:17:26.268 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 14:17:26.790 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-28 14:17:26.796 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-28 14:17:26.796 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 14:17:26.834 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 103.405 seconds (JVM running for 110.008)
2025-07-28 14:17:26.865 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-28 14:17:26.865 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-28 14:17:26.865 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-28 14:17:27.416 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 14:17:27.417 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-28 14:17:27.423 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 6 ms
2025-07-28 14:23:21.130 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
