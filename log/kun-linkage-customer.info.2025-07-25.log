2025-07-25 10:31:27.184 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 10:31:27.257 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 10:31:27.869 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:31:27.869 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:31:29.625 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 10:31:29.657 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 10:31:30.941 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 10:31:30.945 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 10:31:30.990 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-25 10:31:31.187 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 10:31:31.494 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0d5d0aa5-49e4-3a0e-a227-4d4a31a385b5
2025-07-25 10:31:31.608 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:31.609 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:31.609 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/218857805] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:31.610 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:31.612 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:31.617 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:32.227 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$f1123c20] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:31:33.100 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 10:31:33.100 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3429 ms
2025-07-25 10:31:45.730 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 10:31:50.868 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:31:50.965 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:31:50.979 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:31:51.847 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 10:31:51.847 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 10:31:51.847 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 10:31:58.407 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:31:58.441 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:31:59.074 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 10:32:01.180 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:32:04.825 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:32:12.801 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:32:13.304 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 10:32:13.771 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 10:32:15.560 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 10:32:15.614 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 10:32:15.615 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 10:32:15.626 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 10:32:15.629 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 10:32:15.630 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 10:32:15.630 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 10:32:15.630 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3f937c0e
2025-07-25 10:32:18.729 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 10:32:19.403 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1bee92f9[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$9087806#syncCustomerInfoTask]
2025-07-25 10:32:19.415 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:32:19.434 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:32:24.787 [Thread-144] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 10:32:31.182 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:32:31.183 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 10:32:42.423 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:32:42.423 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 10:32:53.651 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:32:53.652 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 10:33:04.881 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:04.882 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 10:33:13.106 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:13.106 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 10:33:13.245 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 10:33:13.283 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 10:33:13.302 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 10:33:13.389 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 10:33:13.493 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 10:33:13.545 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:33:13.545 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:33:19.993 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:25.998 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:32.004 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:36.740 [XNIO-1 task-1] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 10:33:36.741 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 10:33:36.745 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 4 ms
2025-07-25 10:33:38.012 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:43.580 [XNIO-1 task-1] INFO  [ 667020903c9acf11 , 667020903c9acf11 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 978 ms
2025-07-25 10:33:44.025 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:33:44.053 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-25 10:33:44.054 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 10:33:44.054 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 10:33:44.054 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 10:33:44.063 [Thread-144] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-25 10:33:44.663 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-25 10:33:44.664 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-25 10:33:44.664 [main] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-25 10:33:44.665 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-25 10:33:44.665 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-25 10:33:44.666 [Thread-133] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-25 10:33:50.688 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 10:33:50.689 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 10:33:56.699 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 10:33:56.701 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 10:33:56.892 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-25 10:33:56.898 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-25 10:33:56.900 [main] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-25 10:33:56.905 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 10:33:56.925 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-25 10:38:38.960 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 10:38:39.028 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 10:38:39.648 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:38:39.648 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:38:41.425 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 10:38:41.469 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [d9815e79-494d-4e1e-b4d6-42811e67b060_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-25 10:38:41.475 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 10:38:42.698 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 10:38:42.702 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 10:38:42.738 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-07-25 10:38:42.950 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 10:38:43.241 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0d5d0aa5-49e4-3a0e-a227-4d4a31a385b5
2025-07-25 10:38:43.350 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:43.351 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:43.352 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:43.352 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:43.355 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:43.359 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:43.957 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$c4129990] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:38:44.822 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 10:38:44.822 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3330 ms
2025-07-25 10:38:57.592 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 10:39:02.913 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:03.005 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:03.022 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:03.862 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 10:39:03.863 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 10:39:03.863 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 10:39:10.392 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:10.425 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:11.353 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 10:39:12.688 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:39:16.649 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:39:23.500 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:23.987 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 10:39:24.459 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 10:39:26.137 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 10:39:26.185 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 10:39:26.185 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 10:39:26.195 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 10:39:26.198 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 10:39:26.198 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 10:39:26.198 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 10:39:26.199 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6cb6a38c
2025-07-25 10:39:29.225 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 10:39:29.946 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a741f59[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$67a1f361#syncCustomerInfoTask]
2025-07-25 10:39:29.958 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:29.978 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:39:35.339 [Thread-141] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 10:39:41.656 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:39:41.657 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 10:39:52.887 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:39:52.888 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 10:40:04.112 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:40:04.114 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 10:40:15.340 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:40:15.341 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 10:40:23.626 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:40:23.627 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 10:40:23.726 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 10:40:23.764 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 10:40:23.782 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 10:40:23.868 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 10:40:23.965 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 10:40:24.003 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:40:24.003 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:40:24.514 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 10:40:24.518 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 10:40:24.518 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 10:40:24.561 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 105.948 seconds (JVM running for 112.395)
2025-07-25 10:40:24.597 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 10:40:24.598 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 10:40:24.598 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 10:40:25.144 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 10:40:25.145 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 10:40:25.152 [RMI TCP Connection(9)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-07-25 10:40:41.039 [XNIO-1 task-1] INFO  [ 24525794724500dd , 24525794724500dd ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1051 ms
2025-07-25 10:44:43.259 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 10:45:14.217 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-25 10:45:14.230 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 10:45:14.234 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:45:14.236 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:45:14.237 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:45:14.237 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:45:14.237 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:45:14.260 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-25 10:45:14.260 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 10:45:14.260 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 10:45:14.261 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 10:45:14.266 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-25 10:45:14.299 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-25 10:45:14.312 [Thread-141] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-25 10:45:14.418 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-25 10:45:14.419 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-25 10:45:14.419 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-25 10:45:14.419 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-25 10:45:14.419 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-25 10:45:14.420 [Thread-130] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-25 10:45:20.442 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 10:45:20.442 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 10:45:26.457 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 10:45:26.459 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 10:45:26.658 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-25 10:45:26.667 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-25 10:45:34.262 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 10:45:34.323 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 10:45:34.916 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:45:34.916 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:45:36.636 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 10:45:36.681 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [1c2a9459-c0dc-422b-bee3-cdba8a78596d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-25 10:45:36.683 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 10:45:37.812 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 10:45:37.817 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 10:45:37.862 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-07-25 10:45:38.061 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 10:45:38.359 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0d5d0aa5-49e4-3a0e-a227-4d4a31a385b5
2025-07-25 10:45:38.469 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:38.470 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:38.470 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/731915467] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:38.471 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:38.474 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:38.478 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:39.060 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$6c2da675] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:45:39.930 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 10:45:39.930 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3230 ms
2025-07-25 10:45:52.917 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 10:45:58.313 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:45:58.414 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:45:58.429 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:45:59.312 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 10:45:59.312 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 10:45:59.312 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 10:46:05.835 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:46:05.866 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:46:06.501 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 10:46:07.799 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:46:11.473 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:46:18.292 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:46:18.771 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 10:46:19.227 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 10:46:20.837 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 10:46:20.886 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 10:46:20.886 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 10:46:20.896 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 10:46:20.898 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 10:46:20.899 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 10:46:20.899 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 10:46:20.899 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3f937c0e
2025-07-25 10:46:23.925 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 10:46:24.585 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1bee92f9[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$6e4413b1#syncCustomerInfoTask]
2025-07-25 10:46:24.597 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:46:24.616 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:46:29.965 [Thread-141] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 10:46:36.286 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:46:36.287 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 10:46:47.518 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:46:47.519 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 10:46:58.760 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:46:58.760 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 10:47:09.996 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:47:09.997 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 10:47:18.225 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:47:18.225 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 10:47:18.371 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 10:47:18.407 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 10:47:18.425 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 10:47:18.506 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 10:47:18.599 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 10:47:18.636 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:47:18.637 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:47:19.038 [XNIO-1 task-1] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 10:47:19.038 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 10:47:19.052 [XNIO-1 task-1] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 14 ms
2025-07-25 10:47:19.386 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 10:47:19.391 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 10:47:19.392 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 10:47:19.435 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 105.479 seconds (JVM running for 111.879)
2025-07-25 10:47:19.487 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 10:47:19.490 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 10:47:19.490 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 10:47:20.973 [XNIO-1 task-1] INFO  [ 659c02a43315a797 , 659c02a43315a797 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 975 ms
2025-07-25 10:47:52.665 [XNIO-1 task-1] INFO  [ 4bd22110f697ea37 , 4bd22110f697ea37 ] c.k.l.c.base.controller.DefaultErrorController.error:101 - Error attributes: {"timestamp":1753411672657,"status":404,"error":"Not Found","path":"/.well-known/appspecific/com.chrome.devtools.json"}
2025-07-25 10:50:37.396 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 10:51:09.044 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-25 10:51:09.050 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 10:51:09.052 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:51:09.052 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:51:09.052 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:51:09.052 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:51:09.053 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:51:09.065 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-25 10:51:09.065 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 10:51:09.065 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 10:51:09.066 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 10:51:09.068 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-25 10:51:09.131 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-25 10:51:09.143 [Thread-141] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-25 10:51:09.672 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-25 10:51:09.673 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-25 10:51:09.673 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-25 10:51:09.673 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-25 10:51:09.673 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-25 10:51:09.673 [Thread-130] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-25 10:51:15.690 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 10:51:15.691 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 10:51:21.705 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 10:51:21.707 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 10:51:21.895 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-25 10:51:21.903 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-25 10:51:29.479 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 10:51:29.540 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 10:51:30.173 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:51:30.173 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:51:31.891 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 10:51:31.937 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 10:51:33.041 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 10:51:33.045 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 10:51:33.091 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-07-25 10:51:33.297 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 10:51:33.589 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0d5d0aa5-49e4-3a0e-a227-4d4a31a385b5
2025-07-25 10:51:33.695 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:33.696 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:33.696 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/731915467] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:33.697 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:33.699 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:33.703 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:34.282 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$6c2da675] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:51:35.115 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 10:51:35.115 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3162 ms
2025-07-25 10:51:47.885 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 10:51:53.897 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:51:53.998 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:51:54.014 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:51:54.861 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 10:51:54.861 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 10:51:54.862 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 10:52:01.334 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:52:01.368 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:52:01.976 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 10:52:03.291 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:52:06.768 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 10:52:13.574 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:52:14.049 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 10:52:14.492 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 10:52:16.156 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 10:52:16.206 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 10:52:16.207 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 10:52:16.218 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 10:52:16.220 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 10:52:16.221 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 10:52:16.221 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 10:52:16.221 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1933e1dc
2025-07-25 10:52:19.264 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 10:52:19.909 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3e6046fb[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$67a1f361#syncCustomerInfoTask]
2025-07-25 10:52:19.921 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:52:19.940 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 10:52:25.281 [Thread-141] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 10:52:31.605 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:52:31.606 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 10:52:42.858 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:52:42.859 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 10:52:54.103 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:52:54.104 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 10:53:05.347 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:53:05.347 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 10:53:13.583 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 10:53:13.585 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 10:53:13.716 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 10:53:13.752 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 10:53:13.768 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 10:53:13.848 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 10:53:13.942 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 10:53:13.981 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 10:53:13.981 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 10:53:14.510 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 10:53:14.515 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 10:53:14.516 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 10:53:14.554 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 105.405 seconds (JVM running for 111.723)
2025-07-25 10:53:14.585 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 10:53:14.586 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 10:53:14.586 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 10:53:15.042 [RMI TCP Connection(8)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 10:53:15.042 [RMI TCP Connection(8)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 10:53:15.047 [RMI TCP Connection(8)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 5 ms
2025-07-25 10:53:50.037 [XNIO-1 task-1] INFO  [ 8a7955b6624af69f , 8a7955b6624af69f ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 965 ms
2025-07-25 10:54:22.907 [XNIO-1 task-1] INFO  [ eb7aa847f5288373 , eb7aa847f5288373 ] c.k.l.c.s.OrganizationCustomerCardInfoService.getOrganizationCustomerCardList:49 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='********', createDateFrom=2000-07-25, createDateUntil=2025-07-25, cardId='null', customerId='null', cardStatus='null'}
2025-07-25 10:54:23.938 [XNIO-1 task-1] INFO  [ eb7aa847f5288373 , eb7aa847f5288373 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info AS card LEFT JOIN kl_account_info account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_date >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_date <= ?
2025-07-25 10:54:23.938 [XNIO-1 task-1] INFO  [ eb7aa847f5288373 , eb7aa847f5288373 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 10:54:23.938 [XNIO-1 task-1] INFO  [ eb7aa847f5288373 , eb7aa847f5288373 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_date >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_date <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:11:33.315 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 11:12:11.210 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 11:12:11.276 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 11:12:11.869 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 11:12:11.870 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 11:12:13.585 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 11:12:13.646 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [b6529580-2eff-4870-a559-220b0f17a35c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-25 11:12:13.649 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 11:12:14.805 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 11:12:14.809 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 11:12:14.850 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-25 11:12:15.066 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 11:12:15.356 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0d5d0aa5-49e4-3a0e-a227-4d4a31a385b5
2025-07-25 11:12:15.460 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:15.461 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:15.461 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/820781338] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:15.462 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:15.466 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:15.470 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:16.054 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$5a9a0c2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:12:16.906 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 11:12:16.906 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3239 ms
2025-07-25 11:12:29.998 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 11:12:36.326 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:12:36.429 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:12:36.446 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:12:37.288 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 11:12:37.288 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 11:12:37.289 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 11:12:43.753 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:12:43.787 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:12:44.392 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 11:12:45.785 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 11:12:49.614 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 11:12:56.482 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:12:56.992 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 11:12:57.432 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 11:12:59.085 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 11:12:59.135 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 11:12:59.135 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 11:12:59.146 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 11:12:59.148 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 11:12:59.148 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 11:12:59.148 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 11:12:59.148 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2c018211
2025-07-25 11:13:02.205 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 11:13:02.909 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3065a6ee[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$48062fa6#syncCustomerInfoTask]
2025-07-25 11:13:02.922 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:13:02.943 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:13:08.303 [Thread-142] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 11:13:14.646 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:13:14.647 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 11:13:25.897 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:13:25.899 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 11:13:37.145 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:13:37.148 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 11:13:48.392 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:13:48.394 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 11:13:56.630 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:13:56.632 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 11:13:56.770 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 11:13:56.816 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 11:13:56.837 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 11:13:56.922 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 11:13:57.028 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 11:13:57.070 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 11:13:57.071 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 11:13:57.307 [XNIO-1 task-2] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 11:13:57.307 [XNIO-1 task-2] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 11:13:57.312 [XNIO-1 task-2] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 5 ms
2025-07-25 11:13:57.872 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 11:13:57.880 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 11:13:57.881 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 11:13:57.967 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 107.061 seconds (JVM running for 113.321)
2025-07-25 11:13:58.037 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 11:13:58.037 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 11:13:58.038 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 11:13:59.462 [XNIO-1 task-3] INFO  [ 162205c92200bce5 , 162205c92200bce5 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1205 ms
2025-07-25 11:14:06.867 [XNIO-1 task-3] INFO  [ 5839f0274463ddfe , 5839f0274463ddfe ] c.k.l.c.s.OrganizationCustomerCardInfoService.getOrganizationCustomerCardList:49 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='********', createDateFrom=2000-07-25, createDateUntil=2025-07-25, cardId='null', customerId='null', cardStatus='null'}
2025-07-25 11:14:07.972 [XNIO-1 task-3] INFO  [ 5839f0274463ddfe , 5839f0274463ddfe ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info AS card LEFT JOIN kl_account_info account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ?
2025-07-25 11:14:07.972 [XNIO-1 task-3] INFO  [ 5839f0274463ddfe , 5839f0274463ddfe ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 11:14:07.972 [XNIO-1 task-3] INFO  [ 5839f0274463ddfe , 5839f0274463ddfe ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:37:08.570 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 11:37:24.707 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 11:37:24.788 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 11:37:25.418 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 11:37:25.419 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 11:37:27.221 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 11:37:27.260 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [360fac65-7d2d-4ac0-90e4-dfb5cf1b539d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-25 11:37:27.270 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 11:37:28.469 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 11:37:28.474 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 11:37:28.508 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-25 11:37:28.718 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 11:37:29.011 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0d5d0aa5-49e4-3a0e-a227-4d4a31a385b5
2025-07-25 11:37:29.124 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:29.124 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:29.125 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/351108575] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:29.125 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:29.128 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:29.132 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:30.224 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$b2b5dc13] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 11:37:31.139 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 11:37:31.140 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3852 ms
2025-07-25 11:37:43.835 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 11:37:49.898 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:37:49.994 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:37:50.009 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:37:50.867 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 11:37:50.868 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 11:37:50.868 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 11:37:57.375 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:37:57.424 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:37:58.048 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 11:37:59.436 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 11:38:04.716 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 11:38:11.546 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:38:12.039 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 11:38:12.470 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 11:38:14.063 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 11:38:14.111 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 11:38:14.111 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 11:38:14.122 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 11:38:14.124 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 11:38:14.124 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 11:38:14.124 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 11:38:14.124 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2f23fdf1
2025-07-25 11:38:17.129 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 11:38:17.776 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@41c6dbac[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$750b0217#syncCustomerInfoTask]
2025-07-25 11:38:17.788 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:38:17.806 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 11:38:23.162 [Thread-144] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 11:38:29.494 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:38:29.495 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 11:38:40.716 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:38:40.717 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 11:38:51.968 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:38:51.969 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 11:39:03.195 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:39:03.196 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 11:39:11.420 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:39:11.422 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 11:39:11.556 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 11:39:11.598 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 11:39:11.615 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 11:39:11.696 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 11:39:11.794 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 11:39:11.832 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 11:39:11.832 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 11:39:12.358 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 11:39:12.363 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 11:39:12.363 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 11:39:12.402 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 108.119 seconds (JVM running for 114.408)
2025-07-25 11:39:12.433 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 11:39:12.433 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 11:39:12.433 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 11:39:12.638 [RMI TCP Connection(7)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 11:39:12.638 [RMI TCP Connection(7)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 11:39:12.646 [RMI TCP Connection(7)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 7 ms
2025-07-25 11:42:30.237 [XNIO-1 task-1] INFO  [ e7ebe2214ff24439 , e7ebe2214ff24439 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1002 ms
2025-07-25 11:42:36.720 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] c.k.l.c.s.OrganizationCustomerCardInfoService.getOrganizationCustomerCardList:49 - 获取商户卡列表请求参数:OrganizationCustomerCardListQueryDTO{organizationNo='********', createDateFrom=2000-07-25, createDateUntil=2025-07-25, cardId='null', customerId='null', cardStatus='null'}
2025-07-25 11:42:37.817 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_customer_card_info AS card LEFT JOIN kl_account_info account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ?
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_0 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_1 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_2 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_3 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_4 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_5 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.818 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_6 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:37.819 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_customer_card_info_0 AS card LEFT JOIN kl_account_info_7 account ON card.customer_id = account.business_customer_id AND card.currency_code = account.currency_code AND card.organization_no = account.business_organization_no WHERE card.organization_no = ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time >= ? AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d') AND card.create_time <= ? ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25]
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Logic SQL: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info as card
        left join kl_account_info account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ? 
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@27effcbd], lock=Optional.empty, window=Optional.empty)
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_0 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_1 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_2 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_3 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.071 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_4 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.072 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_5 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.072 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_6 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:42:39.072 [XNIO-1 task-1] INFO  [ b6c167e3507c7841 , b6c167e3507c7841 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select
        card.id,
        card.create_time as openTime,
        card.organization_no,
        card.customer_id,
        card.card_id,
        card.masked_card_no AS cardNo,
        card.currency_code as currency,
        card.card_status,
        card.card_active_status,
        CONCAT("+",card.mobile_phone_area," ", card.mobile_phone) as cardholderMobile,
        card.email as cardholderEmail,
        account.available_amount as availableAmount,
        CONCAT(card.cardholder_first_name," ", card.cardholder_last_name) as cardholderName
        from kl_organization_customer_card_info_0 as card
        left join kl_account_info_7 account on card.customer_id = account.business_customer_id and card.currency_code = account.currency_code and card.organization_no = account.business_organization_no
         WHERE  card.organization_no = ?
            
            
            
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') >= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time >= ?
            
            
                AND DATE_FORMAT(card.create_time, '%Y-%m-%d') <= DATE_FORMAT(?, '%Y-%m-%d')
                AND card.create_time <= ? 
        order by card.create_time desc
 LIMIT ?  ::: [********, 2000-07-25, 2000-07-25, 2025-07-25, 2025-07-25, 100]
2025-07-25 11:43:44.296 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 11:44:17.267 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-25 11:44:17.281 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 11:44:17.285 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:44:17.286 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:44:17.286 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:44:17.286 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:44:17.287 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 11:44:17.309 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-25 11:44:17.309 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 11:44:17.310 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 11:44:17.310 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 11:44:17.314 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-25 11:44:17.351 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-25 11:44:17.365 [Thread-144] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-25 11:44:17.786 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-25 11:44:17.787 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-25 11:44:17.788 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-25 11:44:17.789 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-25 11:44:17.789 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-25 11:44:17.790 [Thread-134] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-25 11:44:23.802 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 11:44:23.802 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 11:44:29.814 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 11:44:29.814 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 11:44:29.982 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-25 11:44:29.989 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-25 15:15:12.515 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 15:15:12.582 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 15:15:13.203 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:15:13.203 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:15:14.938 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 15:15:14.984 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 15:15:16.189 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 15:15:16.194 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:15:16.230 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-25 15:15:16.444 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 15:15:16.742 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=50859f36-adf3-3e19-975f-db83aaaea31f
2025-07-25 15:15:16.856 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:16.857 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:16.857 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:16.858 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:16.860 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:16.864 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:17.478 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$480fa016] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:15:18.412 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 15:15:18.412 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3411 ms
2025-07-25 15:15:29.187 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 15:15:34.962 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:35.057 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:35.072 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:35.932 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 15:15:35.932 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:15:35.932 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 15:15:42.389 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:42.424 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:43.050 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 15:15:44.325 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 15:15:46.830 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 15:15:53.671 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:15:54.145 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 15:15:54.609 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 15:15:56.253 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 15:15:56.302 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 15:15:56.302 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 15:15:56.312 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 15:15:56.315 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 15:15:56.315 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 15:15:56.315 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 15:15:56.315 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5ec5a449
2025-07-25 15:15:59.389 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 15:16:00.049 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@118b73d9[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$cf6253a1#syncCustomerInfoTask]
2025-07-25 15:16:00.061 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:00.080 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:16:05.426 [Thread-135] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 15:16:11.687 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:16:11.688 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 15:16:22.856 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:16:22.857 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 15:16:34.034 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:16:34.035 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 15:16:45.201 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:16:45.203 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 15:16:53.368 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:16:53.369 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 15:16:53.507 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 15:16:53.545 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 15:16:53.563 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 15:16:53.653 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 15:16:53.748 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 15:16:53.787 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:16:53.787 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:16:54.318 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 15:16:54.324 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 15:16:54.325 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 15:16:54.365 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 102.206 seconds (JVM running for 108.694)
2025-07-25 15:16:54.398 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 15:16:54.399 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 15:16:54.399 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 15:16:54.842 [RMI TCP Connection(3)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:16:54.842 [RMI TCP Connection(3)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:16:54.848 [RMI TCP Connection(3)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 5 ms
2025-07-25 15:17:05.327 [XNIO-1 task-1] INFO  [ cfb9211340217914 , cfb9211340217914 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 925 ms
2025-07-25 15:22:32.293 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 15:24:14.698 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 15:24:14.765 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 15:24:15.376 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:24:15.376 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:24:17.089 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 15:24:17.134 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 15:24:18.295 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 15:24:18.359 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:24:18.446 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-25 15:24:18.666 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 15:24:18.959 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=50859f36-adf3-3e19-975f-db83aaaea31f
2025-07-25 15:24:19.069 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:19.070 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:19.070 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/747258971] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:19.071 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:19.074 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:19.077 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:19.673 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$b9afc27e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:24:20.538 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 15:24:20.538 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3387 ms
2025-07-25 15:24:31.302 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 15:24:36.293 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:24:36.398 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:24:36.415 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:24:37.268 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 15:24:37.268 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:24:37.268 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 15:24:43.672 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:24:43.706 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:24:44.329 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 15:24:45.542 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 15:24:48.111 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 15:24:54.935 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:24:55.434 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 15:24:55.874 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 15:24:57.524 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 15:24:57.572 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 15:24:57.572 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 15:24:57.582 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 15:24:57.585 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 15:24:57.585 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 15:24:57.585 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 15:24:57.585 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1d546842
2025-07-25 15:25:00.889 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 15:25:01.591 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f028abf[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$6a697f2c#syncCustomerInfoTask]
2025-07-25 15:25:01.604 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:25:01.623 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:25:07.148 [Thread-133] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 15:25:13.421 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:25:13.422 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 15:25:24.594 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:25:24.595 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 15:25:35.775 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:25:35.776 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 15:25:46.951 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:25:46.952 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 15:25:55.131 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:25:55.133 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 15:25:55.255 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 15:25:55.291 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 15:25:55.308 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 15:25:55.390 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 15:25:55.489 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 15:25:55.525 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:25:55.525 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:25:56.100 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 15:25:56.105 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 15:25:56.105 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 15:25:56.144 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 101.766 seconds (JVM running for 107.966)
2025-07-25 15:25:56.176 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 15:25:56.177 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 15:25:56.177 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 15:25:56.499 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:25:56.500 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:25:56.505 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 5 ms
2025-07-25 15:27:11.043 [XNIO-1 task-1] INFO  [ 2b6ac6b159c1aff9 , 2b6ac6b159c1aff9 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 967 ms
2025-07-25 15:27:18.852 [XNIO-1 task-1] INFO  [ 3148833d618c1648 , 3148833d618c1648 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-25 15:27:18.852 [XNIO-1 task-1] INFO  [ 3148833d618c1648 , 3148833d618c1648 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:27:18.852 [XNIO-1 task-1] INFO  [ 3148833d618c1648 , 3148833d618c1648 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-25 15:27:19.558 [XNIO-1 task-1] INFO  [ 3148833d618c1648 , 3148833d618c1648 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ?
2025-07-25 15:27:19.558 [XNIO-1 task-1] INFO  [ 3148833d618c1648 , 3148833d618c1648 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:27:19.558 [XNIO-1 task-1] INFO  [ 3148833d618c1648 , 3148833d618c1648 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2025Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2025Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2026Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2026Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2026Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2026Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2027Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2027Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2027Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2027Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2028Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2028Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2028Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2028Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2029Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2029Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2029Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2029Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2030Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2030Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2030Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2030Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2031Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2031Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2031Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2031Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2032Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2032Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2032Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2032Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2033Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2033Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2033Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2033Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2034Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2034Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2034Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2034Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2035Q1 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2035Q2 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2035Q3 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? UNION ALL SELECT count(0) FROM kl_card_recharge_detail_2035Q4 WHERE organization_no = ? AND customer_id = ? AND recharge_datetime >= CONCAT(?, ' 00:00:00') AND recharge_datetime <= CONCAT(?, ' 23:59:59') AND recharge_amount >= ? AND recharge_amount <= ? AND recharge_status = ? ::: [12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL, 12090276, 23, 2025-07-01, 2025-07-25, 0, 100, FAIL]
2025-07-25 15:40:37.405 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 15:41:09.652 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-25 15:41:09.657 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 15:41:09.659 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:41:09.659 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:41:09.659 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:41:09.659 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:41:09.659 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:41:09.673 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-25 15:41:09.673 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 15:41:09.673 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 15:41:09.673 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 15:41:09.676 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-25 15:41:09.711 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-25 15:41:09.720 [Thread-133] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-25 15:41:10.039 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-25 15:41:10.040 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-25 15:41:10.040 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-25 15:41:10.040 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-25 15:41:10.040 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-25 15:41:10.040 [Thread-122] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-25 15:41:16.057 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 15:41:16.057 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 15:41:22.071 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 15:41:22.073 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 15:41:22.226 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-25 15:41:22.233 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-25 15:46:55.808 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 15:46:55.878 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 15:46:56.491 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:46:56.491 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:46:58.217 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 15:46:58.244 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 15:46:59.416 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 15:46:59.421 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 15:46:59.458 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-25 15:46:59.691 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 15:46:59.993 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=50859f36-adf3-3e19-975f-db83aaaea31f
2025-07-25 15:47:00.104 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:00.105 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:00.105 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/747258971] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:00.106 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:00.109 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:00.113 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:00.729 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$b9afc27e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 15:47:01.614 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 15:47:01.614 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3358 ms
2025-07-25 15:47:12.407 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 15:47:18.318 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:18.420 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:18.437 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:19.297 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 15:47:19.298 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:47:19.298 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 15:47:25.701 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:25.736 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:26.362 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 15:47:27.678 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 15:47:30.143 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 15:47:37.038 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:37.540 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 15:47:37.990 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 15:47:39.647 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 15:47:39.697 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 15:47:39.697 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 15:47:39.708 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 15:47:39.711 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 15:47:39.711 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 15:47:39.711 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 15:47:39.711 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1d546842
2025-07-25 15:47:42.787 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 15:47:43.436 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7a82be85[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$6a697f2c#syncCustomerInfoTask]
2025-07-25 15:47:43.449 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:43.468 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 15:47:48.816 [Thread-134] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 15:47:55.086 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:47:55.087 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 15:48:06.358 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:48:06.359 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 15:48:17.599 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:48:17.600 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 15:48:28.780 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:48:28.781 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 15:48:36.951 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 15:48:36.951 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 15:48:37.068 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 15:48:37.104 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 15:48:37.122 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 15:48:37.210 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 15:48:37.308 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 15:48:37.346 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:48:37.346 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:48:37.980 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 15:48:37.983 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 15:48:37.984 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 15:48:38.022 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 102.542 seconds (JVM running for 108.796)
2025-07-25 15:48:38.056 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 15:48:38.056 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 15:48:38.056 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 15:48:38.486 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:48:38.486 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 15:48:38.494 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-25 15:48:48.116 [XNIO-1 task-1] INFO  [ 92c91d0fed6bf674 , 92c91d0fed6bf674 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-25 15:48:48.116 [XNIO-1 task-1] INFO  [ 92c91d0fed6bf674 , 92c91d0fed6bf674 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:48:48.116 [XNIO-1 task-1] INFO  [ 92c91d0fed6bf674 , 92c91d0fed6bf674 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-25 15:48:48.782 [XNIO-1 task-1] INFO  [ 92c91d0fed6bf674 , 92c91d0fed6bf674 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?)
2025-07-25 15:48:48.784 [XNIO-1 task-1] INFO  [ 92c91d0fed6bf674 , 92c91d0fed6bf674 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:48:48.784 [XNIO-1 task-1] INFO  [ 92c91d0fed6bf674 , 92c91d0fed6bf674 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100]
2025-07-25 15:52:18.364 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-25 15:52:18.365 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:52:18.365 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-25 15:54:09.440 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ?)
2025-07-25 15:54:09.441 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:54:09.442 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59]
2025-07-25 15:55:15.861 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ?)
2025-07-25 15:55:15.864 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:55:15.864 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59]
2025-07-25 15:55:29.908 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ?)
2025-07-25 15:55:29.912 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:55:29.912 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0]
2025-07-25 15:55:37.503 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?)
2025-07-25 15:55:37.504 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:55:37.504 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100]
2025-07-25 15:56:00.091 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ORDER BY recharge_datetime DESC
2025-07-25 15:56:00.092 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:56:00.092 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ORDER BY recharge_datetime DESC ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100]
2025-07-25 15:56:10.575 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 15:56:10.578 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 15:56:10.583 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?)
2025-07-25 15:56:10.584 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:56:10.584 [XNIO-1 task-1] INFO  [ 0c793703096224bf , 0c793703096224bf ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100]
2025-07-25 15:56:49.149 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 15:56:49.151 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 15:56:58.315 [XNIO-1 task-1] INFO  [ 39943bc375f33e13 , 39943bc375f33e13 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-25 15:56:58.316 [XNIO-1 task-1] INFO  [ 39943bc375f33e13 , 39943bc375f33e13 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:56:58.316 [XNIO-1 task-1] INFO  [ 39943bc375f33e13 , 39943bc375f33e13 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-25 15:56:58.413 [XNIO-1 task-1] INFO  [ 39943bc375f33e13 , 39943bc375f33e13 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?)
2025-07-25 15:56:58.413 [XNIO-1 task-1] INFO  [ 39943bc375f33e13 , 39943bc375f33e13 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:56:58.414 [XNIO-1 task-1] INFO  [ 39943bc375f33e13 , 39943bc375f33e13 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100]
2025-07-25 15:57:02.690 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT COUNT( * ) FROM kl_organization_customer_card_info 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?)
2025-07-25 15:57:02.690 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:57:02.690 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT COUNT( * ) FROM kl_organization_customer_card_info_4 
 
 WHERE (organization_no = ? AND customer_id = ? AND card_active_status = ?) ::: [12090276, 23, ACTIVATED]
2025-07-25 15:57:02.777 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_card_recharge_detail WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?)
2025-07-25 15:57:02.777 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 15:57:02.777 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_card_recharge_detail_2025Q3 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100]
2025-07-25 15:57:02.889 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ORDER BY recharge_datetime DESC
 LIMIT ? 
2025-07-25 15:57:02.889 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@2ed2ba7f], lock=Optional.empty, window=Optional.empty)
2025-07-25 15:57:02.890 [XNIO-1 task-1] INFO  [ 452e1f699490a58a , 452e1f699490a58a ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,organization_no,customer_id,request_no,card_id,recharge_datetime,recharge_amount,recharge_currency_code,recharge_currency_precision,recharge_bookkeep_request_no,recharge_bookkeep_status,fx_rate,deduct_currency_code,deduct_processor,deduct_currency_precision,deduct_principal_amount,deduct_principal_bookkeep_request_no,deduct_principal_bookkeep_status,deduct_recharge_fee_amount,deduct_recharge_fee_bookkeep_request_no,deduct_recharge_fee_bookkeep_status,deduct_recharge_fee_detail_id,deduct_acceptance_fee_amount,deduct_acceptance_fee_bookkeep_request_no,deduct_acceptance_fee_bookkeep_status,deduct_acceptance_fee_detail_id,deduct_total_amount,recharge_status,fail_message,bookkeep_reversal_count,create_time,last_modify_time  FROM kl_card_recharge_detail_2025Q3 
 
 WHERE (organization_no = ? AND customer_id = ? AND recharge_datetime BETWEEN ? AND ? AND recharge_amount >= ? AND recharge_amount <= ?) ORDER BY recharge_datetime DESC
 LIMIT ?  ::: [12090276, 23, 2025-07-01T00:00, 2025-07-25T23:59:59, 0, 100, 100]
2025-07-25 16:10:31.836 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 16:11:03.739 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-25 16:11:03.754 [SpringApplicationShutdownHook] INFO  [  ,  ] io.undertow.servlet.log:389 - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 16:11:03.758 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 16:11:03.758 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 16:11:03.759 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 16:11:03.759 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 16:11:03.759 [SpringApplicationShutdownHook] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 16:11:03.782 [SpringApplicationShutdownHook] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-25 16:11:03.782 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 16:11:03.782 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 16:11:03.783 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 16:11:03.787 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
2025-07-25 16:11:03.821 [SpringApplicationShutdownHook] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
2025-07-25 16:11:03.834 [Thread-134] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-25 16:11:04.590 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-customer-executor', registryValue='http://172.19.151.145:16661/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-25 16:11:04.591 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-25 16:11:04.591 [SpringApplicationShutdownHook] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-25 16:11:04.591 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-25 16:11:04.592 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-25 16:11:04.592 [Thread-123] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-25 16:11:10.609 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 16:11:10.611 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 16:11:16.620 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:10911] result: true
2025-07-25 16:11:16.621 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[***********:9876] result: true
2025-07-25 16:11:16.794 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-25 16:11:16.801 [SpringApplicationShutdownHook] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-25 18:25:24.405 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 18:25:24.478 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 18:25:25.264 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 18:25:25.264 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 18:25:27.166 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 18:25:27.195 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 18:25:44.956 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 18:25:45.022 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 18:25:45.619 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 18:25:45.619 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 18:25:47.368 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 18:25:47.408 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 18:25:48.615 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 18:25:48.620 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 18:25:48.659 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-07-25 18:25:48.883 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 18:25:49.179 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=efd1a1bf-72e7-3a6b-a201-f51ff67605f2
2025-07-25 18:25:49.294 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:49.295 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:49.295 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/947069810] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:49.296 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:49.298 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:49.302 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:49.904 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$b8d20511] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:25:50.782 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 18:25:50.782 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3359 ms
2025-07-25 18:26:02.118 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 18:26:07.534 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:07.651 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:07.669 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:08.564 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 18:26:08.565 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 18:26:08.565 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 18:26:15.014 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:15.050 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:15.673 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 18:26:16.835 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 18:26:19.652 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 18:26:26.580 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:27.069 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 18:26:27.528 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 18:26:29.175 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 18:26:29.228 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 18:26:29.229 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 18:26:29.240 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 18:26:29.243 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 18:26:29.243 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 18:26:29.243 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 18:26:29.243 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@107302a5
2025-07-25 18:26:32.284 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 18:26:32.954 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4308744c[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$9c6cf55b#mpcWalletEventRetryNotifyTask]
2025-07-25 18:26:32.955 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@29173f02[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$7a0a7c16#syncCustomerInfoTask]
2025-07-25 18:26:32.968 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:32.987 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:26:38.338 [Thread-133] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 18:26:44.594 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:26:44.595 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 18:26:55.758 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:26:55.760 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 18:27:06.939 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:27:06.940 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 18:27:18.135 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:27:18.137 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 18:27:26.318 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:27:26.319 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 18:27:26.462 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 18:27:26.499 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 18:27:26.515 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 18:27:26.606 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 18:27:26.701 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 18:27:26.741 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 18:27:26.741 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 18:27:27.005 [XNIO-1 task-2] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 18:27:27.005 [XNIO-1 task-2] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 18:27:27.014 [XNIO-1 task-2] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-25 18:27:27.516 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 18:27:27.522 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 18:27:27.523 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 18:27:27.609 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 102.961 seconds (JVM running for 109.173)
2025-07-25 18:27:27.679 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 18:27:27.681 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 18:27:27.681 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 18:27:29.204 [XNIO-1 task-4] INFO  [ ebd9cb178411cb65 , ebd9cb178411cb65 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 1005 ms
2025-07-25 18:30:08.437 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 18:30:53.367 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 18:30:53.457 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 18:30:54.118 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 18:30:54.119 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 18:30:55.908 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 18:30:55.938 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 18:30:57.176 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 18:30:57.181 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 18:30:57.236 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-25 18:30:57.449 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 18:30:57.749 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=efd1a1bf-72e7-3a6b-a201-f51ff67605f2
2025-07-25 18:30:57.863 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:57.864 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:57.865 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/947069810] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:57.866 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:57.868 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:57.872 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:58.473 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$b8d20511] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:30:59.347 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 18:30:59.347 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3393 ms
2025-07-25 18:31:10.185 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 18:31:15.102 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:15.197 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:15.214 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:16.074 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 18:31:16.074 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 18:31:16.074 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 18:31:22.467 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:22.501 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:23.122 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 18:31:24.393 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 18:31:26.928 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 18:31:33.771 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:34.258 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 18:31:34.716 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 18:31:36.659 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 18:31:36.711 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 18:31:36.711 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 18:31:36.722 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 18:31:36.725 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 18:31:36.725 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 18:31:36.725 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 18:31:36.725 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1b9b8759
2025-07-25 18:31:40.012 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 18:31:40.747 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@53b464b9[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$d1a3e64d#mpcWalletEventRetryNotifyTask]
2025-07-25 18:31:40.747 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d7157b0[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$af416d08#syncCustomerInfoTask]
2025-07-25 18:31:40.760 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:40.778 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:31:46.124 [Thread-134] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 18:31:52.390 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:31:52.392 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 18:32:03.578 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:32:03.580 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 18:32:14.747 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:32:14.748 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 18:55:22.069 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-25 18:55:22.139 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 18:55:22.781 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 18:55:22.781 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 18:55:24.530 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-customer,DEFAULT_GROUP'}]
2025-07-25 18:55:24.554 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-25 18:55:25.796 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 18:55:25.801 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 18:55:25.850 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-07-25 18:55:26.061 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-25 18:55:26.367 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=efd1a1bf-72e7-3a6b-a201-f51ff67605f2
2025-07-25 18:55:26.486 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:26.487 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:26.487 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/947069810] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:26.488 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:26.491 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:26.496 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:27.110 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$b8d20511] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 18:55:27.987 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-25 18:55:27.987 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3419 ms
2025-07-25 18:55:38.546 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-25 18:55:43.516 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:55:43.619 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:55:43.634 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:55:44.503 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-25 18:55:44.503 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 18:55:44.504 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-25 18:55:50.928 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:55:50.958 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:55:51.586 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-25 18:55:52.732 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 18:55:55.897 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.101:6379
2025-07-25 18:56:02.780 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'uplus-user' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:56:03.362 [main] INFO  [  ,  ] c.kun.linkage.customer.config.XxlJobConfiguration.xxlJobExecutor:35 - >>>>>>>>>>> xxl-job config init.
2025-07-25 18:56:03.815 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-25 18:56:05.481 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-25 18:56:05.530 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 18:56:05.531 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-25 18:56:05.541 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-25 18:56:05.544 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 18:56:05.544 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 18:56:05.544 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-25 18:56:05.544 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4eccb9f0
2025-07-25 18:56:08.606 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-25 18:56:09.274 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:mpcWalletEventRetryNotifyTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1946579d[class com.kun.linkage.customer.task.MpcWalletEventRetryNotifyTask$$EnhancerBySpringCGLIB$$4cf0f1bf#mpcWalletEventRetryNotifyTask]
2025-07-25 18:56:09.274 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:syncCustomerInfoTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@196cfaed[class com.kun.linkage.customer.task.SyncCustomerInfoTask$$EnhancerBySpringCGLIB$$2a8e787a#syncCustomerInfoTask]
2025-07-25 18:56:09.286 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:56:09.305 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-wallet-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-25 18:56:14.624 [Thread-133] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 16661
2025-07-25 18:56:20.878 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_ORGANIZATION_FEE_DEDUCTION_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:56:20.879 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationFeeDeductionEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 18:56:32.050 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP', nameServer='mq.dev.kun:9876', topic='MPC_WALLET_WEBHOOK_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:56:32.051 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:mpcWalletEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 18:56:43.250 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CANCEL_CARD_REFUND_BALANCE_GROUP', nameServer='mq.dev.kun:9876', topic='CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:56:43.251 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cancelCardRefundBalanceEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 18:56:54.450 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:56:54.450 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:cardRechargeBookkeepReversalEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 18:57:02.627 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_CUSTOMER_OPEN_CARD_GROUP', nameServer='mq.dev.kun:9876', topic='OPEN_CARD_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-25 18:57:02.628 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:openCardEventConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 18:57:02.770 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-25 18:57:02.807 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-25 18:57:02.823 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 18:57:02.908 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-25 18:57:03.001 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 8080 (http)
2025-07-25 18:57:03.039 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 18:57:03.040 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 18:57:03.561 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-customer 172.19.151.145:8080 register finished
2025-07-25 18:57:03.567 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-25 18:57:03.567 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 18:57:03.607 [main] INFO  [  ,  ] c.k.l.c.KunLinkageCustomerServiceApplication.logStarted:61 - Started KunLinkageCustomerServiceApplication in 101.873 seconds (JVM running for 108.033)
2025-07-25 18:57:03.638 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer, group=DEFAULT_GROUP
2025-07-25 18:57:03.639 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer-local.properties, group=DEFAULT_GROUP
2025-07-25 18:57:03.639 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-customer.properties, group=DEFAULT_GROUP
2025-07-25 18:57:04.160 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 18:57:04.162 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 18:57:04.170 [RMI TCP Connection(5)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 8 ms
2025-07-25 18:57:26.773 [XNIO-1 task-1] INFO  [ eac96bde53976f87 , eac96bde53976f87 ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 966 ms
2025-07-25 18:57:57.848 [XNIO-1 task-1] INFO  [ ef47c47551945bc3 , ef47c47551945bc3 ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_organization_fee_detail WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?)
2025-07-25 18:57:57.848 [XNIO-1 task-1] INFO  [ ef47c47551945bc3 , ef47c47551945bc3 ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-25 18:57:57.848 [XNIO-1 task-1] INFO  [ ef47c47551945bc3 , ef47c47551945bc3 ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_organization_fee_detail_202505 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202506 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) UNION ALL SELECT count(0) FROM kl_organization_fee_detail_202507 WHERE (organization_no = ? AND transaction_datetime BETWEEN ? AND ?) ::: [null, 2025-01-01T00:00, 2025-07-25T23:59:59, null, 2025-01-01T00:00, 2025-07-25T23:59:59, null, 2025-01-01T00:00, 2025-07-25T23:59:59]
2025-07-25 19:00:02.793 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
