2025-07-27 20:16:38.672 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-27 20:16:38.741 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 20:16:39.354 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-27 20:16:39.354 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-27 20:16:41.093 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-clearing,DEFAULT_GROUP'}]
2025-07-27 20:16:41.142 [main] INFO  [  ,  ] c.k.linkage.clearing.KunLinkageClearingApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-27 20:16:42.226 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 20:16:42.230 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 20:16:42.268 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-27 20:16:42.467 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-27 20:16:42.745 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=3d5cf638-c559-3a2e-af14-bcda6b2e71e4
2025-07-27 20:16:42.851 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:42.852 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:42.852 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$533/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:42.853 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:42.855 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:42.860 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:43.437 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$bd7082c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-27 20:16:44.326 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-27 20:16:44.326 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3169 ms
2025-07-27 20:16:54.909 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-27 20:16:59.721 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-27 20:16:59.722 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-27 20:16:59.722 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-27 20:17:06.413 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-27 20:17:07.093 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-27 20:17:08.215 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-27 20:17:10.628 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-27 20:17:11.333 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:1
2025-07-27 20:17:17.588 [main] INFO  [  ,  ] c.kun.linkage.clearing.config.XxlJobConfiguration.xxlJobExecutor:33 - >>>>>>>>>>> xxl-job config init.
2025-07-27 20:17:18.089 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-27 20:17:19.528 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-27 20:17:19.575 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-27 20:17:19.576 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-27 20:17:19.586 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-27 20:17:19.588 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-27 20:17:19.588 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-27 20:17:19.588 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-27 20:17:19.588 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6887e1d1
2025-07-27 20:17:21.593 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:21.593 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:22.735 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 20:17:23.427 [main] INFO  [  ,  ] com.xxl.job.core.executor.XxlJobExecutor.registJobHandler:183 - >>>>>>>>>>> xxl-job register jobhandler success, name:VisaBaseFileTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@209c0be5[class com.kun.linkage.clearing.task.VisaBaseFileTask#ywClearingFileTask]
2025-07-27 20:17:23.437 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-clearing' URL not provided. Will try picking an instance via load-balancing.
2025-07-27 20:17:23.480 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-27 20:17:23.499 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-27 20:17:23.519 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-auth' URL not provided. Will try picking an instance via load-balancing.
2025-07-27 20:17:28.906 [Thread-126] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18084
2025-07-27 20:17:32.341 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:35.345 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-27 20:17:35.345 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:35.346 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile33Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-27 20:17:35.354 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:38.360 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:43.630 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:46.636 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:46.636 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-27 20:17:46.635 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:46.637 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:bpcBaseFile05Listener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-27 20:17:46.751 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-27 20:17:46.787 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-27 20:17:46.803 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-27 20:17:46.882 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-27 20:17:46.973 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9020 (http)
2025-07-27 20:17:47.009 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-27 20:17:47.009 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-27 20:17:49.640 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:51.430 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:51.433 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:17:59.442 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:01.364 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:01.369 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:02.444 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:02.446 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-27 20:18:04.371 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:05.452 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_05_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_05_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-27 20:18:05.452 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:08.460 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:11.466 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:11.468 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-27 20:18:14.475 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.destroy:258 - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='BPC_VISA_BASE_FILE_33_GROUP', nameServer='mq.dev.kun:9876', topic='BPC_VISA_BASE_FILE_33_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-27 20:18:14.478 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:14.500 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.destroy:847 - Shutting down Quartz Scheduler
2025-07-27 20:18:14.501 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-27 20:18:14.501 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-27 20:18:14.502 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.shutdown:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-27 20:18:14.509 [Thread-126] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.run:91 - >>>>>>>>>>> xxl-job remoting server stop.
2025-07-27 20:18:15.029 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:87 - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='kun-linkage-clearing', registryValue='http://192.168.0.106:18084/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-27 20:18:15.030 [xxl-job, executor ExecutorRegistryThread] INFO  [  ,  ] com.xxl.job.core.thread.ExecutorRegistryThread.run:105 - >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-27 20:18:15.030 [main] INFO  [  ,  ] com.xxl.job.core.server.EmbedServer.stop:117 - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-27 20:18:15.031 [xxl-job, executor JobLogFileCleanThread] INFO  [  ,  ] com.xxl.job.core.thread.JobLogFileCleanThread.run:99 - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
2025-07-27 20:18:15.032 [xxl-job, executor TriggerCallbackThread] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:98 - >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-27 20:18:15.032 [Thread-115] INFO  [  ,  ] com.xxl.job.core.thread.TriggerCallbackThread.run:128 - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-27 20:18:18.040 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:21.046 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:21.048 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-27 20:18:21.435 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:21.438 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:24.440 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-27 20:18:24.441 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-27 20:18:24.554 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-27 20:18:24.558 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-27 20:18:24.559 [main] INFO  [  ,  ] io.undertow.stop:259 - stopping server: Undertow - 2.2.28.Final
2025-07-27 20:18:24.586 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
