package com.kun.linkage.common.external.facade.api.kcard;


import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubRefundReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXQueryBalanceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXQueryBalanceRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * KCard转发PayX账户接口
 */
@FeignClient(name = "kcard-gateway", path = "/gateway/account/transaction/payx")
public interface KCardPayXAccountFacade {
    /**
     * PayX-集团内询价接口
     * @param req
     * @return
     */
    @RequestMapping("/payxExchangeRate")
    Result<PayXAskPriceRsp> payXExchangeRate(@RequestBody PayXAskPriceReq req);
    /**
     * PayX-集团内部产品扣账接口
     * @param req
     * @return
     */
    @RequestMapping("/payxDebitSub")
    Result<PayXDebitSubRsp> payXDebitSub(@RequestBody PayXDebitSubReq req);
    /**
     * PayX-撤销扣款接口-幂等
     * @param req
     * @return
     */
    @RequestMapping("/payxDebitSubRefund")
    Result<PayXDebitSubRefundRsp> payXDebitSubRefund(@RequestBody PayXDebitSubRefundReq req);

    /**
     * title: <br>
     * @description: PayX-查询子账户信息
     * Copyright: Copyright (c)2014<br>
     * Company: 易宝支付(YeePay)<br>
     *
     * <AUTHOR>
     * @version 1.0.0
     * @since 2025/7/4 11:07
     */
    @RequestMapping(value = "/payxQueryBalnace", method = RequestMethod.POST)
    Result<PayXQueryBalanceRsp> payxQueryBalnace(@RequestBody PayXQueryBalanceReq payXQueryBalanceReq);
}
