package com.kun.linkage.common.external.facade.api.kcard.enums;

public enum KunAndPayXDirectionEnum {
    TO_USER("TO_USER", "从集团账户扣到机构账户"),
    TO_GROUP("TO_GROUP", "从机构账户扣到集团账户");

    private final String direction;
    private final String desc;

    KunAndPayXDirectionEnum(String direction, String desc) {
        this.direction = direction;
        this.desc = desc;
    }

    public String getDirection() {
        return direction;
    }

    public String getDesc() {
        return desc;
    }
}
