package com.kun.linkage.common.external.facade.api.kcard.enums;

/**
 * Kun和PayX动账接口调用Remark枚举
 */
public enum KunAndPayXRemarkEnum {
    RECHARGE("KL Card Topup Transaction", "充值"),
    CANCEL_CARD("KL Card Cancel Transaction BPC-GW", "卡注销还款"),
    TOPUP_NEGATIVE_AMOUNT("KL Card Topup-Negative Amount BPC-GW", "负金额补扣"),
    CREATE_CARD_FEE("KL Create Card Fee BPC-GW", "商户开卡手续费扣除"),
    RECHARGE_CARD_FEE("KL Card Topup Fee", "商户充值手续费扣除"),
    ACCEPTANCE_FEE("KL Crypto Conversion Fee", "商户承兑费扣除"),
    TRANSACTION_FEE("KL Transaction Fee BPC-GW", "商户交易手续费扣除"),
    SETTLEMENT_FEE("KL Settlement Fee BPC-GW", "商户清算手续费扣除"),
    FX_MARKUP_FEE("KL FX Markup Fee BPC-GW", "商户FX Markup扣除"),
    CANCEL_CARD_FEE("KL Card Cancel Transaction Fee BPC-GW", "商户销卡手续费扣除"),
    TRANSACTION_BPC("KL Transaction BPC-GW", "交易")
    ;

    private final String remark;
    private final String desc;

    KunAndPayXRemarkEnum(String remark, String desc) {
        this.remark = remark;
        this.desc = desc;
    }

    public String getRemark() {
        return remark;
    }

    public String getDesc() {
        return desc;
    }
}
