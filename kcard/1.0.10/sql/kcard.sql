-- auto-generated definition
create table kc_auth_flow_template
(
    id                                        bigint auto_increment comment '自增主键 id'
        primary key,
    channel                                   varchar(12)    not null comment '渠道类型 ',
    request_id                                varchar(64)    not null comment '请求流水号',
    card_id                                   varchar(64)    not null comment '卡ID',
    trans_type                                int            not null comment '交易类型',
    authorization_type                        int            not null comment '授权类型',
    accounting_direction                      int            null comment '财务方向',
    reference_no                              varchar(32)    null comment '参考号',
    trans_id                                  varchar(64)    not null comment '交易ID',
    original_trans_id                         varchar(64)    null comment '原交易ID',
    original_finish_time                      datetime       null comment '原交易完成时间 yyyy-MM-dd HH:mm:ss',
    original_finish_date                      date           null comment '原交易完成日期 yyyy-MM-dd',
    processing_code                           varchar(24)    null comment '处理码',
    primary_account_number                    varchar(32)    null comment '主账号',
    amount_account                            varchar(24) null comment '账户金额',
    retrieval_reference_number                varchar(32)    null comment '检索参考号',
    currency_code_cardholder_billing          char(3)        null comment '持卡人账单货币代码',
    currency_code_transaction                 char(3)        null comment '交易货币代码',
    conversion_rate_cardholder_billing        varchar(24) null comment '持卡人账单汇率',
    systems_trace_audit_number                varchar(16)    null comment '系统跟踪审计号',
    currency_code_account                     char(3)        null comment '账户货币代码',
    message_type                              char(4)        null comment '报文类型',
    account_identification                    varchar(32)    null comment '账户识别',
    terminal_output_capability                varchar(6)     null comment '终端输出能力',
    pin_capture_capability                    varchar(6)     null comment 'PIN采集能力',
    card_data_output_capability               varchar(6)     null comment '卡数据输出能力',
    cardholder_authentication_capability      varchar(6)     null comment '持卡人认证能力',
    cardholder_authentication_method          varchar(6)     null comment '持卡人认证方式',
    card_presence                             varchar(6)     null comment '卡片存在标志',
    operating_environmen                      varchar(6)     null comment '操作环境',
    card_capture_capabilit                    varchar(6)     null comment '卡片采集能力',
    card_data_input_capability                varchar(6)     null comment '卡数据输入能力',
    card_data_input_mode                      varchar(6)     null comment '卡数据输入方式',
    cardholder_presence_indicator             varchar(6)     null comment '持卡人存在标志',
    cardholder_authentication_entity          varchar(6)     null comment '持卡人认证主体',
    transaction_amount_account_currency       varchar(24)    null comment '账户货币金额',
    acquirer_fee_amount                       varchar(24)    null comment '收单行费用',
    fee_sign                                  char           null comment '费用符号',
    fee_currency                              char(3)        null comment '费用货币',
    fee_amoun                                 varchar(24)    null comment '费用金额',
    settlement_type                           char           null comment '清算类型',
    sender_reference_number                   varchar(24)    null comment '发送方参考号',
    customer_mobile_phone                     varchar(16)    null comment '客户手机号',
    svfe_transaction_type                     char(3)        null comment 'SVFE交易类型',
    card_type                                 char(2)        null comment '卡片类型',
    local_transaction_date_and_time           varchar(24)    null comment '本地交易时间',
    merchant_type                             char(4)        null comment '商户类型（mcc码）',
    acquier_institue_identifier               varchar(32)    null comment '收单机构代码',
    acquirer_country_code                     varchar(6)     null comment '收单机构国家代码',
    acquirer_network_identifier               varchar(12)    null comment '收单机构网络标识',
    processor_card_id                         varchar(32)    null comment 'BPC卡ID',
    customer_id                               varchar(32)    null comment '客户ID',
    fe_trace_number                           varchar(32)    null comment 'FE跟踪号',
    fe_transaction_date_and_time              varchar(24)    null comment 'FE交易时间',
    fe_transaction_number                     varchar(32)    null comment 'FE交易号',
    issuer_network_identifier                 varchar(12)    null comment '发卡机构网络标识',
    network_reference_data                    varchar(32)    null comment '网络参考数据',
    transaction_risk_score                    varchar(24)    null comment '交易风险评分',
    card_acceptor_identification_code         varchar(32)    null comment '卡受理机构标识',
    transmission_date_and_time                varchar(24)    null comment '传输日期时间',
    conversion_rate_account                   varchar(24)    null comment '账户汇率',
    amount_cardholder_billing                 varchar(24)    null comment '卡人账单金额',
    date_time_local_transaction               varchar(24)    null comment '本地交易日期时间',
    amount_transaction                        varchar(24)    null comment '交易金额',
    settlement_date                           varchar(24)    null comment '清算日期',
    card_acceptor_name                        varchar(128)   null comment '银行商户名',
    card_acceptor_city                        varchar(128)   null comment '银行商户所在城市',
    country_code                              varchar(6)     null comment '国家代码',
    acquiring_institution_identification_code varchar(24)    null comment '收单机构代码',
    svfe_issuer_institution_identifier        varchar(24)    null comment 'SVFE发卡机构标识',
    card_acceptor_terminal_identification     varchar(24)    null comment '终端标识',
    original_data                             json           null comment '原数据',
    auth_type                                 varchar(24)    null comment '授权类型 普通授权 预授权',
    auth_start_time                           datetime       null comment '授权开始时间',
    auth_end_time                             datetime       null comment '授权到期时间',
    markup_rate                               decimal(6, 4)  null comment 'markup比例',
    trans_amt                                 decimal(18, 3) null comment '交易金额 不含markup金额，不会变动',
    bill_amount                               decimal(18, 3) null comment '账单金额 不含markup金额，不会变动',
    bill_amount_with_markup                   decimal(18, 3) null comment '账单金额 含markup金额，不会变动',
    remain_auth_amt                           decimal(18, 3) null comment '交易币种金额汇总  在授权、授权追加时增加，撤销、退款、（清算）时减少，在撤销、退款时，判断交易币种金额是否超出，若超出，直接返回失败',
    remain_bill_amt                           decimal(18, 3) null comment '账单金额汇总（不含markup），在授权、授权追加时增加，撤销、清算时减少，（清算时比对这个金额是否超额清算）。退款时不受影响',
    remain_bill_amt_with_markup               decimal(18, 3) null comment '剩余账单总金额（含markup）在授权、授权追加时增加，撤销、退款、（清算）时减少，在撤销、退款时，若账单金额大于这个金额，则取表里这个金额进行撤销或者退款',
    remain_frozen_amt                         decimal(18, 3) null comment '剩余冻结金额 含markup的账单金额汇总，在授权、授权追加增加，撤销、清算时减少，这个字段用于在清算之后进行授权到期释放',
    system_mark                               varchar(6)     null comment '所属系统 VCC KL',
    member_id                                 bigint         null comment 'kcard商户编号',
    merchant_id                               varchar(32)    null comment 'CRMID',
    status                                    varchar(6)     null comment '状态 SUCC 成功  FAIL 失败',
    clear_status                              tinyint(1) default 0 not null comment '清算状态',
    release_status                            varchar(6) default 'WAIT' not null comment '额度释放状态 WAIT  待释放，SUCC 已释放，FAIL 释放失败',
    release_time                              datetime       null comment '额度释放时间',
    release_amt                               decimal(18, 3) null comment '额度释放金额',
    authorization_decision                    int            not null comment '授权决策：0 同意，1 拒绝',
    auth_completion_time                      datetime       null comment '授权完成时间',
    return_code                               varchar(12)    null comment '返回码',
    return_message                            varchar(128)   null comment '返回信息',
    approve_code                              char(6)        null comment '授权码',
    timeout_flag                              tinyint(1) default 0 not null comment '超时标识',
    create_date                               date           null comment '创建日期',
    create_time                               datetime       null comment '创建时间',
    update_time                               datetime       null comment '修改时间',
    unique KEY uniq_request_id (request_id),
    KEY idx_card_id (card_id),
    KEY idx_primary_account_number (primary_account_number),
    KEY idx_createTime (create_time),
    KEY idx_create_date (create_date)
)
    comment '卡核心回调通知记录表';

-- 基于 kc_auth_flow_template 创建 2025-05 至 2030-12 的月表
CREATE TABLE kc_auth_flow_202505 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202506 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202507 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202508 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202509 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202510 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202511 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202512 LIKE kc_auth_flow_template;

CREATE TABLE kc_auth_flow_202601 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202602 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202603 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202604 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202605 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202606 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202607 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202608 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202609 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202610 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202611 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202612 LIKE kc_auth_flow_template;

CREATE TABLE kc_auth_flow_202701 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202702 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202703 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202704 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202705 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202706 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202707 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202708 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202709 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202710 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202711 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202712 LIKE kc_auth_flow_template;

CREATE TABLE kc_auth_flow_202801 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202802 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202803 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202804 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202805 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202806 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202807 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202808 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202809 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202810 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202811 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202812 LIKE kc_auth_flow_template;

CREATE TABLE kc_auth_flow_202901 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202902 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202903 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202904 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202905 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202906 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202907 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202908 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202909 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202910 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202911 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_202912 LIKE kc_auth_flow_template;

CREATE TABLE kc_auth_flow_203001 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203002 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203003 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203004 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203005 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203006 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203007 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203008 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203009 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203010 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203011 LIKE kc_auth_flow_template;
CREATE TABLE kc_auth_flow_203012 LIKE kc_auth_flow_template;

create table kc_error_code_mapping
(
    id             bigint auto_increment comment '自增主键 id'
        primary key,
    channel        varchar(6)  null comment '系统标识 KCARD、VCC、KL',
    channel_code   varchar(12) not null comment '系统内部错误码',
    channel_msg    varchar(64) not null comment '系统内部错误信息',
    processor_code varchar(12) not null comment 'yeewallex错误码',
    f39_code       varchar(12) not null comment 'f39域错误码',
    create_time    datetime    not null comment '创建时间',
    UNIQUE KEY uniq_kcard_code (channel, channel_code)
)
    comment '错误码转换表';

create table kc_error_callback_info
(
    id                bigint auto_increment comment '自增主键 id'
        primary key,
    error_business_id varchar(64)                    not null comment '异常业务ID',
    status            varchar(6) default 'INIT' not null comment '状态 INTI 初始化 SUCC 处理成功',
    create_time       datetime                  null comment '创建时间',
    update_time       datetime                  null comment '修改时间'
)
    comment '回调异常记录表';


create table kc_ccy_info
(
    id              bigint auto_increment comment '自增主键 id'
        primary key,
    ccy_code        varchar(6)  not null comment '卡片code',
    ccy_description varchar(64) not null comment '币种描述',
    master_code     varchar(6)  null comment '万事达卡对应币种数字',
    visa_code       varchar(6)  null comment 'visa对应卡币种数字',
    exponent        int         not null comment '小数位精度',
    status          tinyint(1)  not null comment '状态 1-有效 0-无效',
    create_time     datetime    not null comment '创建时间'
)
    comment '币种信息表';




alter table `kc_card_apply_info` 
    add column `system` varchar(8) default null comment '来源系统:VCC;KL;' after `id`,
    add column `card_mode` varchar(4) default null comment '开卡模式:1:多次卡;2:单次卡;' after `card_name`,
    add column `card_type` varchar(4) default null comment '卡类型:1:非额度卡;2:额度卡;3:账户卡;' after `card_mode`,
    add column `card_scheme` varchar(16) default null comment '卡组' after `card_type`,
    add column `out_user_id` varchar(64) default null comment '外部用户id' after `merc_id`,
    add column `new_open_card_flag` tinyint(1) default 0 comment '新接口开卡标记:1:新接口;0或者空:老接口;' after `out_user_id`,
    add column `kcard_id_list` varchar(1024) default null comment 'kcard卡id列表' after `new_open_card_flag`,
    add column `channel_member_id` varchar(32) default null comment '渠道商户号' after `merc_id`,
    add column `card_holder_country_no` varchar(3) default null comment '持卡人数字国家码' after `card_scheme`,
    modify `total_fee` decimal(30,15) null comment '手续费总额',
    modify `fee_ccy` varchar(16) null comment '手续费币种',
    MODIFY COLUMN exp_date date NULL COMMENT '有效期' AFTER fee_ccy;


alter table `kc_card_info` 
	add column `system` varchar(8) default null comment '来源系统:VCC;KL;' after `id`,
	add column `card_mode` varchar(4) default null comment '开卡模式:1:多次卡;2:单次卡;' after `card_name`,
	add column `card_type` varchar(4) default null comment '卡类型:1:非额度卡;2:额度卡;3:账户卡;' after `card_mode`,
	add column `card_scheme` varchar(16) default null comment '卡组' after `card_type`,
	add column `out_user_id` varchar(64) default null comment '外部用户id' after `member_id`,
	add column `card_active_status` varchar(16) default 'ACTIVATED' comment '卡激活状态' after card_status,
	add column `channel_member_id` varchar(32) default null comment '渠道商户号' after `member_id`,
	add column `card_holder_country_no` varchar(3) default null comment '持卡人数字国家码' after `card_holder_last_name`;



CREATE TABLE `kc_transfer_out_record_info` (
                                               `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键 id',
                                               `global_trans_code` varchar(128) NOT NULL COMMENT '全局流水号',
                                               `request_no` varchar(128) NOT NULL COMMENT '请求号',
                                               `member_id` bigint NOT NULL COMMENT '发卡商ID',
                                               `transfer_out_acct_no` varchar(64) NOT NULL COMMENT '转出账号',
                                               `transfer_out_ccy` varchar(6) NOT NULL COMMENT '转出币种',
                                               `transfer_out_amt` decimal(30,15) NOT NULL COMMENT '转出金额',
                                               `transfer_out_time` datetime NOT NULL COMMENT '转出时间',
                                               `transfer_out_after_bal_amt` decimal(30,15) DEFAULT NULL COMMENT '转出后账户余额',
                                               `transfer_out_status` varchar(20) NOT NULL COMMENT '转出状态 TRANSFERRING-转出中 FAILED-转出失败 SUCCESS-转出成功',
                                               `transfer_out_error_code` varchar(255) DEFAULT NULL COMMENT '转出异常代码',
                                               `transfer_out_error_message` varchar(255) DEFAULT NULL COMMENT '转出异常信息',
                                               `swift_code` varchar(32) DEFAULT NULL COMMENT 'swiftCode',
                                               `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                               `update_user` varchar(64) DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `uniq_transfer_out_record_info_2` (`request_no`) USING BTREE COMMENT '请求号索引',
                                               KEY `idx_transfer_out_record_info_1` (`global_trans_code`) COMMENT '全局流水号索引'
) ENGINE=InnoDB COMMENT='卡转出记录表';


INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ALL', 'Albanian lek', '008', '008', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('DZD', 'Algerian dinar', '012', '012', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ARS', 'Argentine Peso', '032', '032', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AUD', 'Australian Dollar', '036', '036', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BSD', 'Bahamian dollar', '044', '044', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BHD', 'Bahraini Dinar', '048', '048', 3, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BDT', 'Bangladeshi Taka', '050', '050', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AMD', 'Armenian dram', '051', '051', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BBD', 'Barbados dollar', '052', '052', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BMD', 'Bermudian dollar', '060', '060', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BTN', 'Bhutanese ngultrum', '064', '064', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BOB', 'Bolivian Boliviano', '068', '068', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BWP', 'Botswana pula', '072', '072', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BZD', 'Belize dollar', '084', '084', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SBD', 'Solomon Islands dollar', '090', '090', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BND', 'Brunei Dollar', '096', '096', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MMK', 'Myanmar kyat', '104', '104', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BIF', 'Burundi franc', '108', '108', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KHR', 'Cambodia riel', '116', '116', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CAD', 'Canadian Dollar', '124', '124', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CVE', 'Cape Verde escudo', '132', '132', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KYD', 'Cayman Islands dollar', '136', '136', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LKR', 'Sri Lankan Rupee', '144', '144', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CLP', 'Chilean Peso', '152', '152', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CNY', 'Chinese Yuan', '156', '156', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CNH', 'Chinese Yuan', '157', '157', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('COP', 'Colombian Peso', '170', '170', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KMF', 'Comoro franc', '174', '174', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CRC', 'Costa Rican colon', '188', '188', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('HRK', 'Croatian Kuna', '191', '191', 2, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CUP', 'Peso', '192', '192', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CYP', 'Cypriot Pound', '196', '196', 2, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CZK', 'Czech Koruna', '203', '203', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('DKK', 'Danish Krone', '208', '208', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('DOP', 'Dominican peso', '214', '214', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SVC', 'EI Salvador colon', '222', '', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ETB', 'Ethiopian birr', '230', '230', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ERN', 'Eritrea', '', '232', 2, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('EEK', 'Estonia', '', '233', 2, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('FKP', 'Falkland Islands pound', '238', '238', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('FJD', 'Fiji dollar', '242', '242', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('DJF', 'Djibouti franc', '262', '262', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GMD', 'Gambia dalasi', '270', '270', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GIP', 'Gibraltar pound', '292', '292', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GTQ', 'Guatemala quetzal', '320', '320', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GNF', 'Guinea franc', '324', '324', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GYD', 'Guyana dollar', '328', '328', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('HTG', 'Haiti gourde', '332', '332', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('HNL', 'Hondura lempira', '340', '340', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('HKD', 'Hong Kong Dollar', '344', '344', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('HUF', 'Hungarian Forint', '348', '348', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ISK', 'Icelandic Krona', '352', '352', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('INR', 'Indian Rupee/Bhutan', '356', '356', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('IDR', 'Indonesian Rupiah', '360', '360', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('IRR', 'Iran(Islamic Republic of)', '', '364', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('IQD', 'Iraqi dinar', '368', '368', 3, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ILS', 'Israeli New Shekel', '376', '376', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('JMD', 'Jamaican dollar', '388', '388', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('JPY', 'Japanese Yen', '392', '392', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KZT', 'Kazakhstani Tenge', '398', '398', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('JOD', 'Jordanian Dinar', '400', '400', 3, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KES', 'Kenyan shiling', '404', '404', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KPW', 'Korea(the Democratic People''s Republic of)', '', '408', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KRW', 'South Korean Won', '410', '410', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KWD', 'Kuwaiti Dinar', '414', '414', 3, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('KGS', 'Kyrgyz Som', '417', '417', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LAK', 'Lao Kip', '418', '418', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LBP', 'Lebanese pound', '422', '422', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LSL', 'Loti', '426', '', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LVL', 'Latvia', '', '428', 2, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LRD', 'Liberian dollar', '430', '430', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('LYD', 'Libyan dinar', '', '434', 3, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MOP', 'Macanese Pataca', '446', '446', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MWK', 'Kwacha', '454', '454', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MYR', 'Malaysian Ringgit', '458', '458', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MVR', 'Rufiyaa', '462', '462', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MUR', 'Mauritian Rupee', '480', '480', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MXN', 'Mexican Peso', '484', '484', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MNT', 'Mongolia tugrik', '496', '496', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MDL', 'Moldovan leu', '498', '498', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MAD', 'Moroccan Dirham', '504', '504', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('OMR', 'Omani Rial', '512', '512', 3, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('NAD', 'Namibia dollar', '516', '516', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('NPR', 'Nepalese rupee', '524', '524', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ANG', 'Netherlands Antilian guilder', '532', '532', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AWG', 'Guilder', '533', '533', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('VUV', 'Vanuatu vatu', '548', '548', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('NZD', 'New Zealand Dollar', '554', '554', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('NIO', 'Nicarag cordobaoro', '558', '558', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('NGN', 'Nigerian naira', '566', '566', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('NOK', 'Norwegian Krone', '578', '578', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PKR', 'Pakistani Rupee', '586', '586', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PAB', 'Panamanian Balboa', '590', '590', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PGK', 'Papua New Guinean Kina', '598', '598', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PYG', 'Paraguay guarani', '600', '600', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PEN', 'Peruvian Nuevo Sol', '604', '604', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PHP', 'Philippine Peso', '608', '608', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('QAR', 'Qatari Rial', '634', '634', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('RUB', 'Russian Ruble', '643', '643', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('RWF', 'Rwanda franc', '646', '646', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SHP', 'St. Helena pound', '654', '654', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SAR', 'Saudi Riyal', '682', '682', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SCR', 'Seychelles rupee', '690', '690', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SGD', 'Singapore Dollar', '702', '702', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SSK', 'Slovakia', '', '703', 2, 0, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('VND', 'Vietnamese Dong', '704', '704', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SOS', 'Somali Shilling', '706', '706', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ZAR', 'South African Rand', '710', '710', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SSP', 'South Sudan pound', '728', '728', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SZL', 'Swaziland lilangeni', '748', '748', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SEK', 'Swedish Krona', '752', '752', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CHF', 'Swiss Franc', '756', '756', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SYP', 'Syrian Arab Republic(the)', '', '760', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('THB', 'Thai Baht', '764', '764', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TOP', 'Tonga paanga', '776', '776', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TTD', 'Trinidad and Tobago dollar', '780', '780', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AED', 'United Arab Emirates Dirham', '784', '784', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TND', 'Tunisian dinar', '788', '788', 3, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('UGX', 'Ugandan shilling', '800', '800', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MKD', 'Macedonian denar', '807', '807', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('EGP', 'Egyptian Pound', '818', '818', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GBP', 'British Pound Sterling', '826', '826', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TZS', 'Tanzanian shilling', '834', '834', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('USD', 'United States Dollar', '840', '840', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('UYU', 'Uruguayan Peso', '858', '858', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('UZS', 'Uzbekistan sum', '860', '860', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('WST', 'Samoatala', '882', '882', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('YER', 'Yemenirial', '886', '886', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TWD', 'New Taiwan Dollar', '901', '901', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SLL', 'Sierra Leone', '694', '925', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('VES', 'Venezuelan Bolívar', '928', '928', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MRU', 'Mauritanian ouguiya', '929', '929', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('STN', 'Sao Tome and Principe dobra', '930', '930', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ZWL', 'Zimbabwe dollar', '932', '932', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BYN', 'Belarussian ruble', '933', '933', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TMT', 'Turkmenis tanmanat', '934', '934', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GHS', 'Cedi', '936', '936', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SDG', 'Sudan(the)', '', '938', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('RSD', 'Serbian Dinar', '941', '941', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MZN', 'Metical', '943', '943', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AZN', 'Azerbaijanian manat', '944', '944', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('RON', 'Romanian leu', '946', '946', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TRY', 'Turkish Lira', '949', '949', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('XAF', 'CFA franc BEAC', '950', '950', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('XCD', 'East Caribbean dollar', '951', '951', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('XOF', 'CFA franc BCEAO', '952', '952', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('XPF', 'CFP Franc', '953', '953', 0, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('ZMW', 'Zambian kwacha', '967', '967', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('SRD', 'Suriname dollar', '968', '968', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('MGA', 'Malagasyariary', '969', '969', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AFN', 'Afghanistan afghani', '971', '971', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('TJS', 'Tajikistan somoni', '972', '972', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('AOA', 'Angola kwanza', '973', '973', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BYN', 'Belarus', '933', '974', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BGN', 'Bulgarian Lev', '975', '975', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('CDF', 'Congolese franc', '976', '976', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BAM', 'Bosnia and Herzegovina Convertible Mark', '977', '977', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('EUR', 'Euro', '978', '978', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('UAH', 'Ukrainian Hryvnia', '980', '980', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('GEL', 'Georgian Lari', '981', '981', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('PLN', 'Polish Zloty', '985', '985', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('BRL', 'Brazilian Real', '986', '986', 2, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('USDC', 'USDC', 'USDC', 'USDC', 8, 1, NOW());
INSERT INTO kc_ccy_info (ccy_code, ccy_description, master_code, visa_code, exponent, status, create_time) VALUES ('USDT', 'USDT', 'USDT', 'USDT', 8, 1, NOW());

INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '0000', 'Success', '0000', '000', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1001', 'Illegal Request', '1007', '909', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1002', 'Duplicated Request', '1007', '913', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1003', 'Can''t match the original transaction', '9999', '940', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1004', 'Merchant is not existed', '1004', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1005', 'Merchant is not active', '2000', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1006', 'Card is not active', '2021', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1007', 'Insuffient available balance', '2002', '116', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1008', 'Card is blocked', '2021', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1009', 'Card is cancelled', '2021', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1010', 'Invalid Card', '2021', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1011', 'Card is expired', '2021', '112', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '1012', 'The currency does not exist', '9999', '907', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '2001', 'Restricted transactions', '9999', '953', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '2002', 'Insufficient cancellation amount', '9999', '953', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '2003', 'Insufficient refund amount', '9999', '953', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '2004', 'Request timeout', '9999', '953', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '2000', 'Exceed limit', '9999', '917', NOW());
INSERT INTO kc_error_code_mapping (channel, channel_code, channel_msg, processor_code, f39_code, create_time) VALUES ('BPC-GW', '9999', 'System Error', '9999', '907', NOW());

-- 通道基础信息增加引用商户编号
alter table kc_channel_base_info
    add column reference_member_id varchar(32) null comment '引用商户编号' AFTER sys_fee_type;

-- 商户主表增加引用商户编号
alter table kc_kyc_main_info
    add column reference_member_id varchar(32) null comment '引用商户编号' AFTER register_region;

-- MCC表增加释放天数配置
ALTER TABLE kc_mcc_info
    ADD COLUMN release_days VARCHAR(16) DEFAULT '7' COMMENT '释放天数（7，15，30）' AFTER pre_auth_status;

-- 删除通道币种表，渠道ID字段的唯一索引
ALTER TABLE kc_channel_ccy_info DROP INDEX uniq_channel_id;

-- 清理MCC表数据
delete from kc_mcc_info where id > 0;

-- MCC表初始化
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (1, '0742', 'Veterinary Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:58:56', 'system', '2025-06-04 17:58:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (2, '0763', 'Agricultural Cooperatives', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:58:58', 'system', '2025-06-04 17:58:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (3, '0780', 'Horticultural and Landscaping Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:58:59', 'system', '2025-06-04 17:58:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (4, '1520', 'Retail Contractors–Residential and Commercial', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:00', 'system', '2025-06-04 17:59:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (5, '1711', 'Air Conditioning, Heating and Plumbing Contractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:00', 'system', '2025-06-04 17:59:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (6, '1731', 'Electrical Contractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:01', 'system', '2025-06-04 17:59:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (7, '1740', 'Insulation, Masonry, Plastering, Stonework and Tile Setting Contractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:02', 'system', '2025-06-04 17:59:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (8, '1750', 'Carpentry Contractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:02', 'system', '2025-06-04 17:59:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (9, '1761', 'Roofing and Siding, Sheet Metal Work Contractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:03', 'system', '2025-06-04 17:59:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (10, '1771', 'Concrete Work Contractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:04', 'system', '2025-06-04 17:59:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (11, '1799', 'Contractors, Special Trade Contractors–not elsewhere classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:04', 'system', '2025-06-04 17:59:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (12, '2741', 'Miscellaneous Publishing and Printing', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:05', 'system', '2025-06-04 17:59:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (13, '2791', 'Typesetting, Plate Making and Related Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:06', 'system', '2025-06-04 17:59:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (14, '2842', 'Sanitation, Polishing and Specialty Cleaning Preparations', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:07', 'system', '2025-06-04 17:59:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (15, '3000', 'United Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:07', 'system', '2025-06-04 17:59:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (16, '3001', 'American Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:08', 'system', '2025-06-04 17:59:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (17, '3002', 'Pan American', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:09', 'system', '2025-06-04 17:59:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (18, '3003', 'Eurofly Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:09', 'system', '2025-06-04 17:59:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (19, '3004', 'Dragon Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:10', 'system', '2025-06-04 17:59:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (20, '3005', 'British Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:11', 'system', '2025-06-04 17:59:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (21, '3006', 'Japan Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:12', 'system', '2025-06-04 17:59:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (22, '3007', 'Air France', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:12', 'system', '2025-06-04 17:59:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (23, '3008', 'Lufthansa German Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:13', 'system', '2025-06-04 17:59:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (24, '3009', 'Air Canada', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:14', 'system', '2025-06-04 17:59:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (25, '3010', 'Royal Dutch Airlines (KLM Airlines)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:14', 'system', '2025-06-04 17:59:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (26, '3011', 'Aeroflot', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:15', 'system', '2025-06-04 17:59:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (27, '3012', 'Qantas', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:16', 'system', '2025-06-04 17:59:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (28, '3013', 'ITA Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:17', 'system', '2025-06-04 17:59:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (29, '3014', 'Saudi Arabian Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:17', 'system', '2025-06-04 17:59:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (30, '3015', 'Swiss International Air Lines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:18', 'system', '2025-06-04 17:59:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (31, '3016', 'Scandinavian Airline System (SAS)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:19', 'system', '2025-06-04 17:59:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (32, '3017', 'South African Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:19', 'system', '2025-06-04 17:59:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (33, '3018', 'VARIG', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:20', 'system', '2025-06-04 17:59:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (34, '3019', 'EASTERN AIRLINES', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:21', 'system', '2025-06-04 17:59:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (35, '3020', 'Air India', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:21', 'system', '2025-06-04 17:59:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (36, '3021', 'Air Algerie', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:22', 'system', '2025-06-04 17:59:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (37, '3022', 'Philippine Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:23', 'system', '2025-06-04 17:59:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (38, '3023', 'Mexicana', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:23', 'system', '2025-06-04 17:59:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (39, '3024', 'Pakistan International', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:24', 'system', '2025-06-04 17:59:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (40, '3025', 'Air New Zealand', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:25', 'system', '2025-06-04 17:59:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (41, '3026', 'Emirates Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:26', 'system', '2025-06-04 17:59:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (42, '3027', 'UTA/interair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:26', 'system', '2025-06-04 17:59:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (43, '3028', 'Air Malta', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:27', 'system', '2025-06-04 17:59:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (44, '3029', 'SN Brussels Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:28', 'system', '2025-06-04 17:59:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (45, '3030', 'Aerolineas Argentinas', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:28', 'system', '2025-06-04 17:59:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (46, '3031', 'Olympic Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:29', 'system', '2025-06-04 17:59:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (47, '3032', 'EL AL', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:30', 'system', '2025-06-04 17:59:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (48, '3033', 'Ansett Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:30', 'system', '2025-06-04 17:59:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (49, '3034', 'Etihadair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:31', 'system', '2025-06-04 17:59:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (50, '3035', 'TAP (Portugal)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:32', 'system', '2025-06-04 17:59:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (51, '3036', 'VASP (BRAZIL)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:33', 'system', '2025-06-04 17:59:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (52, '3037', 'Egyptair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:33', 'system', '2025-06-04 17:59:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (53, '3038', 'Kuwait Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:34', 'system', '2025-06-04 17:59:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (54, '3039', 'Avianca', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:35', 'system', '2025-06-04 17:59:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (55, '3040', 'Gulf Air (Bahrain)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:35', 'system', '2025-06-04 17:59:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (56, '3041', 'Balkan-Bulgarian Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:36', 'system', '2025-06-04 17:59:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (57, '3042', 'Finnair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:37', 'system', '2025-06-04 17:59:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (58, '3043', 'Aer Lingus', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:37', 'system', '2025-06-04 17:59:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (59, '3044', 'Air Lanka', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:38', 'system', '2025-06-04 17:59:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (60, '3045', 'Nigeria Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:39', 'system', '2025-06-04 17:59:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (61, '3046', 'Cruzeiro Do Sul', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:39', 'system', '2025-06-04 17:59:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (62, '3047', 'Turk Hava Yollari', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:40', 'system', '2025-06-04 17:59:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (63, '3048', 'Royal Air Maroc', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:41', 'system', '2025-06-04 17:59:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (64, '3049', 'Tunis Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:42', 'system', '2025-06-04 17:59:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (65, '3050', 'Icelandair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:42', 'system', '2025-06-04 17:59:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (66, '3051', 'Austrian Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:43', 'system', '2025-06-04 17:59:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (67, '3052', 'LAN Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:44', 'system', '2025-06-04 17:59:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (68, '3053', 'AVIACO (SPAIN)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:44', 'system', '2025-06-04 17:59:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (69, '3054', 'LADECO (CHILE)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:45', 'system', '2025-06-04 17:59:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (70, '3055', 'LAB (BOLIVIA)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:46', 'system', '2025-06-04 17:59:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (71, '3056', 'Jet Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:46', 'system', '2025-06-04 17:59:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (72, '3057', 'Virgin America', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:47', 'system', '2025-06-04 17:59:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (73, '3058', 'Delta', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:48', 'system', '2025-06-04 17:59:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (74, '3059', 'DBA Airline', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:49', 'system', '2025-06-04 17:59:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (75, '3060', 'Northwest Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:49', 'system', '2025-06-04 17:59:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (76, '3061', 'Continental', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:50', 'system', '2025-06-04 17:59:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (77, '3062', 'Hapag-Lloyd Express', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:51', 'system', '2025-06-04 17:59:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (78, '3063', 'US Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:51', 'system', '2025-06-04 17:59:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (79, '3064', 'Adria Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:52', 'system', '2025-06-04 17:59:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (80, '3065', 'Airinter', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:53', 'system', '2025-06-04 17:59:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (81, '3066', 'Southwest Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:53', 'system', '2025-06-04 17:59:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (82, '3067', 'Vanguard Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:54', 'system', '2025-06-04 17:59:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (83, '3068', 'Air Astana', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:55', 'system', '2025-06-04 17:59:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (84, '3069', 'Sun Country Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:56', 'system', '2025-06-04 17:59:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (85, '3070', 'Fly Dubai', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:56', 'system', '2025-06-04 17:59:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (86, '3071', 'Air British Columbia', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:57', 'system', '2025-06-04 17:59:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (87, '3072', 'Cebu Pacific Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:58', 'system', '2025-06-04 17:59:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (88, '3075', 'Singapore Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:58', 'system', '2025-06-04 17:59:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (89, '3076', 'Aeromexico', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 17:59:59', 'system', '2025-06-04 17:59:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (90, '3077', 'Thai Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:00', 'system', '2025-06-04 18:00:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (91, '3078', 'China Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:01', 'system', '2025-06-04 18:00:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (92, '3079', 'Jetstar Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:01', 'system', '2025-06-04 18:00:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (93, '3080', 'SWOOP INC', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:02', 'system', '2025-06-04 18:00:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (94, '3081', 'XIAMEN Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:03', 'system', '2025-06-04 18:00:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (95, '3082', 'Korean Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:04', 'system', '2025-06-04 18:00:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (96, '3083', 'Air Afrique', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:04', 'system', '2025-06-04 18:00:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (97, '3084', 'Eva Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:05', 'system', '2025-06-04 18:00:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (98, '3085', 'Midwest Express Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:06', 'system', '2025-06-04 18:00:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (99, '3086', 'Carnival Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:06', 'system', '2025-06-04 18:00:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (100, '3087', 'METRO Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:07', 'system', '2025-06-04 18:00:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (101, '3088', 'Croatia Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:08', 'system', '2025-06-04 18:00:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (102, '3089', 'Transaero', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:09', 'system', '2025-06-04 18:00:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (103, '3090', 'Uni Airways Corporation', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:09', 'system', '2025-06-04 18:00:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (104, '3094', 'Zambia Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:10', 'system', '2025-06-04 18:00:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (105, '3095', 'WARDAIR (CANADA)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:11', 'system', '2025-06-04 18:00:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (106, '3096', 'Air Zimbabwe Corp', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:11', 'system', '2025-06-04 18:00:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (107, '3097', 'Spanair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:12', 'system', '2025-06-04 18:00:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (108, '3098', 'Asiana Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:13', 'system', '2025-06-04 18:00:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (109, '3099', 'Cathay Pacific', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:14', 'system', '2025-06-04 18:00:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (110, '3100', 'Malaysian Airline System', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:14', 'system', '2025-06-04 18:00:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (111, '3102', 'Iberia', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:15', 'system', '2025-06-04 18:00:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (112, '3103', 'Garuda', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:16', 'system', '2025-06-04 18:00:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (113, '3106', 'Braathens S.A.F.E.', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:16', 'system', '2025-06-04 18:00:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (114, '3110', 'Wings Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:17', 'system', '2025-06-04 18:00:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (115, '3111', 'British Midland', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:18', 'system', '2025-06-04 18:00:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (116, '3112', 'Windward Island', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:18', 'system', '2025-06-04 18:00:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (117, '3115', 'Tower Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:19', 'system', '2025-06-04 18:00:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (118, '3117', 'Venezolana International De Aviacion', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:20', 'system', '2025-06-04 18:00:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (119, '3118', 'Valley Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:20', 'system', '2025-06-04 18:00:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (120, '3125', 'Tan Sahsa', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:21', 'system', '2025-06-04 18:00:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (121, '3126', 'Talair PTY Ltd.', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:22', 'system', '2025-06-04 18:00:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (122, '3127', 'Taca International', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:23', 'system', '2025-06-04 18:00:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (123, '3129', 'Surinam Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:23', 'system', '2025-06-04 18:00:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (124, '3130', 'Sunworld International', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:24', 'system', '2025-06-04 18:00:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (125, '3131', 'VLM Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:25', 'system', '2025-06-04 18:00:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (126, '3132', 'Frontier Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:25', 'system', '2025-06-04 18:00:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (127, '3133', 'Sunbelt Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:26', 'system', '2025-06-04 18:00:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (128, '3135', 'Sudan Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:27', 'system', '2025-06-04 18:00:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (129, '3136', 'Qatar Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:27', 'system', '2025-06-04 18:00:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (130, '3137', 'Singleton', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:28', 'system', '2025-06-04 18:00:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (131, '3138', 'Simmons Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:29', 'system', '2025-06-04 18:00:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (132, '3143', 'Scenic Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:29', 'system', '2025-06-04 18:00:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (133, '3144', 'Virgin Atlantic', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:30', 'system', '2025-06-04 18:00:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (134, '3145', 'San Juan', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:31', 'system', '2025-06-04 18:00:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (135, '3146', 'Luxair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:32', 'system', '2025-06-04 18:00:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (136, '3148', 'Air Littoral S.A.', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:32', 'system', '2025-06-04 18:00:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (137, '3151', 'Air Zaire', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:33', 'system', '2025-06-04 18:00:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (138, '3154', 'Princeville', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:34', 'system', '2025-06-04 18:00:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (139, '3156', 'Go Fly Ltd.', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:34', 'system', '2025-06-04 18:00:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (140, '3159', 'Provincetown-Boston Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:35', 'system', '2025-06-04 18:00:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (141, '3161', 'All Nipon Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:36', 'system', '2025-06-04 18:00:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (142, '3164', 'Norontair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:37', 'system', '2025-06-04 18:00:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (143, '3165', 'New York Helicopter', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:37', 'system', '2025-06-04 18:00:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (144, '3167', 'Aero Continente', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:38', 'system', '2025-06-04 18:00:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (145, '3168', 'Hainan Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:39', 'system', '2025-06-04 18:00:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (146, '3170', 'Mount Cook AIR', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:39', 'system', '2025-06-04 18:00:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (147, '3171', 'Canadian Airlines International', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:40', 'system', '2025-06-04 18:00:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (148, '3172', 'Nationair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:41', 'system', '2025-06-04 18:00:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (149, '3174', 'Jetblue Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:41', 'system', '2025-06-04 18:00:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (150, '3175', 'Middle East Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:42', 'system', '2025-06-04 18:00:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (151, '3176', 'Metroflight Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:43', 'system', '2025-06-04 18:00:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (152, '3177', 'AirTran Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:43', 'system', '2025-06-04 18:00:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (153, '3178', 'Mesa Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:44', 'system', '2025-06-04 18:00:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (154, '3180', 'Westjet Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:45', 'system', '2025-06-04 18:00:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (155, '3181', 'Malev', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:46', 'system', '2025-06-04 18:00:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (156, '3182', 'LOT (POLAND)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:46', 'system', '2025-06-04 18:00:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (157, '3183', 'Oman Aviation Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:47', 'system', '2025-06-04 18:00:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (158, '3184', 'LIAT', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:48', 'system', '2025-06-04 18:00:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (159, '3185', 'LAV (Venezuela)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:48', 'system', '2025-06-04 18:00:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (160, '3186', 'LAP (Paraguay)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:49', 'system', '2025-06-04 18:00:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (161, '3187', 'LACSA (Costa Rica)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:50', 'system', '2025-06-04 18:00:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (162, '3188', 'Virgin Express', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:50', 'system', '2025-06-04 18:00:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (163, '3190', 'Jugoslav Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:51', 'system', '2025-06-04 18:00:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (164, '3191', 'Island Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:52', 'system', '2025-06-04 18:00:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (165, '3193', 'Indian Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:53', 'system', '2025-06-04 18:00:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (166, '3196', 'Hawaiian Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:53', 'system', '2025-06-04 18:00:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (167, '3197', 'Havasu Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:54', 'system', '2025-06-04 18:00:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (168, '3200', 'Guyana Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:55', 'system', '2025-06-04 18:00:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (169, '3203', 'Golden Pacific Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:55', 'system', '2025-06-04 18:00:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (170, '3204', 'Freedom Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:56', 'system', '2025-06-04 18:00:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (171, '3206', 'China Eastern Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:57', 'system', '2025-06-04 18:00:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (172, '3211', 'Norwegian Air Shuttle', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:58', 'system', '2025-06-04 18:00:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (173, '3212', 'Dominicana De Aviacion', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:58', 'system', '2025-06-04 18:00:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (174, '3213', 'Malmo Aviation', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:00:59', 'system', '2025-06-04 18:00:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (175, '3215', 'Dan Air Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:00', 'system', '2025-06-04 18:01:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (176, '3216', 'Cumberland Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:00', 'system', '2025-06-04 18:01:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (177, '3217', 'Ceskoslovenske Aerolinie (CSA)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:01', 'system', '2025-06-04 18:01:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (178, '3218', 'Crown Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:02', 'system', '2025-06-04 18:01:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (179, '3219', 'Compania Panamena De Aviacion (COPA)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:03', 'system', '2025-06-04 18:01:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (180, '3220', 'Compania Faucett', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:03', 'system', '2025-06-04 18:01:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (181, '3221', 'Transportes Aeros Militares', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:04', 'system', '2025-06-04 18:01:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (182, '3222', 'Command Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:05', 'system', '2025-06-04 18:01:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (183, '3223', 'Comair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:05', 'system', '2025-06-04 18:01:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (184, '3226', 'Skyways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:06', 'system', '2025-06-04 18:01:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (185, '3228', 'Cayman Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:07', 'system', '2025-06-04 18:01:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (186, '3229', 'SAETA (Sociedad Ecuatorianos De Transportes Aereos)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:07', 'system', '2025-06-04 18:01:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (187, '3231', 'SAHSA (Servicio Aero De Honduras)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:08', 'system', '2025-06-04 18:01:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (188, '3233', 'Capitol Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:09', 'system', '2025-06-04 18:01:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (189, '3234', 'Caribbean Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:10', 'system', '2025-06-04 18:01:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (190, '3235', 'Brockway Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:10', 'system', '2025-06-04 18:01:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (191, '3236', 'Air Arabia Airline', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:11', 'system', '2025-06-04 18:01:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (192, '3238', 'Bemidji Aviation', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:12', 'system', '2025-06-04 18:01:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (193, '3239', 'Bar Harbor Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:12', 'system', '2025-06-04 18:01:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (194, '3240', 'Bahamasair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:13', 'system', '2025-06-04 18:01:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (195, '3241', 'Aviateca', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:14', 'system', '2025-06-04 18:01:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (196, '3242', 'Avensa', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:14', 'system', '2025-06-04 18:01:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (197, '3243', 'Austrian Air Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:15', 'system', '2025-06-04 18:01:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (198, '3245', 'Easyjet Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:16', 'system', '2025-06-04 18:01:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (199, '3246', 'Ryanair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:17', 'system', '2025-06-04 18:01:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (200, '3247', 'Gol Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:17', 'system', '2025-06-04 18:01:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (201, '3248', 'Tam Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:18', 'system', '2025-06-04 18:01:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (202, '3251', 'Aloha Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:19', 'system', '2025-06-04 18:01:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (203, '3253', 'AMERICA WEST', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:19', 'system', '2025-06-04 18:01:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (204, '3252', 'Antilean Airlines (ALM)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:20', 'system', '2025-06-04 18:01:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (205, '3254', 'U.S. Air Shuttle', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:21', 'system', '2025-06-04 18:01:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (206, '3256', 'Alaska Airlines Inc.', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:21', 'system', '2025-06-04 18:01:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (207, '3259', 'American Trans Air', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:22', 'system', '2025-06-04 18:01:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (208, '3260', 'Spirit Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:23', 'system', '2025-06-04 18:01:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (209, '3261', 'Air China', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:24', 'system', '2025-06-04 18:01:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (210, '3262', 'Reno Air V RENO AIR', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:24', 'system', '2025-06-04 18:01:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (211, '3263', 'Aero Servicio Carabobo', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:25', 'system', '2025-06-04 18:01:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (212, '3266', 'Air Seychelles', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:26', 'system', '2025-06-04 18:01:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (213, '3267', 'Air Panama International', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:26', 'system', '2025-06-04 18:01:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (214, '3280', 'Air Jamaica', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:27', 'system', '2025-06-04 18:01:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (215, '3282', 'Air Djibouti', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:28', 'system', '2025-06-04 18:01:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (216, '3284', 'Aero Virgin Islands', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:28', 'system', '2025-06-04 18:01:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (217, '3285', 'Aero Peru', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:29', 'system', '2025-06-04 18:01:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (218, '3286', 'Aero Nicaraguensis', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:30', 'system', '2025-06-04 18:01:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (219, '3287', 'Aero Coach Aviation', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:31', 'system', '2025-06-04 18:01:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (220, '3292', 'Cyprus Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:31', 'system', '2025-06-04 18:01:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (221, '3293', 'Ecuatoriana', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:32', 'system', '2025-06-04 18:01:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (222, '3294', 'Ethiopian Airlines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:33', 'system', '2025-06-04 18:01:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (223, '3295', 'Kenya Airways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:33', 'system', '2025-06-04 18:01:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (224, '3296', 'Air Berlin', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:34', 'system', '2025-06-04 18:01:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (225, '3297', 'Tarom Romanian Air Transport', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:35', 'system', '2025-06-04 18:01:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (226, '3298', 'Air Mauritius', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:35', 'system', '2025-06-04 18:01:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (227, '3299', 'Wideroes Flyveselskap', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:36', 'system', '2025-06-04 18:01:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (228, '3300', 'AZUL AIR', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:37', 'system', '2025-06-04 18:01:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (229, '3301', 'WIZZ AIRLINES', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:38', 'system', '2025-06-04 18:01:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (230, '3302', 'FLYBE LTD', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:38', 'system', '2025-06-04 18:01:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (231, '3303', 'TIGERAIR', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:39', 'system', '2025-06-04 18:01:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (232, '3308', 'CHINA SOUTHERN AIRLINES', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:01:40', 'system', '2025-06-04 18:01:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (233, '3351', 'Affiliated Auto Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:40', 'system', '2025-06-04 18:01:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (234, '3352', 'American International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:41', 'system', '2025-06-04 18:01:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (235, '3353', 'Brooks Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:42', 'system', '2025-06-04 18:01:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (236, '3354', 'Action Auto Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:42', 'system', '2025-06-04 18:01:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (237, '3355', 'Sixt Car Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:43', 'system', '2025-06-04 18:01:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (238, '3357', 'Hertz', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:44', 'system', '2025-06-04 18:01:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (239, '3359', 'Payless Car Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:45', 'system', '2025-06-04 18:01:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (240, '3360', 'Snappy Car Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:45', 'system', '2025-06-04 18:01:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (241, '3361', 'Airways', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:46', 'system', '2025-06-04 18:01:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (242, '3362', 'Altra Auto Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:47', 'system', '2025-06-04 18:01:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (243, '3364', 'Agency Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:47', 'system', '2025-06-04 18:01:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (244, '3366', 'Budget Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:48', 'system', '2025-06-04 18:01:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (245, '3368', 'Holiday Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:49', 'system', '2025-06-04 18:01:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (246, '3370', 'Rent-A-Wreck', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:49', 'system', '2025-06-04 18:01:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (247, '3374', 'Accent Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:50', 'system', '2025-06-04 18:01:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (248, '3376', 'Ajax Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:51', 'system', '2025-06-04 18:01:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (249, '3380', 'Triangle Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:52', 'system', '2025-06-04 18:01:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (250, '3381', 'Europcar', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:52', 'system', '2025-06-04 18:01:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (251, '3385', 'Tropical Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:53', 'system', '2025-06-04 18:01:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (252, '3386', 'Showcase Rental Cars', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:54', 'system', '2025-06-04 18:01:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (253, '3387', 'Alamo Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:54', 'system', '2025-06-04 18:01:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (254, '3388', 'Merchants Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:55', 'system', '2025-06-04 18:01:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (255, '3389', 'Avis Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:56', 'system', '2025-06-04 18:01:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (256, '3390', 'Dollar Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:56', 'system', '2025-06-04 18:01:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (257, '3391', 'Europe By Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:57', 'system', '2025-06-04 18:01:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (258, '3393', 'National Car Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:58', 'system', '2025-06-04 18:01:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (259, '3394', 'Kemwell Group', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:59', 'system', '2025-06-04 18:01:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (260, '3395', 'Thrifty Car Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:01:59', 'system', '2025-06-04 18:01:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (261, '3396', 'Tilden Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:00', 'system', '2025-06-04 18:02:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (262, '3398', 'Econo-Car Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:01', 'system', '2025-06-04 18:02:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (263, '3400', 'Auto Host Rental Cars', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:01', 'system', '2025-06-04 18:02:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (264, '3405', 'Enterprise Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:02', 'system', '2025-06-04 18:02:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (265, '3409', 'Retail Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:03', 'system', '2025-06-04 18:02:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (266, '3412', 'A1 Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:04', 'system', '2025-06-04 18:02:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (267, '3414', 'Godfrey National', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:02:04', 'system', '2025-06-04 18:02:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (268, '3420', 'Ansa International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:05', 'system', '2025-06-04 18:02:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (269, '3421', 'Allstate', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:06', 'system', '2025-06-04 18:02:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (270, '3423', 'AVCAR', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:07', 'system', '2025-06-04 18:02:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (271, '3425', 'Automate', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:07', 'system', '2025-06-04 18:02:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (272, '3427', 'Avon Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:08', 'system', '2025-06-04 18:02:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (273, '3428', 'Carey', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:09', 'system', '2025-06-04 18:02:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (274, '3429', 'Insurance Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:09', 'system', '2025-06-04 18:02:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (275, '3430', 'Major Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:10', 'system', '2025-06-04 18:02:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (276, '3431', 'Replacement Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:11', 'system', '2025-06-04 18:02:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (277, '3432', 'Reserve Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:12', 'system', '2025-06-04 18:02:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (278, '3433', 'Ugly Duckling Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:12', 'system', '2025-06-04 18:02:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (279, '3434', 'USA(Car Rental)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:13', 'system', '2025-06-04 18:02:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (280, '3435', 'Value Rent-A-Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:14', 'system', '2025-06-04 18:02:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (281, '3436', 'Autohansa', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:14', 'system', '2025-06-04 18:02:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (282, '3437', 'Cite', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:02:15', 'system', '2025-06-04 18:02:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (283, '3438', 'Interent', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:16', 'system', '2025-06-04 18:02:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (284, '3439', 'Milleville', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:17', 'system', '2025-06-04 18:02:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (285, '3441', 'Advantage Rent A Car', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:17', 'system', '2025-06-04 18:02:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (286, '3501', 'Holiday Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:18', 'system', '2025-06-04 18:02:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (287, '3502', 'Best Western', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:19', 'system', '2025-06-04 18:02:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (288, '3503', 'Sheraton', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:19', 'system', '2025-06-04 18:02:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (289, '3504', 'Hilton', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:20', 'system', '2025-06-04 18:02:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (290, '3505', 'Trusthouse Forte', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:21', 'system', '2025-06-04 18:02:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (291, '3506', 'Golden Tulip', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:21', 'system', '2025-06-04 18:02:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (292, '3507', 'Friendship Inns International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:22', 'system', '2025-06-04 18:02:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (293, '3508', 'Quality International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:23', 'system', '2025-06-04 18:02:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (294, '3509', 'Marriott', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:24', 'system', '2025-06-04 18:02:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (295, '3510', 'Days Inns Of America', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:25', 'system', '2025-06-04 18:02:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (296, '3511', 'Arabella Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:25', 'system', '2025-06-04 18:02:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (297, '3512', 'Intercontinental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:26', 'system', '2025-06-04 18:02:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (298, '3513', 'Westin Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:27', 'system', '2025-06-04 18:02:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (299, '3514', 'Amerisuites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:28', 'system', '2025-06-04 18:02:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (300, '3515', 'Rodeway Inns International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:28', 'system', '2025-06-04 18:02:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (301, '3516', 'La Quinta Motor Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:29', 'system', '2025-06-04 18:02:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (302, '3517', 'Americana Hotels Corporation', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:30', 'system', '2025-06-04 18:02:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (303, '3518', 'Sol Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:30', 'system', '2025-06-04 18:02:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (304, '3519', 'PLM/ETAP International Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:31', 'system', '2025-06-04 18:02:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (305, '3520', 'Meridien', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:32', 'system', '2025-06-04 18:02:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (306, '3521', 'Crest Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:32', 'system', '2025-06-04 18:02:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (307, '3522', 'Tokyo Group', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:33', 'system', '2025-06-04 18:02:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (308, '3523', 'Peninsula Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:34', 'system', '2025-06-04 18:02:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (309, '3524', 'Welcomgroup', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:35', 'system', '2025-06-04 18:02:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (310, '3525', 'Dunfey Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:35', 'system', '2025-06-04 18:02:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (311, '3526', 'Prince Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:36', 'system', '2025-06-04 18:02:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (312, '3527', 'Downtowner Passport hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:37', 'system', '2025-06-04 18:02:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (313, '3528', 'Thunderbird/Red Lion', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:37', 'system', '2025-06-04 18:02:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (314, '3529', 'CP HOTELS(Canadian Pacific)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:38', 'system', '2025-06-04 18:02:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (315, '3530', 'Renaissance Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:39', 'system', '2025-06-04 18:02:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (316, '3531', 'Kauai Coconut Beach Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:39', 'system', '2025-06-04 18:02:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (317, '3532', 'Royal Kona Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:40', 'system', '2025-06-04 18:02:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (318, '3533', 'Park Inn by Radisson', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:41', 'system', '2025-06-04 18:02:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (319, '3534', 'Southern Pacific Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:42', 'system', '2025-06-04 18:02:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (320, '3535', 'Hilton International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:42', 'system', '2025-06-04 18:02:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (321, '3536', 'AMFAC Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:43', 'system', '2025-06-04 18:02:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (322, '3537', 'Ana Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:44', 'system', '2025-06-04 18:02:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (323, '3538', 'Concorde Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:44', 'system', '2025-06-04 18:02:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (324, '3539', 'Summerfield Suites Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:45', 'system', '2025-06-04 18:02:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (325, '3540', 'Iberotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:46', 'system', '2025-06-04 18:02:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (326, '3541', 'Hotel Okura', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:47', 'system', '2025-06-04 18:02:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (327, '3542', 'Royal Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:47', 'system', '2025-06-04 18:02:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (328, '3543', 'Four Seasons', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:48', 'system', '2025-06-04 18:02:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (329, '3544', 'Cigahotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:49', 'system', '2025-06-04 18:02:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (330, '3545', 'Shangri-La International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:49', 'system', '2025-06-04 18:02:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (331, '3546', 'Hotel Sierra', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:50', 'system', '2025-06-04 18:02:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (332, '3547', 'Breakers Resort', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:02:51', 'system', '2025-06-04 18:02:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (333, '3548', 'Hotels Melia', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:51', 'system', '2025-06-04 18:02:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (334, '3549', 'Auberge Des Governeurs', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:52', 'system', '2025-06-04 18:02:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (335, '3550', 'Regal 8 Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:53', 'system', '2025-06-04 18:02:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (336, '3551', 'Mirage Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:54', 'system', '2025-06-04 18:02:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (337, '3552', 'Coast Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:54', 'system', '2025-06-04 18:02:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (338, '3553', 'Parks Inns International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:55', 'system', '2025-06-04 18:02:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (339, '3554', 'Pinehurst Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:56', 'system', '2025-06-04 18:02:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (340, '3555', 'Treasure Island Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:56', 'system', '2025-06-04 18:02:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (341, '3556', 'Barton Creek Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:57', 'system', '2025-06-04 18:02:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (342, '3557', 'Manhattan East Suite Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:58', 'system', '2025-06-04 18:02:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (343, '3558', 'Jolly Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:58', 'system', '2025-06-04 18:02:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (344, '3559', 'Candlewood Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:02:59', 'system', '2025-06-04 18:02:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (345, '3560', 'Aladdin Resort and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:00', 'system', '2025-06-04 18:03:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (346, '3561', 'Golden Nugget', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:01', 'system', '2025-06-04 18:03:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (347, '3562', 'Comfort Hotel International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:01', 'system', '2025-06-04 18:03:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (348, '3563', 'Journeys End Motels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:02', 'system', '2025-06-04 18:03:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (349, '3564', 'Sams Town Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:03', 'system', '2025-06-04 18:03:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (350, '3565', 'Relax Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:03', 'system', '2025-06-04 18:03:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (351, '3566', 'Garden Place Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:04', 'system', '2025-06-04 18:03:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (352, '3567', 'Soho Grand Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:05', 'system', '2025-06-04 18:03:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (353, '3568', 'Ladbroke Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:05', 'system', '2025-06-04 18:03:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (354, '3569', 'Tribeca Grand Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:06', 'system', '2025-06-04 18:03:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (355, '3570', 'Grand Met Forum Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:07', 'system', '2025-06-04 18:03:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (356, '3571', 'Grand Wailea Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:08', 'system', '2025-06-04 18:03:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (357, '3572', 'Miyako Hotel/Kintetsu', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:08', 'system', '2025-06-04 18:03:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (358, '3573', 'Sandman Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:09', 'system', '2025-06-04 18:03:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (359, '3574', 'Venture Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:10', 'system', '2025-06-04 18:03:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (360, '3575', 'Vagabond Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:10', 'system', '2025-06-04 18:03:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (361, '3576', 'La Quinta Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:11', 'system', '2025-06-04 18:03:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (362, '3577', 'Mandarin International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:12', 'system', '2025-06-04 18:03:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (363, '3578', 'Frankenmuth Bavarian', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:12', 'system', '2025-06-04 18:03:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (364, '3579', 'Hotel Mercure', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:13', 'system', '2025-06-04 18:03:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (365, '3580', 'Hotel Del Coronado', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:14', 'system', '2025-06-04 18:03:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (366, '3581', 'Delta Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:14', 'system', '2025-06-04 18:03:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (367, '3582', 'California Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:15', 'system', '2025-06-04 18:03:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (368, '3583', 'Radisson BLU', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:16', 'system', '2025-06-04 18:03:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (369, '3584', 'Princess Hotels International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:17', 'system', '2025-06-04 18:03:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (370, '3585', 'Hungar Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:17', 'system', '2025-06-04 18:03:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (371, '3586', 'Sokos Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:18', 'system', '2025-06-04 18:03:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (372, '3587', 'Doral Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:19', 'system', '2025-06-04 18:03:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (373, '3588', 'Helmsley Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:19', 'system', '2025-06-04 18:03:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (374, '3589', 'Doral Golf Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:20', 'system', '2025-06-04 18:03:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (375, '3590', 'Fairmont Hotels Corporation', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:21', 'system', '2025-06-04 18:03:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (376, '3591', 'Sonesta International Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:21', 'system', '2025-06-04 18:03:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (377, '3592', 'Omni International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:22', 'system', '2025-06-04 18:03:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (378, '3593', 'Cunard Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:23', 'system', '2025-06-04 18:03:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (379, '3594', 'Arizona Biltmore', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:24', 'system', '2025-06-04 18:03:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (380, '3595', 'Hospitality Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:24', 'system', '2025-06-04 18:03:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (381, '3596', 'Wynn Las Vegas', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:25', 'system', '2025-06-04 18:03:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (382, '3597', 'Riverside Resort and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:26', 'system', '2025-06-04 18:03:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (383, '3598', 'Regent Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:26', 'system', '2025-06-04 18:03:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (384, '3599', 'Pannonia Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:27', 'system', '2025-06-04 18:03:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (385, '3600', 'Saddlebrook Resort–Tampa', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:28', 'system', '2025-06-04 18:03:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (386, '3601', 'Tradewinds Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:28', 'system', '2025-06-04 18:03:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (387, '3602', 'Hudson Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:29', 'system', '2025-06-04 18:03:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (388, '3603', 'Noahs Hotel (Melbourne)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:30', 'system', '2025-06-04 18:03:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (389, '3604', 'Hilton Garden Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:31', 'system', '2025-06-04 18:03:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (390, '3605', 'Jurys Doyle Hotel Group', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:03:31', 'system', '2025-06-04 18:03:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (391, '3606', 'Jefferson Hotel', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:03:32', 'system', '2025-06-04 18:03:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (392, '3607', 'Fontainebleau Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:33', 'system', '2025-06-04 18:03:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (393, '3608', 'Gaylord Opryland', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:33', 'system', '2025-06-04 18:03:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (394, '3609', 'Gaylord Palms', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:34', 'system', '2025-06-04 18:03:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (395, '3610', 'Gaylord Texan', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:03:35', 'system', '2025-06-04 18:03:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (396, '3611', 'C MON INN', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:03:35', 'system', '2025-06-04 18:03:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (397, '3612', 'Moevenpick', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:36', 'system', '2025-06-04 18:03:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (398, '3613', 'Microtel Inn and Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:37', 'system', '2025-06-04 18:03:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (399, '3614', 'AmericInn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:38', 'system', '2025-06-04 18:03:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (400, '3615', 'Travelodge Motels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:38', 'system', '2025-06-04 18:03:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (401, '3616', 'Hermitage Hotels M', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:03:39', 'system', '2025-06-04 18:03:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (402, '3617', 'America''s Best Value Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:40', 'system', '2025-06-04 18:03:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (403, '3618', 'Great Wolf', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:40', 'system', '2025-06-04 18:03:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (404, '3619', 'Aloft', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:41', 'system', '2025-06-04 18:03:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (405, '3620', 'Binions Horseshoe Club', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:42', 'system', '2025-06-04 18:03:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (406, '3621', 'Extended Stay', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:43', 'system', '2025-06-04 18:03:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (407, '3622', 'Merlin Hotel (Perth)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:43', 'system', '2025-06-04 18:03:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (408, '3623', 'Dorint Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:44', 'system', '2025-06-04 18:03:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (409, '3624', 'Lady Luck Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:45', 'system', '2025-06-04 18:03:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (410, '3625', 'Hotel Universal', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:45', 'system', '2025-06-04 18:03:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (411, '3626', 'Studio Plus', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:46', 'system', '2025-06-04 18:03:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (412, '3627', 'Extended Stay America', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:47', 'system', '2025-06-04 18:03:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (413, '3628', 'Excalibur Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:47', 'system', '2025-06-04 18:03:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (414, '3629', 'Dan Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:48', 'system', '2025-06-04 18:03:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (415, '3630', 'Extended Stay Deluxe', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:49', 'system', '2025-06-04 18:03:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (416, '3631', 'Sleep Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:50', 'system', '2025-06-04 18:03:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (417, '3632', 'The Phoenician', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:50', 'system', '2025-06-04 18:03:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (418, '3633', 'Rank Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:51', 'system', '2025-06-04 18:03:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (419, '3634', 'Swissotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:52', 'system', '2025-06-04 18:03:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (420, '3635', 'Reso Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:52', 'system', '2025-06-04 18:03:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (421, '3636', 'Sarova Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:53', 'system', '2025-06-04 18:03:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (422, '3637', 'Ramada Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:54', 'system', '2025-06-04 18:03:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (423, '3638', 'Howard Johnson', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:54', 'system', '2025-06-04 18:03:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (424, '3639', 'Mount Charlotte Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:55', 'system', '2025-06-04 18:03:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (425, '3640', 'Hyatt Hotels/International', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:56', 'system', '2025-06-04 18:03:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (426, '3641', 'Sofitel Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:57', 'system', '2025-06-04 18:03:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (427, '3642', 'Novotel Sieh (Accor)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:57', 'system', '2025-06-04 18:03:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (428, '3643', 'Steigenberger', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:58', 'system', '2025-06-04 18:03:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (429, '3644', 'Econo-Travel Motor Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:59', 'system', '2025-06-04 18:03:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (430, '3645', 'Queens Moat Houses', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:03:59', 'system', '2025-06-04 18:03:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (431, '3646', 'Swallow Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:00', 'system', '2025-06-04 18:04:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (432, '3647', 'Grupo Hotels HUSA SA', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:01', 'system', '2025-06-04 18:04:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (433, '3648', 'De Vere Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:01', 'system', '2025-06-04 18:04:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (434, '3649', 'Radisson', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:02', 'system', '2025-06-04 18:04:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (435, '3650', 'Red Roof Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:03', 'system', '2025-06-04 18:04:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (436, '3651', 'Imperial London Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:04', 'system', '2025-06-04 18:04:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (437, '3652', 'Embassy Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:04', 'system', '2025-06-04 18:04:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (438, '3653', 'Penta Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:05', 'system', '2025-06-04 18:04:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (439, '3654', 'Loews Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:06', 'system', '2025-06-04 18:04:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (440, '3655', 'Scandic Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:06', 'system', '2025-06-04 18:04:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (441, '3656', 'Sara Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:07', 'system', '2025-06-04 18:04:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (442, '3657', 'Oberoi Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:08', 'system', '2025-06-04 18:04:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (443, '3658', 'New Otani Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:09', 'system', '2025-06-04 18:04:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (444, '3659', 'Taj Hotels Intl', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:09', 'system', '2025-06-04 18:04:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (445, '3660', 'Knights Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:10', 'system', '2025-06-04 18:04:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (446, '3661', 'Metropole Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:11', 'system', '2025-06-04 18:04:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (447, '3662', 'Circus Circus Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:12', 'system', '2025-06-04 18:04:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (448, '3663', 'Hoteles El Presidente', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:13', 'system', '2025-06-04 18:04:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (449, '3664', 'Flag Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:13', 'system', '2025-06-04 18:04:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (450, '3665', 'Hampton Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:14', 'system', '2025-06-04 18:04:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (451, '3666', 'Stakis Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:15', 'system', '2025-06-04 18:04:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (452, '3667', 'Luxor Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:15', 'system', '2025-06-04 18:04:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (453, '3668', 'Maritim', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:16', 'system', '2025-06-04 18:04:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (454, '3669', 'Eldorado Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:17', 'system', '2025-06-04 18:04:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (455, '3670', 'Arcade', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:18', 'system', '2025-06-04 18:04:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (456, '3671', 'Arctia', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:18', 'system', '2025-06-04 18:04:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (457, '3672', 'Campanile', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:19', 'system', '2025-06-04 18:04:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (458, '3673', 'Ibusz Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:20', 'system', '2025-06-04 18:04:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (459, '3674', 'Rantasipi Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:20', 'system', '2025-06-04 18:04:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (460, '3675', 'Interhotel Cedok', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:21', 'system', '2025-06-04 18:04:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (461, '3676', 'Monte Carlo Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:22', 'system', '2025-06-04 18:04:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (462, '3677', 'Climat De France', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:22', 'system', '2025-06-04 18:04:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (463, '3678', 'Cumulus Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:23', 'system', '2025-06-04 18:04:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (464, '3679', 'Silver Legacy Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:24', 'system', '2025-06-04 18:04:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (465, '3680', 'Hoteis Othan', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:24', 'system', '2025-06-04 18:04:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (466, '3681', 'Adams Mark', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:25', 'system', '2025-06-04 18:04:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (467, '3682', 'Sahara Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:26', 'system', '2025-06-04 18:04:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (468, '3683', 'Bradbury Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:27', 'system', '2025-06-04 18:04:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (469, '3684', 'Budget Host Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:27', 'system', '2025-06-04 18:04:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (470, '3685', 'Budgetel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:28', 'system', '2025-06-04 18:04:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (471, '3686', 'Suisse Chalet', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:29', 'system', '2025-06-04 18:04:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (472, '3687', 'Clarion Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:29', 'system', '2025-06-04 18:04:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (473, '3688', 'Compri Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:30', 'system', '2025-06-04 18:04:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (474, '3689', 'Consort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:31', 'system', '2025-06-04 18:04:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (475, '3690', 'Courtyard by Marriott', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:32', 'system', '2025-06-04 18:04:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (476, '3691', 'Dillon Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:32', 'system', '2025-06-04 18:04:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (477, '3692', 'Doubletree', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:33', 'system', '2025-06-04 18:04:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (478, '3693', 'Drury Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:34', 'system', '2025-06-04 18:04:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (479, '3694', 'Economy Inns Of America', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:34', 'system', '2025-06-04 18:04:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (480, '3695', 'Embassy Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:35', 'system', '2025-06-04 18:04:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (481, '3696', 'Excel Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:36', 'system', '2025-06-04 18:04:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (482, '3697', 'Fairfield Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:36', 'system', '2025-06-04 18:04:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (483, '3698', 'Harley Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:37', 'system', '2025-06-04 18:04:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (484, '3699', 'Midway Motor Lodge', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:38', 'system', '2025-06-04 18:04:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (485, '3700', 'Motel 6', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:39', 'system', '2025-06-04 18:04:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (486, '3701', 'La Mansion Del Rio', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:40', 'system', '2025-06-04 18:04:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (487, '3702', 'Registry Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:40', 'system', '2025-06-04 18:04:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (488, '3703', 'Residence Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:41', 'system', '2025-06-04 18:04:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (489, '3704', 'Royce Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:42', 'system', '2025-06-04 18:04:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (490, '3705', 'Sandman Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:42', 'system', '2025-06-04 18:04:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (491, '3706', 'Shilo Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:43', 'system', '2025-06-04 18:04:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (492, '3707', 'Shoneys Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:44', 'system', '2025-06-04 18:04:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (493, '3708', 'Virgin River Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:44', 'system', '2025-06-04 18:04:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (494, '3709', 'Super 8 Motel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:45', 'system', '2025-06-04 18:04:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (495, '3710', 'Ritz-Carlton', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:46', 'system', '2025-06-04 18:04:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (496, '3711', 'Flag Inns (Australia)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:47', 'system', '2025-06-04 18:04:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (497, '3712', 'Buffalo Bills Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:47', 'system', '2025-06-04 18:04:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (498, '3713', 'Quality Pacific Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:48', 'system', '2025-06-04 18:04:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (499, '3714', 'Four Seasons (Australia)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:49', 'system', '2025-06-04 18:04:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (500, '3715', 'Fairfield Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:49', 'system', '2025-06-04 18:04:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (501, '3716', 'Carlton Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:50', 'system', '2025-06-04 18:04:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (502, '3717', 'City Lodge Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:51', 'system', '2025-06-04 18:04:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (503, '3718', 'Karos Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:52', 'system', '2025-06-04 18:04:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (504, '3719', 'Protea Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:52', 'system', '2025-06-04 18:04:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (505, '3720', 'Southern Sun Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:53', 'system', '2025-06-04 18:04:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (506, '3721', 'Conrad Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:54', 'system', '2025-06-04 18:04:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (507, '3722', 'Wyndham', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:54', 'system', '2025-06-04 18:04:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (508, '3723', 'Rica Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:55', 'system', '2025-06-04 18:04:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (509, '3724', 'Inter Nor Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:56', 'system', '2025-06-04 18:04:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (510, '3725', 'Sea Pines Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:57', 'system', '2025-06-04 18:04:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (511, '3726', 'Rio Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:57', 'system', '2025-06-04 18:04:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (512, '3727', 'Broadmoor Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:58', 'system', '2025-06-04 18:04:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (513, '3728', 'Ballys Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:59', 'system', '2025-06-04 18:04:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (514, '3729', 'John Ascuagas Nugget', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:04:59', 'system', '2025-06-04 18:04:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (515, '3730', 'MGM Grand Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:00', 'system', '2025-06-04 18:05:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (516, '3731', 'Harrah''s Hotels and Casinos', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:01', 'system', '2025-06-04 18:05:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (517, '3732', 'Opryland Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:02', 'system', '2025-06-04 18:05:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (518, '3733', 'Boca Raton Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:02', 'system', '2025-06-04 18:05:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (519, '3734', 'Harvey Bristol Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:03', 'system', '2025-06-04 18:05:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (520, '3735', 'Masters Economy Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:04', 'system', '2025-06-04 18:05:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (521, '3736', 'Colorado Belle Edgewater Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:04', 'system', '2025-06-04 18:05:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (522, '3737', 'Riviera Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:05', 'system', '2025-06-04 18:05:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (523, '3738', 'Tropicana Resort and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:06', 'system', '2025-06-04 18:05:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (524, '3739', 'Woodside Hotels and Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:07', 'system', '2025-06-04 18:05:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (525, '3740', 'Towneplace Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:07', 'system', '2025-06-04 18:05:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (526, '3741', 'Millennium Broadway Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:08', 'system', '2025-06-04 18:05:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (527, '3742', 'Club Med', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:09', 'system', '2025-06-04 18:05:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (528, '3743', 'Biltmore Hotel and Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:09', 'system', '2025-06-04 18:05:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (529, '3744', 'Carefree Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:10', 'system', '2025-06-04 18:05:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (530, '3745', 'St. Regis Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:11', 'system', '2025-06-04 18:05:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (531, '3746', 'The Eliot Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:12', 'system', '2025-06-04 18:05:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (532, '3747', 'Club Corporation/Club Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:12', 'system', '2025-06-04 18:05:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (533, '3748', 'Wellesley Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:13', 'system', '2025-06-04 18:05:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (534, '3749', 'Beverly Hills Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:14', 'system', '2025-06-04 18:05:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (535, '3750', 'Crowne Plaza Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:14', 'system', '2025-06-04 18:05:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (536, '3751', 'Homewood Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:15', 'system', '2025-06-04 18:05:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (537, '3752', 'Peabody Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:16', 'system', '2025-06-04 18:05:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (538, '3753', 'Greenbriar Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:17', 'system', '2025-06-04 18:05:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (539, '3754', 'Amelia Island Plantation', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:17', 'system', '2025-06-04 18:05:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (540, '3755', 'Homestead', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:18', 'system', '2025-06-04 18:05:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (541, '3756', 'Toyoko Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:19', 'system', '2025-06-04 18:05:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (542, '3757', 'CANYON RANCH', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:19', 'system', '2025-06-04 18:05:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (543, '3758', 'Kahala Mandarin Oriental Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:20', 'system', '2025-06-04 18:05:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (544, '3759', 'Orchid At Mauna Lai', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:21', 'system', '2025-06-04 18:05:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (545, '3760', 'Halekulani Hotel/Waikiki Parc', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:22', 'system', '2025-06-04 18:05:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (546, '3761', 'Primadonna Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:22', 'system', '2025-06-04 18:05:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (547, '3762', 'Whiskey Pete''s Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:23', 'system', '2025-06-04 18:05:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (548, '3763', 'Chateau Elan Winery and Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:24', 'system', '2025-06-04 18:05:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (549, '3764', 'Beau Rivage Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:24', 'system', '2025-06-04 18:05:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (550, '3765', 'Bellagio', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:25', 'system', '2025-06-04 18:05:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (551, '3766', 'Fremont Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:26', 'system', '2025-06-04 18:05:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (552, '3767', 'Main Street Station Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:26', 'system', '2025-06-04 18:05:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (553, '3768', 'Silver Star Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:27', 'system', '2025-06-04 18:05:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (554, '3769', 'Stratosphere Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:28', 'system', '2025-06-04 18:05:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (555, '3770', 'Springhill Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:29', 'system', '2025-06-04 18:05:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (556, '3771', 'Caesars Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:29', 'system', '2025-06-04 18:05:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (557, '3772', 'Nemacolin Woodlands', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:30', 'system', '2025-06-04 18:05:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (558, '3773', 'Venetian Resort Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:31', 'system', '2025-06-04 18:05:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (559, '3774', 'New York-New York Hotel and Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:31', 'system', '2025-06-04 18:05:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (560, '3775', 'Sands Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:32', 'system', '2025-06-04 18:05:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (561, '3776', 'Nevele Grande Resort and Country Club', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:33', 'system', '2025-06-04 18:05:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (562, '3777', 'Mandalay Bay Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:34', 'system', '2025-06-04 18:05:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (563, '3778', 'Four Points Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:34', 'system', '2025-06-04 18:05:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (564, '3779', 'W Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:35', 'system', '2025-06-04 18:05:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (565, '3780', 'Disneyland Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:36', 'system', '2025-06-04 18:05:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (566, '3781', 'Patricia Grand Resort Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:36', 'system', '2025-06-04 18:05:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (567, '3782', 'Rosen Hotel and Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:37', 'system', '2025-06-04 18:05:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (568, '3783', 'Town and Country Resort and Convention Center', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:38', 'system', '2025-06-04 18:05:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (569, '3784', 'First Hospitality Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:38', 'system', '2025-06-04 18:05:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (570, '3785', 'Outrigger Hotels and Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:39', 'system', '2025-06-04 18:05:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (571, '3786', 'Ohana Hotels of Hawaii', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:40', 'system', '2025-06-04 18:05:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (572, '3787', 'Caribe Royal Resort Suite and Villas', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:41', 'system', '2025-06-04 18:05:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (573, '3788', 'Ala Moana Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:41', 'system', '2025-06-04 18:05:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (574, '3789', 'Smugglers Notch Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:42', 'system', '2025-06-04 18:05:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (575, '3790', 'Raffles Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:43', 'system', '2025-06-04 18:05:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (576, '3791', 'Staybridge Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:43', 'system', '2025-06-04 18:05:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (577, '3792', 'Claridge Casino Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:44', 'system', '2025-06-04 18:05:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (578, '3793', 'The Flamingo Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:45', 'system', '2025-06-04 18:05:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (579, '3794', 'Grand Casino Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:45', 'system', '2025-06-04 18:05:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (580, '3795', 'Paris Las Vegas Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:46', 'system', '2025-06-04 18:05:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (581, '3796', 'Peppermill Hotel Casino', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:47', 'system', '2025-06-04 18:05:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (582, '3797', 'Atlantic City Hilton', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:48', 'system', '2025-06-04 18:05:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (583, '3798', 'Embassy Vacation Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:48', 'system', '2025-06-04 18:05:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (584, '3799', 'Hale Koa Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:49', 'system', '2025-06-04 18:05:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (585, '3800', 'Homestead Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:50', 'system', '2025-06-04 18:05:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (586, '3801', 'Wilderness Hotel and Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:50', 'system', '2025-06-04 18:05:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (587, '3802', 'The Palace Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:51', 'system', '2025-06-04 18:05:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (588, '3803', 'The Wigwam Golf Resort and Spa', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:05:52', 'system', '2025-06-04 18:05:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (589, '3804', 'The Diplomat Country Club and Spa', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:05:53', 'system', '2025-06-04 18:05:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (590, '3805', 'The Atlantic', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:05:53', 'system', '2025-06-04 18:05:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (591, '3806', 'Princeville Resort', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:05:54', 'system', '2025-06-04 18:05:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (592, '3807', 'Element', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:55', 'system', '2025-06-04 18:05:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (593, '3808', 'LXR (Luxury Resorts)', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:55', 'system', '2025-06-04 18:05:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (594, '3809', 'Settle Inn', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:05:56', 'system', '2025-06-04 18:05:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (595, '3810', 'La Costa Resort', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:05:57', 'system', '2025-06-04 18:05:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (596, '3811', 'Premier Travel Inns', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:57', 'system', '2025-06-04 18:05:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (597, '3812', 'Hyatt Place', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:58', 'system', '2025-06-04 18:05:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (598, '3813', 'Hotel Indigo', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:05:59', 'system', '2025-06-04 18:05:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (599, '3814', 'The Roosevelt Hotel NY', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:00', 'system', '2025-06-04 18:06:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (600, '3815', 'Nickelodeon Family Suites by Holiday Inn', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:00', 'system', '2025-06-04 18:06:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (601, '3816', 'Home2Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:01', 'system', '2025-06-04 18:06:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (602, '3817', 'Affinia', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:02', 'system', '2025-06-04 18:06:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (603, '3818', 'Mainstay Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:02', 'system', '2025-06-04 18:06:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (604, '3819', 'Oxford Suites', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:03', 'system', '2025-06-04 18:06:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (605, '3820', 'Jumeirah Essex House', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:04', 'system', '2025-06-04 18:06:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (606, '3821', 'Caribe Royal', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:05', 'system', '2025-06-04 18:06:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (607, '3822', 'Crossland', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:06', 'system', '2025-06-04 18:06:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (608, '3823', 'Grand Sierra Resort', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:06', 'system', '2025-06-04 18:06:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (609, '3824', 'Aria', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:07', 'system', '2025-06-04 18:06:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (610, '3825', 'Vdara', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:08', 'system', '2025-06-04 18:06:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (611, '3826', 'Autograph', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:08', 'system', '2025-06-04 18:06:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (612, '3827', 'Galt House', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:09', 'system', '2025-06-04 18:06:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (613, '3828', 'Cosmopolitan of Las Vegas', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:10', 'system', '2025-06-04 18:06:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (614, '3829', 'Country Inn By Carlson', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:10', 'system', '2025-06-04 18:06:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (615, '3830', 'Park Plaza Hotel', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:11', 'system', '2025-06-04 18:06:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (616, '3831', 'Waldorf', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:12', 'system', '2025-06-04 18:06:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (617, '3832', 'CURIO Hotels', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:13', 'system', '2025-06-04 18:06:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (618, '3833', 'CANOPY', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:13', 'system', '2025-06-04 18:06:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (619, '3834', 'BAYMONT INN&SUITES', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:14', 'system', '2025-06-04 18:06:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (620, '3835', 'DOLCE HOTELS And Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:15', 'system', '2025-06-04 18:06:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (621, '3836', 'HAWTHORNE BY WYNDHAM', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:16', 'system', '2025-06-04 18:06:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (622, '3837', 'HOSHINO Resorts', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:16', 'system', '2025-06-04 18:06:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (623, '3838', 'KIMPTON HOTELS', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:17', 'system', '2025-06-04 18:06:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (624, '3839', 'KYORITSU HOTELS', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:18', 'system', '2025-06-04 18:06:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (625, '3840', 'RIO HOTELS', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:18', 'system', '2025-06-04 18:06:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (626, '3998', 'China Railway', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:19', 'system', '2025-06-04 18:06:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (627, '4011', 'Railroads–Freight', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:20', 'system', '2025-06-04 18:06:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (628, '4111', 'Transportation–Suburban and Local Commuter Passenger, including Ferries', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:20', 'system', '2025-06-04 18:06:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (629, '4112', 'Passenger Railways', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:21', 'system', '2025-06-04 18:06:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (630, '4119', 'Ambulance Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:22', 'system', '2025-06-04 18:06:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (631, '4121', 'Taxicabs and Limousines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:23', 'system', '2025-06-04 18:06:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (632, '4131', 'Bus Lines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:23', 'system', '2025-06-04 18:06:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (633, '4214', 'Motor Freight Carriers,Trucking–Local/Long Distance, Moving and Storage Companies, Local Delivery', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:24', 'system', '2025-06-04 18:06:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (634, '4215', 'Courier Services–Air and Ground, Freight Forwarders', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:25', 'system', '2025-06-04 18:06:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (635, '4225', 'Public Warehousing–Farm Products, Refrigerated Goods, Household Goods Storage', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:25', 'system', '2025-06-04 18:06:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (636, '4411', 'Cruise Lines', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:06:26', 'system', '2025-06-04 18:06:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (637, '4457', 'Boat Leases and Boat Rentals', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:27', 'system', '2025-06-04 18:06:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (638, '4468', 'Marinas, Marine Service/Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:28', 'system', '2025-06-04 18:06:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (639, '4511', 'Air Carriers, Airlines–not elsewhere classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:28', 'system', '2025-06-04 18:06:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (640, '4582', 'Airports, Airport Terminals, Flying Fields', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:29', 'system', '2025-06-04 18:06:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (641, '4722', 'Travel Agencies and Tour Operators', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:30', 'system', '2025-06-04 18:06:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (642, '4723', 'PACKAGE TOUR OPERATORS(Germany Only)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:31', 'system', '2025-06-04 18:06:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (643, '4784', 'Bridge and Road Fees, Tolls', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:31', 'system', '2025-06-04 18:06:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (644, '4789', 'Transportation Services Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:32', 'system', '2025-06-04 18:06:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (645, '4812', 'Telecommunication Equipment Including Telephone Sales', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:33', 'system', '2025-06-04 18:06:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (646, '4813', 'Key-Entry Telecom Merchant providing single local and long-distance phone calls using a central access number in a non-face-to-face environment using key entry', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:33', 'system', '2025-06-04 18:06:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (647, '4814', 'Telecommunication Services including but not limited to prepaid phone services and recurring phone services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:34', 'system', '2025-06-04 18:06:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (648, '4815', 'Monthly telephone charges', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:35', 'system', '2025-06-04 18:06:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (649, '4816', 'Computer Network/Information Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:36', 'system', '2025-06-04 18:06:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (650, '4821', 'Telegraph Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:36', 'system', '2025-06-04 18:06:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (651, '4829', 'Wire Transfer Money Orders (V, D, G, X)/Money Transfer (M)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:37', 'system', '2025-06-04 18:06:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (652, '4899', 'Cable, Satellite, and Other Pay Television and Radio Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:38', 'system', '2025-06-04 18:06:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (653, '4900', 'Utilities–Electric, Gas, Heating Oil, Sanitary, Water', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:38', 'system', '2025-06-04 18:06:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (654, '5013', 'Motor Vehicle Supplies and New Parts', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:39', 'system', '2025-06-04 18:06:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (655, '5021', 'Office and Commercial Furniture', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:40', 'system', '2025-06-04 18:06:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (656, '5039', 'Construction Materials Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:40', 'system', '2025-06-04 18:06:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (657, '5044', 'Office, Photographic, Photocopy and Microfilm Equipment', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:41', 'system', '2025-06-04 18:06:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (658, '5045', 'Computers, Computer Peripheral Equipment, Software', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:42', 'system', '2025-06-04 18:06:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (659, '5046', 'Commercial Equipment Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:43', 'system', '2025-06-04 18:06:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (660, '5047', 'Dental/Laboratory/Medical/Ophthalmic Hospital Equipment and Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:43', 'system', '2025-06-04 18:06:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (661, '5051', 'Metal Service Centers and Offices', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:44', 'system', '2025-06-04 18:06:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (662, '5065', 'Electrical Parts and Equipment', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:45', 'system', '2025-06-04 18:06:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (663, '5072', 'Hardware Equipment and Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:45', 'system', '2025-06-04 18:06:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (664, '5074', 'Plumbing and Heating Equipment', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:46', 'system', '2025-06-04 18:06:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (665, '5085', 'Industrial Supplies Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:47', 'system', '2025-06-04 18:06:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (666, '5094', 'Precious Stones and Metals, Watches and Jewelry', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:47', 'system', '2025-06-04 18:06:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (667, '5099', 'Durable Goods Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:48', 'system', '2025-06-04 18:06:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (668, '5111', 'Stationery, Office Supplies, Printing and Writing Paper', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:49', 'system', '2025-06-04 18:06:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (669, '5122', 'Drugs, Drug Proprietors and Druggists Sundries', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:49', 'system', '2025-06-04 18:06:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (670, '5131', 'Piece Goods, Notions, and Other Dry Goods', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:50', 'system', '2025-06-04 18:06:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (671, '5137', 'Men''s, Women''s and Children''s Uniforms and Commercial Clothing', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:51', 'system', '2025-06-04 18:06:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (672, '5139', 'Commercial Footware', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:52', 'system', '2025-06-04 18:06:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (673, '5169', 'Chemicals and Allied Products Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:52', 'system', '2025-06-04 18:06:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (674, '5172', 'Petroleum and Petroleum Products', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:53', 'system', '2025-06-04 18:06:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (675, '5192', 'Books, Periodicals and Newspapers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:54', 'system', '2025-06-04 18:06:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (676, '5193', 'Florists Supplies, Nursery Stock and Flowers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:55', 'system', '2025-06-04 18:06:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (677, '5198', 'Paints, Varnishes and Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:55', 'system', '2025-06-04 18:06:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (678, '5199', 'Non-Durable Goods Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:56', 'system', '2025-06-04 18:06:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (679, '5200', 'Home Supply Warehouse Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:57', 'system', '2025-06-04 18:06:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (680, '5211', 'Building Materials, Lumber Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:58', 'system', '2025-06-04 18:06:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (681, '5231', 'Glass, Paint, Wallpaper Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:58', 'system', '2025-06-04 18:06:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (682, '5251', 'Hardware Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:06:59', 'system', '2025-06-04 18:06:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (683, '5261', 'Lawn and Garden Supply Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:00', 'system', '2025-06-04 18:07:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (684, '5262', 'Marketplace', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:00', 'system', '2025-06-04 18:07:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (685, '5271', 'Mobile Home Dealers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:01', 'system', '2025-06-04 18:07:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (686, '5300', 'Wholesale Clubs', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:02', 'system', '2025-06-04 18:07:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (687, '5309', 'Duty Free Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:03', 'system', '2025-06-04 18:07:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (688, '5310', 'Discount Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:03', 'system', '2025-06-04 18:07:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (689, '5311', 'Department Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:04', 'system', '2025-06-04 18:07:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (690, '5331', 'Variety Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:05', 'system', '2025-06-04 18:07:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (691, '5399', 'Miscellaneous Retail Merchandise Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:05', 'system', '2025-06-04 18:07:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (692, '5411', 'Grocery Stores, Supermarkets', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:06', 'system', '2025-06-04 18:07:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (693, '5422', 'Freezer and Locker Meat Provisioners', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:07', 'system', '2025-06-04 18:07:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (694, '5441', 'Candy, Nut and Confectionery Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:08', 'system', '2025-06-04 18:07:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (695, '5451', 'Dairy Products Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:09', 'system', '2025-06-04 18:07:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (696, '5462', 'Bakeries', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:09', 'system', '2025-06-04 18:07:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (697, '5499', 'Miscellaneous Food Stores–Convenience Stores, Markets, Specialty Stores, and Vending Machines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:10', 'system', '2025-06-04 18:07:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (698, '5511', 'Automobile and Truck Dealers.Sales, Service, Repairs, Parts, and Leasing', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:11', 'system', '2025-06-04 18:07:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (699, '5521', 'Automobile and Truck Dealers–(Used Only)–Sales', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:11', 'system', '2025-06-04 18:07:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (700, '5531', 'AUTO STORE, HOME SUPPLY STORE', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:12', 'system', '2025-06-04 18:07:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (701, '5532', 'Automotive Tire Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:13', 'system', '2025-06-04 18:07:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (702, '5533', 'Automotive Parts and Accessories Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:14', 'system', '2025-06-04 18:07:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (703, '5541', 'Service Stations (With or Without Ancillary Services)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:14', 'system', '2025-06-04 18:07:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (704, '5542', 'Fuel Dispenser, Automated', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:15', 'system', '2025-06-04 18:07:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (705, '5543', 'CAMPSA/SOLRED', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:16', 'system', '2025-06-04 18:07:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (706, '5551', 'Boat Dealers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:17', 'system', '2025-06-04 18:07:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (707, '5552', 'Electric Vehicle Charging', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:17', 'system', '2025-06-04 18:07:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (708, '5561', 'Camper Dealers, Recreational and Utility Trailers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:18', 'system', '2025-06-04 18:07:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (709, '5571', 'Motorcycle Shops and Dealers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:19', 'system', '2025-06-04 18:07:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (710, '5592', 'Motor Home Dealers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:20', 'system', '2025-06-04 18:07:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (711, '5598', 'Snowmobile Dealers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:20', 'system', '2025-06-04 18:07:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (712, '5599', 'Miscellaneous Automotive, Aircraft, and Farm Equipment Dealers–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:21', 'system', '2025-06-04 18:07:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (713, '5611', 'Men''s and Boys'' Clothing and Accessories Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:22', 'system', '2025-06-04 18:07:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (714, '5621', 'Women''s Ready to Wear Stor''es', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:23', 'system', '2025-06-04 18:07:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (715, '5631', 'Women''s Accessory and Specialty Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:23', 'system', '2025-06-04 18:07:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (716, '5641', 'Children''s and Infants'' Wear Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:24', 'system', '2025-06-04 18:07:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (717, '5651', 'Family Clothing Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:25', 'system', '2025-06-04 18:07:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (718, '5655', 'Sports Apparel, and Riding Apparel Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:26', 'system', '2025-06-04 18:07:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (719, '5661', 'Shoe Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:26', 'system', '2025-06-04 18:07:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (720, '5681', 'Furriers and Fur Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:27', 'system', '2025-06-04 18:07:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (721, '5691', 'Men''s and Women''s Clothing Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:28', 'system', '2025-06-04 18:07:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (722, '5697', 'Alterations, Mending, Seamstresses, Tailors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:29', 'system', '2025-06-04 18:07:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (723, '5698', 'Wig and Toupee Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:29', 'system', '2025-06-04 18:07:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (724, '5699', 'Accessory and Apparel Stores–Miscellaneous', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:30', 'system', '2025-06-04 18:07:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (725, '5712', 'Equipment, Furniture and Home Furnishings Stores (except Appliances)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:31', 'system', '2025-06-04 18:07:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (726, '5713', 'Floor Covering Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:31', 'system', '2025-06-04 18:07:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (727, '5714', 'Drapery, Upholstery and Window Coverings Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:32', 'system', '2025-06-04 18:07:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (728, '5715', 'ALCOHOLIC BEVERAGE', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:33', 'system', '2025-06-04 18:07:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (729, '5718', 'Fireplace, Fireplace Screens and Accessories Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:33', 'system', '2025-06-04 18:07:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (730, '5719', 'Miscellaneous House Furnishing Specialty Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:34', 'system', '2025-06-04 18:07:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (731, '5722', 'Household Appliance Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:35', 'system', '2025-06-04 18:07:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (732, '5723', 'Guns and Ammunition Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:36', 'system', '2025-06-04 18:07:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (733, '5732', 'Electronics Sales', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:36', 'system', '2025-06-04 18:07:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (734, '5733', 'Music Stores–Musical Instruments, Pianos and Sheet Music', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:37', 'system', '2025-06-04 18:07:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (735, '5734', 'Computer Software Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:38', 'system', '2025-06-04 18:07:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (736, '5735', 'Record Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:38', 'system', '2025-06-04 18:07:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (737, '5811', 'Caterers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:39', 'system', '2025-06-04 18:07:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (738, '5812', 'Eating Places and Restaurants', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:40', 'system', '2025-06-04 18:07:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (739, '5813', 'Bars, Cocktail Lounges, Discotheques, Nightclubs and Taverns–Drinking Places (Alcoholic Beverages)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:40', 'system', '2025-06-04 18:07:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (740, '5814', 'Fast Food Restaurants', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:41', 'system', '2025-06-04 18:07:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (741, '5815', 'Digital Goods: Books, Movies, Music', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:42', 'system', '2025-06-04 18:07:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (742, '5816', 'Digital Goods: Games', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:43', 'system', '2025-06-04 18:07:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (743, '5817', 'Digital Goods: Applications (Excludes Games)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:43', 'system', '2025-06-04 18:07:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (744, '5818', 'Digital Goods: Large Digital Goods Merchant (V) Digital Goods: Multi-Category (M)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:44', 'system', '2025-06-04 18:07:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (745, '5912', 'Drug Stores and Pharmacies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:45', 'system', '2025-06-04 18:07:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (746, '5921', 'Package Stores–Beer, Wine and Liquor', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:45', 'system', '2025-06-04 18:07:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (747, '5931', 'Second Hand Stores, Used Merchandise Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:46', 'system', '2025-06-04 18:07:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (748, '5932', 'Antique Shops–Sales, Repairs and Restoration Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:47', 'system', '2025-06-04 18:07:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (749, '5933', 'Pawn Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:47', 'system', '2025-06-04 18:07:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (750, '5935', 'Wrecking and Salvage Yards', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:48', 'system', '2025-06-04 18:07:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (751, '5937', 'Antique Reproduction Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:49', 'system', '2025-06-04 18:07:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (752, '5940', 'Bicycle Shops–Sales and Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:50', 'system', '2025-06-04 18:07:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (753, '5941', 'Sporting Goods Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:50', 'system', '2025-06-04 18:07:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (754, '5942', 'Book Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:51', 'system', '2025-06-04 18:07:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (755, '5943', 'Office, School Supply and Stationery Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:52', 'system', '2025-06-04 18:07:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (756, '5944', 'Clock, Jewelry, Watch and Silverware Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:53', 'system', '2025-06-04 18:07:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (757, '5945', 'Game, Toy and Hobby Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:53', 'system', '2025-06-04 18:07:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (758, '5946', 'Camera and Photographic Supply Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:54', 'system', '2025-06-04 18:07:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (759, '5947', 'Card, Gift, Novelty and Souvenir Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:55', 'system', '2025-06-04 18:07:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (760, '5948', 'Leather Goods and Luggage Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:56', 'system', '2025-06-04 18:07:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (761, '5949', 'Fabric, Needlework, Piece Goods and Sewing Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:56', 'system', '2025-06-04 18:07:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (762, '5950', 'Crystal and Glassware Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:57', 'system', '2025-06-04 18:07:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (763, '5960', 'Direct Marketing Insurance Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:58', 'system', '2025-06-04 18:07:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (764, '5962', 'Direct Marketing–Travel Related Arrangement Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:58', 'system', '2025-06-04 18:07:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (765, '5963', 'Door-to-Door Sales', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:07:59', 'system', '2025-06-04 18:07:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (766, '5964', 'Direct Marketing–Catalog Merchants', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:00', 'system', '2025-06-04 18:08:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (767, '5965', 'Direct Marketing–Combination Catalog and Retail Merchant', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:01', 'system', '2025-06-04 18:08:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (768, '5966', 'Direct Marketing–Outbound Telemarketing Merchants', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:01', 'system', '2025-06-04 18:08:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (769, '5967', 'Direct Marketing–Inbound Telemarketing Merchants', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:02', 'system', '2025-06-04 18:08:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (770, '5968', 'Direct Marketing–Continuity/Subscription Merchants', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:03', 'system', '2025-06-04 18:08:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (771, '5969', 'Direct Marketing–Other Direct Marketers–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:03', 'system', '2025-06-04 18:08:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (772, '5970', 'Artist Supply Stores, Craft Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:04', 'system', '2025-06-04 18:08:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (773, '5971', 'Art Dealers and Galleries', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:05', 'system', '2025-06-04 18:08:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (774, '5972', 'Stamp and Coin Stores–Philatelic and Numismatic Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:05', 'system', '2025-06-04 18:08:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (775, '5973', 'Religious Goods Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:06', 'system', '2025-06-04 18:08:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (776, '5975', 'Hearing Aids–Sales, Service, Supply Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:07', 'system', '2025-06-04 18:08:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (777, '5976', 'Orthopedic Goods–Artificial Limb Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:08', 'system', '2025-06-04 18:08:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (778, '5977', 'Cosmetic Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:08', 'system', '2025-06-04 18:08:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (779, '5978', 'Typewriter Stores–Rentals, Sales, Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:09', 'system', '2025-06-04 18:08:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (780, '5983', 'Fuel Dealers–Coal, Fuel Oil, Liquefied Petroleum, Wood', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:10', 'system', '2025-06-04 18:08:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (781, '5992', 'Florists', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:10', 'system', '2025-06-04 18:08:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (782, '5993', 'Cigar Stores and Stands', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:11', 'system', '2025-06-04 18:08:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (783, '5994', 'News Dealers and Newsstands', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:12', 'system', '2025-06-04 18:08:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (784, '5995', 'Pet Shops, Pet Food and Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:13', 'system', '2025-06-04 18:08:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (785, '5996', 'Swimming Pools–Sales and Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:13', 'system', '2025-06-04 18:08:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (786, '5997', 'Electric Razor Stores–Sales and Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:14', 'system', '2025-06-04 18:08:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (787, '5998', 'Tent and Awning Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:15', 'system', '2025-06-04 18:08:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (788, '5999', 'Miscellaneous and Specialty Retail Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:15', 'system', '2025-06-04 18:08:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (789, '6010', 'Manual Cash Disbursements-Customer Financial Institution', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:17', 'system', '2025-06-04 18:08:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (790, '6011', 'Automated Cash Disbursements-Customer Financial Institution', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:17', 'system', '2025-06-04 18:08:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (791, '6012', 'Merchandise and Services-Customer Financial Institution', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:18', 'system', '2025-06-04 18:08:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (792, '6050', 'Quasi Cash–Member Financial Institution M', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:19', 'system', '2025-06-04 18:08:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (793, '6051', 'Quasi Cash–Merchant', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:20', 'system', '2025-06-04 18:08:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (794, '6211', 'Securities–Brokers and Dealers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:21', 'system', '2025-06-04 18:08:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (795, '6300', 'Insurance Sales, Underwriting and Premiums', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:21', 'system', '2025-06-04 18:08:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (796, '6513', 'Real Estate Agents and Managers–Rentals', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:22', 'system', '2025-06-04 18:08:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (797, '6532', 'PAYMENT TRANSACTION-Customer Financial Institution', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:23', 'system', '2025-06-04 18:08:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (798, '6533', 'PAYMENT TRANSACTION-Merchant', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:23', 'system', '2025-06-04 18:08:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (799, '6536', 'MoneySend Intracountry', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:24', 'system', '2025-06-04 18:08:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (800, '6537', 'MoneySend Intercountry', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:25', 'system', '2025-06-04 18:08:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (801, '6538', 'MoneySend Funding', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:25', 'system', '2025-06-04 18:08:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (802, '6540', 'POI (Point of Interaction) Funding Transactions (Excluding MoneySend)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:26', 'system', '2025-06-04 18:08:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (803, '7011', 'Lodging–Hotels, Motels, Resorts–not elsewhere classified', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:08:27', 'system', '2025-06-04 18:08:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (804, '7012', 'Timeshares', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:27', 'system', '2025-06-04 18:08:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (805, '7032', 'Sporting and Recreational Camps', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:28', 'system', '2025-06-04 18:08:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (806, '7033', 'Campgrounds and Trailer Parks', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:29', 'system', '2025-06-04 18:08:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (807, '7210', 'Cleaning, Garment and Laundry Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:30', 'system', '2025-06-04 18:08:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (808, '7211', 'Laundry Services–Family and Commercial', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:30', 'system', '2025-06-04 18:08:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (809, '7216', 'Dry Cleaners', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:31', 'system', '2025-06-04 18:08:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (810, '7217', 'Carpet and Upholstery Cleaning', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:32', 'system', '2025-06-04 18:08:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (811, '7221', 'Photographic Studios', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:32', 'system', '2025-06-04 18:08:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (812, '7230', 'Barber and Beauty Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:33', 'system', '2025-06-04 18:08:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (813, '7251', 'Hat Cleaning Shops, Shoe Repair Shops, Shoe Shine Parlors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:34', 'system', '2025-06-04 18:08:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (814, '7261', 'Funeral Service and Crematories', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:35', 'system', '2025-06-04 18:08:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (815, '7273', 'Dating Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:35', 'system', '2025-06-04 18:08:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (816, '7276', 'Tax Preparation Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:36', 'system', '2025-06-04 18:08:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (817, '7277', 'Debt, Marriage, Personal–Counseling Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:37', 'system', '2025-06-04 18:08:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (818, '7278', 'Buying/Shopping Clubs, Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:37', 'system', '2025-06-04 18:08:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (819, '7296', 'Clothing Rental–Costumes, Uniforms and Formal Wear', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:38', 'system', '2025-06-04 18:08:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (820, '7297', 'Massage Parlors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:39', 'system', '2025-06-04 18:08:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (821, '7298', 'Health and Beauty Spas', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:40', 'system', '2025-06-04 18:08:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (822, '7299', 'Other Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:40', 'system', '2025-06-04 18:08:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (823, '7311', 'Advertising Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:41', 'system', '2025-06-04 18:08:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (824, '7321', 'Consumer Credit Reporting Agencies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:42', 'system', '2025-06-04 18:08:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (825, '7322', 'Collection Agents', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:42', 'system', '2025-06-04 18:08:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (826, '7333', 'Commercial Art, Graphics, Photography', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:43', 'system', '2025-06-04 18:08:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (827, '7338', 'Quick Copy, Reproduction and Blueprinting Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:44', 'system', '2025-06-04 18:08:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (828, '7339', 'Stenographic and Secretarial Support Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:45', 'system', '2025-06-04 18:08:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (829, '7342', 'Exterminating and Disinfecting Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:45', 'system', '2025-06-04 18:08:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (830, '7349', 'Cleaning and Maintenance, Janitorial Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:46', 'system', '2025-06-04 18:08:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (831, '7361', 'Employment Agencies and Temporary Help Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:47', 'system', '2025-06-04 18:08:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (832, '7372', 'Computer Programming, Data Processing and Integrated System Design Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:48', 'system', '2025-06-04 18:08:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (833, '7375', 'Information Retrieval Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:48', 'system', '2025-06-04 18:08:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (834, '7379', 'Computer Maintenance, Repair And Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:49', 'system', '2025-06-04 18:08:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (835, '7392', 'Consulting, Management and Public Relations Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:50', 'system', '2025-06-04 18:08:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (836, '7393', 'Detective Agencies, Protective Agencies, Security Services including Armored Cars, Guard Dogs', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:51', 'system', '2025-06-04 18:08:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (837, '7394', 'Equipment Rental and Leasing Services, Furniture Rental, Tool Rental', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:51', 'system', '2025-06-04 18:08:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (838, '7395', 'Photo Developing, Photofinishing Laboratories', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:52', 'system', '2025-06-04 18:08:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (839, '7399', 'Business Services Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:53', 'system', '2025-06-04 18:08:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (840, '7512', 'Automobile Rental Agency–Not Elsewhere Classified', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:08:54', 'system', '2025-06-04 18:08:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (841, '7513', 'Truck Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:08:54', 'system', '2025-06-04 18:08:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (842, '7519', 'Motor Home and Recreational Vehicle Rental', 'ENABLE', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:08:55', 'system', '2025-06-04 18:08:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (843, '7523', 'Automobile Parking Lots and Garages', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:56', 'system', '2025-06-04 18:08:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (844, '7531', 'Automotive Body Repair Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:57', 'system', '2025-06-04 18:08:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (845, '7534', 'Tire Retreading and Repair Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:57', 'system', '2025-06-04 18:08:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (846, '7535', 'Automotive Paint Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:58', 'system', '2025-06-04 18:08:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (847, '7538', 'Automotive Service Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:08:59', 'system', '2025-06-04 18:08:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (848, '7542', 'Car Washes', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:00', 'system', '2025-06-04 18:09:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (849, '7549', 'Towing Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:00', 'system', '2025-06-04 18:09:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (850, '7622', 'Electronic Repair Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:01', 'system', '2025-06-04 18:09:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (851, '7623', 'Air Conditioning and Refrigeration Repair Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:02', 'system', '2025-06-04 18:09:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (852, '7629', 'Appliance Repair Shops, Electrical and Small', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:03', 'system', '2025-06-04 18:09:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (853, '7631', 'Clock, Jewelry and Watch Repair Shops', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:03', 'system', '2025-06-04 18:09:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (854, '7641', 'Furniture–Reupholstery, Repair and Refinishing', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:04', 'system', '2025-06-04 18:09:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (855, '7692', 'Welding Repair', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:05', 'system', '2025-06-04 18:09:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (856, '7699', 'Miscellaneous Repair Shops and Related Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:05', 'system', '2025-06-04 18:09:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (857, '7800', 'Government Owned Lottery', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:06', 'system', '2025-06-04 18:09:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (858, '7801', 'Government-Licensed Casinos (Online or Internet Gambling)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:07', 'system', '2025-06-04 18:09:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (859, '7802', 'Government-Licensed Horse/Dog Racing', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:07', 'system', '2025-06-04 18:09:07', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (860, '7829', 'Motion Picture and Video Tape Production and Distribution', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:08', 'system', '2025-06-04 18:09:08', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (861, '7832', 'Motion Picture Theaters', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:09', 'system', '2025-06-04 18:09:09', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (862, '7841', 'DVD/Video Tape Rental Stores', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:10', 'system', '2025-06-04 18:09:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (863, '7911', 'Dance Halls, Schools and Studios', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:10', 'system', '2025-06-04 18:09:10', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (864, '7922', 'Theatrical Producers (Except Motion Pictures), Ticket Agencies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:11', 'system', '2025-06-04 18:09:11', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (865, '7929', 'Bands, Orchestras and Miscellaneous Entertainers–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:12', 'system', '2025-06-04 18:09:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (866, '7932', 'Pool and Billiard Establishments', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:12', 'system', '2025-06-04 18:09:12', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (867, '7933', 'Bowling Alleys', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:13', 'system', '2025-06-04 18:09:13', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (868, '7941', 'Athletic Fields, Commercial Sports, Professional Sports Clubs, Sports Promoters', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:14', 'system', '2025-06-04 18:09:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (869, '7991', 'Tourist Attractions and Exhibits', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:14', 'system', '2025-06-04 18:09:14', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (870, '7992', 'Golf Courses, Public', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:15', 'system', '2025-06-04 18:09:15', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (871, '7993', 'Video Amusement Game Supplies', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:16', 'system', '2025-06-04 18:09:16', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (872, '7994', 'Video Game Arcades and Establishments', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:17', 'system', '2025-06-04 18:09:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (873, '7995', 'Gambling Transactions Betting (Sportsbook, fantasy, social gaming; when regulated and not covered by other MCCs) (D)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:17', 'system', '2025-06-04 18:09:17', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (874, '7996', 'Amusement Parks, Carnivals, Circuses, Carnivals, Fortune Tellers', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:18', 'system', '2025-06-04 18:09:18', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (875, '7997', 'Clubs–Country Clubs, Membership (Athletic, Recreation, Sports), Private Golf Courses', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:19', 'system', '2025-06-04 18:09:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (876, '7998', 'Aquariums, Dolphinariums, Zoos and Seaquariums', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:19', 'system', '2025-06-04 18:09:19', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (877, '7999', 'Recreation Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:20', 'system', '2025-06-04 18:09:20', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (878, '8011', 'Doctors–not elsewhere classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:21', 'system', '2025-06-04 18:09:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (879, '8021', 'Dentists and Orthodontists', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:21', 'system', '2025-06-04 18:09:21', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (880, '8031', 'Osteopathic Physicians', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:22', 'system', '2025-06-04 18:09:22', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (881, '8041', 'Chiropractors', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:23', 'system', '2025-06-04 18:09:23', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (882, '8042', 'Optometrists and Ophthalmologists', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:24', 'system', '2025-06-04 18:09:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (883, '8043', 'Opticians, Optical Goods and Eyeglasses', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:24', 'system', '2025-06-04 18:09:24', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (884, '8049', 'Chiropodists, Podiatrists', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:25', 'system', '2025-06-04 18:09:25', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (885, '8050', 'Nursing and Personal Care Facilities', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:26', 'system', '2025-06-04 18:09:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (886, '8062', 'Hospitals', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:26', 'system', '2025-06-04 18:09:26', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (887, '8071', 'Dental and Medical Laboratories', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:27', 'system', '2025-06-04 18:09:27', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (888, '8099', 'Health Practitioners, Medical Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:28', 'system', '2025-06-04 18:09:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (889, '8111', 'Attorneys, Legal Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:28', 'system', '2025-06-04 18:09:28', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (890, '8211', 'Schools, Elementary and Secondary', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:29', 'system', '2025-06-04 18:09:29', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (891, '8220', 'Colleges, Universities, Professional Schools and Junior Colleges', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:30', 'system', '2025-06-04 18:09:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (892, '8241', 'Schools, Correspondence', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:30', 'system', '2025-06-04 18:09:30', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (893, '8244', 'Schools, Business and Secretarial', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:31', 'system', '2025-06-04 18:09:31', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (894, '8249', 'Schools, Trade and Vocational', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:32', 'system', '2025-06-04 18:09:32', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (895, '8299', 'Schools And Educational Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:33', 'system', '2025-06-04 18:09:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (896, '8351', 'Child Care Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:33', 'system', '2025-06-04 18:09:33', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (897, '8398', 'Organizations, Charitable and Social Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:34', 'system', '2025-06-04 18:09:34', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (898, '8641', 'Associations–Civic, Social and Fraternal', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:35', 'system', '2025-06-04 18:09:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (899, '8651', 'Political Organizations', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:35', 'system', '2025-06-04 18:09:35', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (900, '8661', 'Religious Organizations', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:36', 'system', '2025-06-04 18:09:36', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (901, '8675', 'Automobile Associations', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:37', 'system', '2025-06-04 18:09:37', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (902, '8699', 'Membership Organizations–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:38', 'system', '2025-06-04 18:09:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (903, '8734', 'Testing Laboratories (Non-Medical)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:38', 'system', '2025-06-04 18:09:38', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (904, '8911', 'Architectural, Engineering and Surveying Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:39', 'system', '2025-06-04 18:09:39', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (905, '8931', 'Accounting, Auditing and Bookkeeping Services', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:40', 'system', '2025-06-04 18:09:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (906, '8999', 'Professional Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:40', 'system', '2025-06-04 18:09:40', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (907, '9211', 'Court Costs Including Alimony and Child Support', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:41', 'system', '2025-06-04 18:09:41', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (908, '9222', 'Fines', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:42', 'system', '2025-06-04 18:09:42', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (909, '9223', 'Bail and Bond Payments', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:43', 'system', '2025-06-04 18:09:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (910, '9311', 'Tax Payments', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:43', 'system', '2025-06-04 18:09:43', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (911, '9399', 'Government Services–Not Elsewhere Classified', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:44', 'system', '2025-06-04 18:09:44', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (912, '9402', 'Postal Services–Government Only', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:45', 'system', '2025-06-04 18:09:45', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (913, '9405', 'Intra-Government Purchases–Government Only', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:46', 'system', '2025-06-04 18:09:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (914, '9406', 'Government Owned Lottery(Non US region)', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:46', 'system', '2025-06-04 18:09:46', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (915, '9702', 'GCAS Emergency Service', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:47', 'system', '2025-06-04 18:09:47', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (916, '9950', 'Intra-Company Purchases', 'ENABLE', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:48', 'system', '2025-06-04 18:09:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (917, '0743', 'Wine Producers', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:48', 'system', '2025-06-04 18:09:48', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (918, '0744', 'Champagne Producers', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:49', 'system', '2025-06-04 18:09:49', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (919, '3092', 'MIDWAY AIRLINES', 'DISABLED', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:09:50', 'system', '2025-06-04 18:09:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (920, '3192', 'IRAN AIR', 'DISABLED', 'ENABLE', '30', 'EN', '', 'system', '2025-06-04 18:09:50', 'system', '2025-06-04 18:09:50', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (921, '4458', 'Tobacco logistics', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:51', 'system', '2025-06-04 18:09:51', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (922, '4733', 'TICKET SALES-SCENIC SPOTS', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:52', 'system', '2025-06-04 18:09:52', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (923, '5333', 'HYPERMARKET', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:53', 'system', '2025-06-04 18:09:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (924, '5398', 'CORPORATE WHOLESALE', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:53', 'system', '2025-06-04 18:09:53', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (925, '5974', 'RUBBER STAMP STORES', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:54', 'system', '2025-06-04 18:09:54', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (926, '6381', 'INSURANCE PREMIUME', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:55', 'system', '2025-06-04 18:09:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (927, '6399', 'INSURANCE-NOT ELSEWHERE CLASSIFIED', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:55', 'system', '2025-06-04 18:09:55', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (928, '6529', 'Payment Transactions – Member Financial Institution', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:56', 'system', '2025-06-04 18:09:56', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (929, '6530', 'Payment Transactions-Merchant', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:57', 'system', '2025-06-04 18:09:57', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (930, '6531', 'Payment Services Provider – Money Transfer For A Purchase', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:58', 'system', '2025-06-04 18:09:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (931, '6534', 'Money Transfer – Member Financial Institution', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:58', 'system', '2025-06-04 18:09:58', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (932, '6535', 'Value Purchase – Member Financial Institution', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:09:59', 'system', '2025-06-04 18:09:59', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (933, '7013', 'REAL ESTATE AGENT-BROKERS', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:00', 'system', '2025-06-04 18:10:00', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (934, '7280', 'HOSPTIAL PATIENT PERSONAL FUNDS WITHDRAWAL ACCOUNTS', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:01', 'system', '2025-06-04 18:10:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (935, '7295', 'BABYSITTING SERVICES', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:01', 'system', '2025-06-04 18:10:01', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (936, '8044', 'EYEGLASSES OPTICAL GOODS STORES', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:02', 'system', '2025-06-04 18:10:02', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (937, '8082', 'HEALTH CARE HOME', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:03', 'system', '2025-06-04 18:10:03', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (938, '8912', 'ORNAMENTS AND GARDENING', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:04', 'system', '2025-06-04 18:10:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (939, '9400', 'EMBASSY FEE PAYMENTS', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:04', 'system', '2025-06-04 18:10:04', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (940, '9498', 'Credit Card Repayment', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:05', 'system', '2025-06-04 18:10:05', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (941, '9751', 'UK SUPERMARKET', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:06', 'system', '2025-06-04 18:10:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (942, '9793', 'PROMOTION REWARDS, CASHBACK (JCBI USE ONLY)', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:06', 'system', '2025-06-04 18:10:06', 0);
INSERT INTO kc_mcc_info (id, mcc_code, mcc_desc, status, pre_auth_status, release_days, language, remark, create_user, create_time, update_user, update_time, is_deleted) VALUES (943, '9999', 'Other categories which are defined by other payment schemes', 'DISABLED', 'DISABLED', '7', 'EN', '', 'system', '2025-06-04 18:10:07', 'system', '2025-06-04 18:10:07', 0);

-- 字典表新增开卡币种、渠道类型、卡片导出字典类型
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'CARD_CURRENCY', 'EUR', '欧元', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'CARD_CURRENCY', 'EUR', '歐元', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'CARD_CURRENCY', 'EUR', 'Euro', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'CARD_CURRENCY', 'AUD', '澳元', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'CARD_CURRENCY', 'AUD', '澳元', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'CARD_CURRENCY', 'AUD', 'Australian Dollar', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'CARD_CURRENCY', 'JPY', '日元', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'CARD_CURRENCY', 'JPY', '日元', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'CARD_CURRENCY', 'JPY', 'Japanese Yen', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'CHANNEL_TYPE', 'BPC-GW', 'BPC-GW', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'CHANNEL_TYPE', 'BPC-GW', 'BPC-GW', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'CHANNEL_TYPE', 'BPC-GW', 'BPC-GW', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'OPERATION_CARD_EXPORT', 'MEMBER_ID', '商戶編號', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'OPERATION_CARD_EXPORT', 'MEMBER_NAME', '商户名称', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'OPERATION_CARD_EXPORT', 'CHANNEL_NAME', '通道名称', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'OPERATION_CARD_EXPORT', 'MEMBER_ID', '商戶編号', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'OPERATION_CARD_EXPORT', 'MEMBER_NAME', '商戶名稱', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'OPERATION_CARD_EXPORT', 'CHANNEL_NAME', '通道名稱', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'OPERATION_CARD_EXPORT', 'MEMBER_ID', 'Merchant ID', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'OPERATION_CARD_EXPORT', 'MEMBER_NAME', 'Merchant Name', '', 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'OPERATION_CARD_EXPORT', 'CHANNEL_NAME', 'Channel Name', '', 1);

-- 补充VCC商户的卡来源字段
UPDATE kc_card_info
SET `system` = 'VCC'
WHERE member_id IN (
    SELECT member_id FROM kc_kyc_main_info
    WHERE member_channel = 'VCC'
);


-- 配置卡核心基础通道配置 卡核心地址暂未提供待后续提供
INSERT INTO kc_channel_base_info (id, channel_type, channel_name, is_enabled, use_scene, channel_base_url, auth_config, channel_authorize_code, channel_private_key, fee_config_menu, bins, avail_start_time, avail_end_time, remark, create_user, create_time, update_user, update_time, del_flag, card_type, bins_mode, fee_type, sys_fee_type, reference_member_id) VALUES (4, 'BPC-GW', 'BPC-GW', 0, '测试环境', null, null, 'YW', null, null, '[{"cardBin":"493875592","desc":"卡BIN描述"},{"cardBin":"493875590","desc":"卡BIN描述"},{"cardBin":"441359770","desc":"卡BIN描述"}]', now(), '2099-01-01 00:00:00', null, 'system', now(), 'SYSTEM', now(), 0, '0,1', 'SPECIFY', '', null, null);

INSERT INTO kc_channel_ccy_info (`channel_id`, `channel_name`, `ccy`, `create_user`, `create_time`, `update_user`, `update_time`, `del_flag`) VALUES (4, 'BPC-GW', 'USD', 'SYSTEM', now(), NULL, NULL, 0);
INSERT INTO kc_channel_ccy_info (`channel_id`, `channel_name`, `ccy`, `create_user`, `create_time`, `update_user`, `update_time`, `del_flag`) VALUES ( 4, 'BPC-GW', 'HKD', 'SYSTEM', now(), NULL, NULL, 0);
INSERT INTO kc_channel_ccy_info (`channel_id`, `channel_name`, `ccy`, `create_user`, `create_time`, `update_user`, `update_time`, `del_flag`) VALUES ( 4, 'BPC-GW', 'AUD', 'SYSTEM', now(), NULL, NULL, 0);
INSERT INTO kc_channel_ccy_info (`channel_id`, `channel_name`, `ccy`, `create_user`, `create_time`, `update_user`, `update_time`, `del_flag`) VALUES ( 4, 'BPC-GW', 'EUR', 'SYSTEM', now(), NULL, NULL, 0);
INSERT INTO kc_channel_ccy_info (`channel_id`, `channel_name`, `ccy`, `create_user`, `create_time`, `update_user`, `update_time`, `del_flag`) VALUES ( 4, 'BPC-GW', 'JPY', 'SYSTEM', now(), NULL, NULL, 0);
