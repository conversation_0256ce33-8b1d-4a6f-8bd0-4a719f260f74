-- 新增退款账户提现字典
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-CN', 'TRANSACTION_TYPE', 'REFUND_ACCOUNT_OUT', '退款账户转出', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('zh-TW', 'TRANSACTION_TYPE', 'REFUND_ACCOUNT_OUT', '退款賬戶轉出', null, 1);
INSERT INTO kc_dict (language, dict_type, dict_key, dict_value, remark, is_enabled) VALUES ('en', 'TRANSACTION_TYPE', 'REFUND_ACCOUNT_OUT', 'Transfer out of refund account', null, 1);

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100271, '退款账户-转出', 0, 1, '', null, null, '', 1, 0, 'F', '0', '0', 'overview:account:refund', '#', '<EMAIL>', now(), '<EMAIL>', now(), '', 'CIS');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, domain) VALUES (100272, '更新密钥', 0, 99, '', null, null, '', 1, 0, 'F', '0', '0', 'secure:data:update', '#', '<EMAIL>', now(), '', now(), '', 'IOP');

alter table kc_card_info
    add encrypted_id bigint default 1 null comment '加密使用的密钥ID编号' after rule_type;

create table kun_data_key
(
    id                 bigint auto_increment comment '自增主键id'
        primary key,
    biz_tag            varchar(64)  not null comment '业务系统标记',
    encrypted_data_key varchar(512) not null comment '密钥密文',
    security_key_id    bigint       not null comment '加密密钥id',
    valid_flag         tinyint(1)   not null comment '有效标记 1=有效, 0=无效',
    checksum           varchar(64)  null comment '密钥校验值',
    remark             varchar(255) null comment '备注',
    expire_time        datetime     not null comment '过期时间',
    create_time        datetime     null comment '创建时间',
    update_time        datetime     null comment '更新时间',
    KEY idx_tag_valid (biz_tag, valid_flag)
)
    comment '数据密钥表';

create table kun_security_key
(
    id          bigint auto_increment comment '主键ID'
        primary key,
    biz_tag     varchar(24)  not null comment '业务系统标记',
    plain_key   varchar(256) not null comment '密钥明文',
    valid_flag  tinyint(1)   not null comment '有效标记 1=有效, 0=无效',
    checksum    varchar(64)  null comment '校验值',
    remark      varchar(256) null comment '备注',
    expire_time datetime     not null comment '过期时间',
    create_time datetime     not null comment '创建时间',
    update_time datetime     not null comment '更新时间'
)
    comment '加密密钥表';

INSERT INTO kun_data_key (id, biz_tag, encrypted_data_key, security_key_id, valid_flag, checksum, remark, expire_time, create_time, update_time) VALUES (1, 'KCARD', '00b5f229f9500419ace405359ba4f4983f2c0bb4ae52c59483b584d82da1948471f174079cfb537e86542daebc7f8518', 1, 1, 'bee3d22f', null, '2026-01-19 14:56:41', now(), now());
INSERT INTO kun_security_key (id, biz_tag, plain_key, valid_flag, checksum, remark, expire_time, create_time, update_time) VALUES (1, 'KCARD', 'dfeebf55a719655af1c0b6e74361ed3815e3cf662cb515d33764682b184b21d1', 1, 'f76afbac', null, '2026-07-23 14:56:41', now(), now());

alter table kc_card_info modify exp_date varchar(256) null comment '有效日期';

update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4072;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4073;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4074;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4075;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4076;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4077;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4078;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4079;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4080;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4081;
update kc_card_info set exp_date = '460c380acb6e0d500df1488b4ff4ec48' where id = 4082;


