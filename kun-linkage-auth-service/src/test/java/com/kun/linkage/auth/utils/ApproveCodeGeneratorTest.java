package com.kun.linkage.auth.utils;

import com.kun.linkage.auth.KunLinkageAuthServiceApplicationTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import static org.junit.jupiter.api.Assertions.*;

public class ApproveCodeGeneratorTest extends KunLinkageAuthServiceApplicationTest {
    
    @Resource
    private ApproveCodeGenerator approveCodeGenerator;

    @Test
    void generate() {
        for (int i = 0; i < 1000; i++) {
            log.info("生成的授权码：{}", approveCodeGenerator.generate());
        }
    }
}