package com.kun.linkage.auth.mq;

import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.service.AuthTransactionFeeService;
import com.kun.linkage.auth.utils.AuthLarkAlarmUtil;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import com.kun.common.util.mq.RocketMqService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TransactionFeeListener 单元测试
 * 
 * 主要测试告警去重机制的正确性
 */
@ExtendWith(MockitoExtension.class)
class TransactionFeeListenerTest {

    @Mock
    private AuthTransactionFeeService authTransactionFeeService;

    @Mock
    private RocketMqService rocketMqService;

    @Mock
    private AuthLarkAlarmUtil authLarkAlarmUtil;

    @Mock
    private RedissonCacheUtil redissonCacheUtil;

    @InjectMocks
    private TransactionFeeListener transactionFeeListener;

    private AuthTransactionContextDTO testContext;

    @BeforeEach
    void setUp() {
        testContext = new AuthTransactionContextDTO();
        testContext.setTransactionId("TEST_TRANSACTION_001");
        testContext.setRetryTimes(6); // 已达到最大重试次数
    }

    @Test
    void testAlarmDeduplication_FirstAlarm_ShouldSendAlarm() {
        // 模拟处理失败且达到最大重试次数
        Result<OrganizationFeeDetail> failResult = Result.fail("PROCESSING_ERROR");
        when(authTransactionFeeService.processTransactionFee(testContext)).thenReturn(failResult);
        
        // 模拟Redis中不存在告警去重键（第一次告警）
        when(redissonCacheUtil.exists(anyString())).thenReturn(false);

        // 执行测试
        transactionFeeListener.onMessage(testContext);

        // 验证告警被发送
        verify(authLarkAlarmUtil, times(1)).sendTransactionFeeAlarm(
            eq(testContext), 
            eq("PROCESSING_ERROR"), 
            eq("处理失败"), 
            eq(6)
        );
        
        // 验证设置了去重键
        verify(redissonCacheUtil, times(1)).set(
            eq("transaction_fee_alarm_dedup:TEST_TRANSACTION_001"),
            anyLong(),
            eq(24L),
            eq(TimeUnit.HOURS)
        );
    }

    @Test
    void testAlarmDeduplication_DuplicateAlarm_ShouldNotSendAlarm() {
        // 模拟处理失败且达到最大重试次数
        Result<OrganizationFeeDetail> failResult = Result.fail("PROCESSING_ERROR");
        when(authTransactionFeeService.processTransactionFee(testContext)).thenReturn(failResult);
        
        // 模拟Redis中已存在告警去重键（重复告警）
        when(redissonCacheUtil.exists(anyString())).thenReturn(true);

        // 执行测试
        transactionFeeListener.onMessage(testContext);

        // 验证告警没有被发送
        verify(authLarkAlarmUtil, never()).sendTransactionFeeAlarm(any(), any(), any(), anyInt());
        
        // 验证没有设置去重键
        verify(redissonCacheUtil, never()).set(anyString(), anyLong(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testAlarmDeduplication_ExceptionHandling_ShouldFallbackToSendAlarm() {
        // 模拟处理失败且达到最大重试次数
        Result<OrganizationFeeDetail> failResult = Result.fail("PROCESSING_ERROR");
        when(authTransactionFeeService.processTransactionFee(testContext)).thenReturn(failResult);
        
        // 模拟Redis操作异常
        when(redissonCacheUtil.exists(anyString())).thenThrow(new RuntimeException("Redis连接异常"));

        // 执行测试
        transactionFeeListener.onMessage(testContext);

        // 验证降级发送告警
        verify(authLarkAlarmUtil, times(1)).sendTransactionFeeAlarm(
            eq(testContext), 
            eq("PROCESSING_ERROR"), 
            eq("处理失败"), 
            eq(6)
        );
    }

    @Test
    void testSuccessfulProcessing_ShouldNotSendAlarm() {
        // 模拟处理成功
        OrganizationFeeDetail feeDetail = new OrganizationFeeDetail();
        feeDetail.setId(123L);
        Result<OrganizationFeeDetail> successResult = Result.success(feeDetail);
        when(authTransactionFeeService.processTransactionFee(testContext)).thenReturn(successResult);

        // 执行测试
        transactionFeeListener.onMessage(testContext);

        // 验证没有发送告警
        verify(authLarkAlarmUtil, never()).sendTransactionFeeAlarm(any(), any(), any(), anyInt());
        verify(redissonCacheUtil, never()).exists(anyString());
    }

    @Test
    void testRetryLogic_ShouldNotSendAlarmBeforeMaxRetries() {
        // 模拟处理失败但未达到最大重试次数
        testContext.setRetryTimes(3); // 未达到最大重试次数
        Result<OrganizationFeeDetail> failResult = Result.fail("PROCESSING_ERROR");
        when(authTransactionFeeService.processTransactionFee(testContext)).thenReturn(failResult);

        // 执行测试
        transactionFeeListener.onMessage(testContext);

        // 验证发送了重试消息
        verify(rocketMqService, times(1)).delayedSend(anyString(), any(), anyInt(), anyInt());
        
        // 验证没有发送告警
        verify(authLarkAlarmUtil, never()).sendTransactionFeeAlarm(any(), any(), any(), anyInt());
        verify(redissonCacheUtil, never()).exists(anyString());
    }
}
