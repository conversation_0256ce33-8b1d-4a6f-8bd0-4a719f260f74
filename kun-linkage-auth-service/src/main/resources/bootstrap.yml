# Spring
spring:
  application:
    # 应用名称
    name: kun-linkage-auth
  profiles:
    # 环境配置
    active: local
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    modules:
      - com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
  messages:
    basename: i18n/messages
    encoding: UTF-8
    default-locale: en
  sleuth:
    # 采样率配置
    sampler:
      probability: 1.0
    # 是否启用web
    web:
      enabled: true
    # 是否启用消息
    messaging:
      enabled: true
    # 是否启用rpc
    rpc:
      enabled: true
    # 是否启用redis
    redis:
      enabled: true
    # 是否启用jdbc
    jdbc:
      enabled: true
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    async:
      request-timeout: 6000000
    servlet:
      path: /linkage-auth

uid:
  enable: true

feign:
  # 启用sentinel
  sentinel:
    enabled: true
  client:
    config:
      # 默认配置
      default:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full
      # kun-linkage-auth服务配置
      kun-linkage-account:
        connectTimeout: 15000
        readTimeout: 15000
        loggerLevel: full

# 外部接口调用性能优化配置
external:
  api:
    # 连接池配置
    connection:
      max-total: 200                    # 最大连接数
      default-max-per-route: 50         # 每个路由最大连接数
      validate-after-inactivity: 30000  # 连接空闲30秒后验证
      evict-idle-connections: 60        # 清理空闲连接间隔（秒）
    # 超时配置
    timeout:
      connect: 5000                     # 连接超时（毫秒）
      read: 30000                       # 读取超时（毫秒）
      request: 10000                    # 请求连接超时（毫秒）
    # 重试配置
    retry:
      max-attempts: 3                   # 最大重试次数
      backoff-multiplier: 2.0           # 退避乘数
      initial-interval: 1000            # 初始重试间隔（毫秒）
    # 监控配置
    monitoring:
      enabled: true                     # 是否启用监控
      log-slow-queries: true            # 是否记录慢查询
      slow-query-threshold: 5000        # 慢查询阈值（毫秒）
    # Redis存储配置
    redis:
      enabled: true                     # 是否启用Redis存储
      data-retention-days: 30           # 数据保留天数

kun:
  linkage:
    cache:
      enabled: true