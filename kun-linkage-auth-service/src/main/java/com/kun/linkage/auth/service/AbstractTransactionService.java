package com.kun.linkage.auth.service;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.common.util.mq.RocketMqService;
import com.kun.common.util.uid.DateUtils;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.account.facade.api.AccountTransactionFacade;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.ext.mapper.AuthFlowMapperExt;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.constant.TransactionStatusEnum;
import com.kun.linkage.auth.facade.vo.webhook.WebHookResult;
import com.kun.linkage.auth.facade.constant.TransactionTypeCatogoryEnum;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.auth.facade.vo.webhook.AuthResultWebHookRequestVO;
import com.kun.linkage.auth.utils.ApproveCodeGenerator;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.auth.utils.ResponseMessageUtil;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.AuthAccountLogMapper;
import com.kun.linkage.common.db.mapper.AuthFlowExtMapper;
import com.kun.linkage.common.db.mapper.AuthFlowMapper;
import com.kun.linkage.common.db.mapper.AuthRequestLogMapper;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.enums.BusinessAccountTypeEnum;
import com.kun.linkage.customer.facade.enums.CardActiveStatusEnum;
import com.kun.linkage.customer.facade.enums.CardStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 授权交易抽象服务类
 * <p>
 * 提供授权交易相关的通用校验、日志保存、数据库操作等基础能力，供具体业务实现继承。 主要功能包括： 1. 请求日志保存 2. 数据库参数校验（包括重复性校验、商户KYC校验、卡状态校验等） 3. 原交易查询和校验
 */
@Service
public abstract class AbstractTransactionService {

    private static final Logger log = LoggerFactory.getLogger(AbstractTransactionService.class);

    /** 国际化消息服务 */
    @Resource
    protected I18nMessageService i18nMessageService;
    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;
    /** 授权请求日志Mapper */
    @Resource
    protected AuthRequestLogMapper authRequestLogMapper;
    /** 机构信息 */
    @Resource
    protected OrganizationBasicInfoService organizationBasicInfoService;
    /** 机构客户卡信息 */
    @Resource
    protected OrganizationCustomerCardInfoService organizationCustomerCardInfoService;
    /** 机构客户账户信息 */
    @Resource
    protected OrganizationCustomerAccountInfoService organizationCustomerAccountInfoService;
    /** 授权流水Mapper */
    @Resource
    protected AuthFlowMapper authFlowMapper;
    /** 授权码生成器 */
    @Resource
    protected ApproveCodeGenerator approveCodeGenerator;
    @Resource
    protected AuthFlowMapperExt authFlowMapperExt;
    @Resource
    protected AuthFlowExtMapper authFlowExtMapper;
    @Resource
    protected AccountTransactionFacade accountTransactionFacade;
    @Resource
    protected AuthAccountLogMapper authAccountLogMapper;
    @Resource
    protected RocketMqService rocketMqService;
    @Resource
    protected Tracer tracer;
    @Resource
    protected RedissonLockUtil redissonLockUtil;

    @Resource
    protected OrganizationCustomerLimitBizService organizationCustomerLimitBizService;

    @Resource
    protected ExternalWebhookService externalWebhookService;

    @Resource
    protected TransactionAccountingService transactionAccountingService;

    /**
     * 保存请求日志
     * <p>
     * 生成唯一交易ID，保存请求上下文到数据库日志表。 使用雪花算法生成唯一ID，确保分布式环境下的ID唯一性。
     *
     * @param authTransactionContextDTO 交易上下文，包含请求信息和响应信息
     */
    public void saveRequestLog(AuthTransactionContextDTO authTransactionContextDTO) {
        long snowId = uidGenerator.getUID();
        authTransactionContextDTO.setTransactionId(String.valueOf(snowId));
        AuthRequestLog authRequestLog = new AuthRequestLog();
        authRequestLog.setAuthFlowId(snowId);
        authRequestLog.setRequestJson(JSON.toJSONString(authTransactionContextDTO.getBaseAuthRequestVO()));
        authRequestLog.setCreateTime(DateTimeUtils.getCurrentDateTime());
        int insert = authRequestLogMapper.insert(authRequestLog);
        log.info("保存请求日志: {}, 影响行数: {}", authRequestLog, insert);
    }

    /**
     * 数据库参数校验
     * <p>
     * 校验请求是否重复、商户是否存在且通过KYC、卡信息及状态、原交易等。 校验失败时直接设置响应VO并返回，否则返回null。 校验流程： 1. 重复性校验（24小时内同一请求ID+处理方） 2. 商户KYC校验 3.
     * 卡信息及状态校验 4. 原交易校验（如有）
     *
     * @param authTransactionContextDTO 交易上下文
     * @return 错误响应VO，校验通过返回null
     */
    public BaseAuthResponseVO verifyParameterFromDB(AuthTransactionContextDTO authTransactionContextDTO) {
        // 1. 重复性校验（24小时内同一请求ID+处理方）
        List<AuthFlow> authFlows = authFlowMapper.selectList(
            new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getProcessorRequestId,
                    authTransactionContextDTO.getBaseAuthRequestVO().getRequestId())
                .eq(AuthFlow::getProcessor, authTransactionContextDTO.getBaseAuthRequestVO().getProcessor())
                .gt(AuthFlow::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -1))
                .lt(AuthFlow::getCreateTime, DateTimeUtils.getCurrentDateTime()));
        if (authFlows.size() > 0) {
            log.warn("重复请求: {}", authTransactionContextDTO.getBaseAuthRequestVO().getRequestId());
            BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
            responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.DUPLICATE_TRANSACIION.getCode());
            responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
            return responseVO;
        }
        // 2. 校验商户是否存在且通过KYC
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoService.getOrganizationBasicInfo(
            authTransactionContextDTO.getBaseAuthRequestVO().getMID());
        if (organizationBasicInfo == null) {
            log.warn("商户信息不存在: {}", authTransactionContextDTO.getBaseAuthRequestVO().getMID());
            BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
            responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.MID_NOT_FOUND.getCode());
            responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
            return responseVO;
        } else if (!ValidStatusEnum.VALID.getValue().equals(organizationBasicInfo.getStatus())) {
            log.warn("商户状态无效: {}, 状态: {}", authTransactionContextDTO.getBaseAuthRequestVO().getMID(),
                organizationBasicInfo.getStatus());
            BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
            responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.MID_STATUS_INVALID.getCode());
            responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
            return responseVO;
        } else {
            authTransactionContextDTO.setOrganizationBasicInfo(organizationBasicInfo);
        }

        // 3. 检查卡ID是否存在及卡状态
        OrganizationCustomerCardInfo organizationCustomerCardInfo =
            organizationCustomerCardInfoService.getOrganizationCustomerCardInfo(
                authTransactionContextDTO.getBaseAuthRequestVO().getMID(),
                authTransactionContextDTO.getBaseAuthRequestVO().getProcessor(),
                authTransactionContextDTO.getBaseAuthRequestVO().getGatewayCardId());

        if (organizationCustomerCardInfo != null) {
            // 根据交易类型检查卡状态是否正常
            if (!isCardStatusValidForTransaction(organizationCustomerCardInfo, authTransactionContextDTO)) {
                log.warn("卡状态异常: {}, 交易类型: {}", organizationCustomerCardInfo.getCardId(),
                    authTransactionContextDTO.getTransactionTypeEnum());
                BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.CARD_STATUS_ERROR.getCode());
                responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                return responseVO;
            }
            if (!StringUtils.equals(organizationCustomerCardInfo.getCurrencyCode(), authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency())){
                log.warn("卡币种不匹配: {}, 请求币种: {}", organizationCustomerCardInfo.getCurrencyCode(),
                    authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency());
                BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.CARD_NOT_FOUND.getCode());
                responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                return responseVO;
            }
            authTransactionContextDTO.setOrganizationCustomerCardInfo(organizationCustomerCardInfo);
        } else {
            log.warn("卡ID不存在: {}", authTransactionContextDTO.getBaseAuthRequestVO().getGatewayCardId());
            BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
            responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.CARD_NOT_FOUND.getCode());
            responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
            return responseVO;
        }

        List<OrganizationCustomerAccountInfo> organizationCustomerAccountInfos =
            organizationCustomerAccountInfoService.getOrganizationCustomerAccountInfo(
                organizationCustomerCardInfo.getOrganizationNo(), organizationCustomerCardInfo.getCustomerId(),
                authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency(),
                ValidStatusEnum.VALID.getValue());
        if (organizationCustomerAccountInfos != null && organizationCustomerAccountInfos.size() > 0) {
            boolean hasBasicAccount = false;
            // 预留
            boolean hasCreditAccount = false;
            // 预留
            boolean hasCryptoAccount = false;
            for (OrganizationCustomerAccountInfo customerAccountInfo : organizationCustomerAccountInfos) {
                if (BusinessAccountTypeEnum.BASIC.getValue().equals(customerAccountInfo.getAccountType())) {
                    authTransactionContextDTO.setCustomerBasicAccount(customerAccountInfo);
                    hasBasicAccount = true;
                } else if (BusinessAccountTypeEnum.CREDIT.getValue().equals(customerAccountInfo.getAccountType())) {
                    authTransactionContextDTO.setCustomerCreditAccount(customerAccountInfo);
                    hasCreditAccount = true;
                } else if (BusinessAccountTypeEnum.CRYPTO.getValue().equals(customerAccountInfo.getAccountType())) {
                    authTransactionContextDTO.setCustomerCryptoAccount(customerAccountInfo);
                    hasCryptoAccount = true;
                }
            }
            if (!hasBasicAccount) {
                log.error("商户没有基本账户: {}", organizationCustomerCardInfo.getOrganizationNo());
                BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.CAN_NOT_BE_ACCOUNTING.getCode());
                responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                return responseVO;
            }
        } else {
            log.error("客户没有账户信息, 商户号:{}, 客户号: {}, 币种: {}", organizationCustomerCardInfo.getOrganizationNo(), organizationCustomerCardInfo.getCustomerId(), authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency());
            BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
            responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.CAN_NOT_BE_ACCOUNTING.getCode());
            responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
            return responseVO;
        }

        // 检查交易金额是否符合要求
        if (!isTransactionAmountValid(authTransactionContextDTO)) {
            log.warn("交易金额不符合要求: {}", authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount());
            BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
            responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.TRANSACTION_AMOUNT_EXCEEDS_LIMIT.getCode());
            responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
            return responseVO;
        }

        if (authTransactionContextDTO.getBaseAuthRequestVO()
            .getErrorCode() != null && !KunLinkageAuthResponseCodeConstant.SUCCESS.getCode()
            .equals(authTransactionContextDTO.getBaseAuthRequestVO().getErrorCode())) {
            // 如果请求中已经有错误码，直接跳过检查去保存数据即可
            return null;
        }
        // 4. 检查原交易是否存在（如有原始请求ID）
        if (StringUtils.isNotBlank(authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId())) {
            // 校验原交易相关参数
            if (StringUtils.isBlank(authTransactionContextDTO.getBaseAuthRequestVO().getAcquireReferenceNo())) {
                log.info("acquireReferenceNo is null");
                authTransactionContextDTO.getBaseAuthResponseVO()
                    .setReturnCode(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode());
                authTransactionContextDTO.getBaseAuthResponseVO().setErrorMessage(ResponseMessageUtil.getResponseMsg(
                    authTransactionContextDTO.getBaseAuthResponseVO().getReturnCode(), null, i18nMessageService,
                    "acquireReferenceNo"));
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }
            if (StringUtils.isBlank(authTransactionContextDTO.getBaseAuthRequestVO().getProcessorExt1())) {
                log.info("processorExt1 is null");
                authTransactionContextDTO.getBaseAuthResponseVO()
                    .setReturnCode(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode());
                authTransactionContextDTO.getBaseAuthResponseVO().setErrorMessage(ResponseMessageUtil.getResponseMsg(
                    authTransactionContextDTO.getBaseAuthResponseVO().getReturnCode(), null, i18nMessageService,
                    "processorExt1"));
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }
            // 查询原交易
            AuthFlow originalAuthFlow = authFlowMapper.selectOne(
                new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getMerchantNo,
                        authTransactionContextDTO.getBaseAuthRequestVO().getMID()).eq(AuthFlow::getProcessorTransId,
                        authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId())
                    .eq(AuthFlow::getProcessor, authTransactionContextDTO.getBaseAuthRequestVO().getProcessor())
                    .eq(AuthFlow::getProcessorCardId, authTransactionContextDTO.getBaseAuthRequestVO().getProcessorCardId())
                    .eq(AuthFlow::getStatus, TransactionStatusEnum.SUCCESS.getCode())
                    .gt(AuthFlow::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -180))
                    .lt(AuthFlow::getCreateTime, DateTimeUtils.getCurrentDateTime()));
            if (originalAuthFlow == null) {
                // 如果原交易不存在，且不是冲正交易，则返回错误
                if (TransactionTypeCatogoryEnum.REVERSAL.equals(
                    authTransactionContextDTO.getTransactionTypeCatogoryEnum())) {
                    // 根据其它字段重新查询原交易
                    List<AuthFlow> originalList = this.getOriginalAuthFlowList(authTransactionContextDTO, -35);
                    if (originalList != null && originalList.size() > 0) {
                        // 找到原交易
                        authTransactionContextDTO.setFirstOriginalAuthFlow(originalList.get(0));
                        originalAuthFlow = originalList.get(0);
                    } else {
                        // 原交易不存在，且没有找到其它匹配的交易
                        log.info("冲正交易原交易不存在: {}",
                            authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId());
                        return null;
                    }
                    // 如果是冲正交易，原交易可以不存在
                    log.info("冲正交易原交易不存在: {}",
                        authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId());
                    return null;
                }
                log.info("原交易不存在: {}", authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId());
                BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.ORIGINAL_TRANSACTION_NOT_FOUND.getCode());
                responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                return responseVO;
            } else if (StringUtils.isNotBlank(originalAuthFlow.getOriginalId())) {
                // 如果原交易存在且有原始ID，说明是原交易的冲正/撤销/完成等操作
                List<AuthFlow> originalList = this.getOriginalAuthFlowList(authTransactionContextDTO, -180);
                if (originalList.size() > 0) {
                    // 找到原交易
                    authTransactionContextDTO.setFirstOriginalAuthFlow(originalList.get(0));
                } else {
                    // 如果没有找到原交易，说明原交易和第一笔交易是同一笔
                    authTransactionContextDTO.setFirstOriginalAuthFlow(originalAuthFlow);
                }
            } else {
                // 如果原交易存在但没有原始ID，说明是原交易和第一笔交易是同一笔
                authTransactionContextDTO.setFirstOriginalAuthFlow(originalAuthFlow);
            }
            authTransactionContextDTO.setOriginalAuthFlow(originalAuthFlow);
            // 根据交易类型检查交易金额
            switch (authTransactionContextDTO.getTransactionTypeEnum()) {
                case AUTHORIZATION:
                case PRE_AUTH:
                case CASH_ADVANCE:
                case TRANSFER_OUT:
                case TRANSFER_IN:
                    // 无需检查交易金额
                    break;
                case REFUND:
                    // 退款交易需要检查原交易金额是否大于等于退款金额
                    if (authTransactionContextDTO.getFirstOriginalAuthFlow() != null && authTransactionContextDTO.getBaseAuthRequestVO()
                        .getTransAmount().compareTo(
                            authTransactionContextDTO.getFirstOriginalAuthFlow().getRemainingTransAmount()) > 0) {
                        log.warn("退款金额超过原交易金额: {}",
                            authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount());
                        BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                        responseVO.setReturnCode(
                            KunLinkageAuthResponseCodeConstant.TRANSACTION_AMOUNT_EXCEEDS_LIMIT.getCode());
                        responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                        return responseVO;
                    }
                    break;
                case PRE_AUTH_COMPLETION:
                    // TODO 根据是否允许超过原交易金额来决定是否检查

                    break;
                case AUTHORIZATION_VOID:
                case PRE_AUTH_VOID:
                case PRE_AUTH_COMPLETION_VOID:
                case CASH_ADVANCE_VOID:
                case TRANSFER_OUT_VOID:
                case TRANSFER_IN_VOID:
                case REFUND_VOID:
                case AUTHORIZATION_REVERSAL:
                case PRE_AUTH_REVERSAL:
                case PRE_AUTH_COMPLETION_REVERSAL:
                case CASH_ADVANCE_REVERSAL:
                case REFUND_REVERSAL:
                case TRANSFER_OUT_REVERSAL:
                case TRANSFER_IN_REVERSAL:
                case AUTHORIZATION_VOID_REVERSAL:
                case PRE_AUTH_VOID_REVERSAL:
                case PRE_AUTH_COMPLETION_VOID_REVERSAL:
                case CASH_ADVANCE_VOID_REVERSAL:
                case REFUND_VOID_REVERSAL:
                case TRANSFER_OUT_VOID_REVERSAL:
                case TRANSFER_IN_VOID_REVERSAL:
                    // 撤销/冲正交易需要检查原交易金额小于等于交易金额
                    if (authTransactionContextDTO.getFirstOriginalAuthFlow() != null && authTransactionContextDTO.getBaseAuthRequestVO()
                        .getTransAmount().compareTo(
                            authTransactionContextDTO.getFirstOriginalAuthFlow().getRemainingTransAmount()) > 0) {
                        log.warn("撤销/冲正交易金额超过原交易金额: {}",
                            authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount());
                        BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                        responseVO.setReturnCode(
                            KunLinkageAuthResponseCodeConstant.TRANSACTION_AMOUNT_EXCEEDS_LIMIT.getCode());
                        responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                        return responseVO;
                    }
                    break;
            }
            BaseAuthResponseVO responseVO = this.getOriginalAuthFlowExt(authTransactionContextDTO);
            if (responseVO != null) {
                return responseVO;
            }
        }
        return null;
    }

    /**
     * 获取原交易扩展信息
     * <p>
     * 查询原交易的扩展信息，并设置到交易上下文中。 如果原交易不存在，返回错误响应VO。
     *
     * @param authTransactionContextDTO 交易上下文
     * @return 错误响应VO，如果查询成功则返回null
     */
    @Nullable
    private BaseAuthResponseVO getOriginalAuthFlowExt(AuthTransactionContextDTO authTransactionContextDTO) {
        if (authTransactionContextDTO.getOriginalAuthFlow() != null) {
            // 查询原交易扩展信息
            AuthFlowExt originalAuthFlowExt = authFlowExtMapper.selectOne(
                new LambdaQueryWrapper<AuthFlowExt>().eq(AuthFlowExt::getAuthFlowId,
                    authTransactionContextDTO.getOriginalAuthFlow().getId()).between(AuthFlowExt::getCreateTime,
                    authTransactionContextDTO.getOriginalAuthFlow().getCreateTime(), DateTimeUtils.truncateToSecond(
                        DateUtils.addDays(authTransactionContextDTO.getOriginalAuthFlow().getCreateTime(), 1))));
            if (originalAuthFlowExt != null) {
                authTransactionContextDTO.setOriginalAuthFlowExt(originalAuthFlowExt);
            } else {
                log.warn("原交易扩展信息不存在: {}", authTransactionContextDTO.getOriginalAuthFlow().getId());
                BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                responseVO.setReturnCode(KunLinkageAuthResponseCodeConstant.ORIGINAL_TRANSACTION_NOT_FOUND.getCode());
                responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                return responseVO;
            }
            if (!authTransactionContextDTO.getOriginalAuthFlow().getId()
                .equals(authTransactionContextDTO.getFirstOriginalAuthFlow().getId())) {
                AuthFlowExt firstAuthFlowExt = authFlowExtMapper.selectOne(
                    new LambdaQueryWrapper<AuthFlowExt>().eq(AuthFlowExt::getAuthFlowId,
                        authTransactionContextDTO.getFirstOriginalAuthFlow().getId()).between(AuthFlowExt::getCreateTime,
                        authTransactionContextDTO.getFirstOriginalAuthFlow().getCreateTime(), DateTimeUtils.truncateToSecond(
                            DateUtils.addDays(authTransactionContextDTO.getFirstOriginalAuthFlow().getCreateTime(), 1))));
                if (firstAuthFlowExt != null) {
                    authTransactionContextDTO.setFirstOriginalAuthFlowExt(firstAuthFlowExt);
                } else {
                    log.warn("第一笔原交易扩展信息不存在: {}",
                        authTransactionContextDTO.getFirstOriginalAuthFlow().getId());
                    BaseAuthResponseVO responseVO = authTransactionContextDTO.getBaseAuthResponseVO();
                    responseVO.setReturnCode(
                        KunLinkageAuthResponseCodeConstant.ORIGINAL_TRANSACTION_NOT_FOUND.getCode());
                    responseVO.setErrorMessage(ResponseMessageUtil.getResponseMsg(responseVO.getReturnCode(), null, i18nMessageService));
                    return responseVO;
                }
            }
        }
        return null;
    }

    /**
     * 检查卡状态是否适用于当前交易类型
     *
     * @param cardInfo   卡信息
     * @param contextDTO 交易上下文
     * @return 卡状态是否有效
     */
    private boolean isCardStatusValidForTransaction(OrganizationCustomerCardInfo cardInfo,
        AuthTransactionContextDTO contextDTO) {
            if (CardActiveStatusEnum.ACTIVATED.getStatus().equals(cardInfo.getCardActiveStatus())) {
                // 根据交易类型进行额外的卡状态检查
                switch (contextDTO.getTransactionTypeEnum()) {
                    case AUTHORIZATION:
                    case PRE_AUTH:
                    case PRE_AUTH_COMPLETION:
                    case CASH_ADVANCE:
                    case TRANSFER_OUT:
                    case CARD_VERIFICATION:
                        // 这些交易类型需要卡处于正常状态
                        return CardStatusEnum.NORMAL.getStatus().equals(cardInfo.getCardStatus());
                    case REFUND:
                    case TRANSFER_IN:
                        // 退款和转入交易可以在卡状态为正常或冻结时进行
                        return !CardStatusEnum.CANCEL.getStatus().equals(cardInfo.getCardStatus());
                    case AUTHORIZATION_VOID:
                    case PRE_AUTH_VOID:
                    case PRE_AUTH_COMPLETION_VOID:
                    case CASH_ADVANCE_VOID:
                    case REFUND_VOID:
                    case TRANSFER_OUT_VOID:
                    case TRANSFER_IN_VOID:
                    case AUTHORIZATION_REVERSAL:
                    case PRE_AUTH_REVERSAL:
                    case PRE_AUTH_COMPLETION_REVERSAL:
                    case CASH_ADVANCE_REVERSAL:
                    case REFUND_REVERSAL:
                    case TRANSFER_IN_REVERSAL:
                    case TRANSFER_OUT_REVERSAL:
                    case AUTHORIZATION_VOID_REVERSAL:
                    case PRE_AUTH_VOID_REVERSAL:
                    case PRE_AUTH_COMPLETION_VOID_REVERSAL:
                    case CASH_ADVANCE_VOID_REVERSAL:
                    case REFUND_VOID_REVERSAL:
                    case TRANSFER_OUT_VOID_REVERSAL:
                    case TRANSFER_IN_VOID_REVERSAL:
                    case CARD_VERIFICATION_REVERSAL:
                        // 撤销/冲正交易不需要检查卡状态
                        return true;
                    default:
                        return false;
                }
            } else {
                return false;
            }
    }

    /**
     * 检查交易金额是否符合要求
     *
     * @param contextDTO 交易上下文
     * @return 交易金额是否有效
     */
    private boolean isTransactionAmountValid(AuthTransactionContextDTO contextDTO) {
        // 检查交易金额是否为正数
        if (contextDTO.getBaseAuthRequestVO().getTransAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 根据其它字段重新查询原交易
     * <p>
     * 用于根据交易关键字段（如商户号、处理方卡ID、处理方等）查询原交易列表。 主要用于冲正交易时，当原交易ID不存在时，通过其他字段匹配原交易。
     *
     * @param authTransactionContextDTO 交易上下文
     * @param days                      查询的天数范围，负数表示向前查询的天数
     * @return 原交易列表，按创建时间升序排序
     */
    private List<AuthFlow> getOriginalAuthFlowList(AuthTransactionContextDTO authTransactionContextDTO, int days) {
        // 根据其它字段重新查询原交易
        List<AuthFlow> originalList = authFlowMapper.selectList(
            new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getMerchantNo,
                    authTransactionContextDTO.getBaseAuthRequestVO().getMID())
                .eq(AuthFlow::getProcessorCardId, authTransactionContextDTO.getBaseAuthRequestVO().getProcessorCardId())
                .eq(AuthFlow::getProcessor, authTransactionContextDTO.getBaseAuthRequestVO().getProcessor())
                .eq(AuthFlow::getAcquireReferenceNo,
                    authTransactionContextDTO.getBaseAuthRequestVO().getAcquireReferenceNo())
                .eq(AuthFlow::getSystemsTraceAuditNumber,
                    authTransactionContextDTO.getBaseAuthRequestVO().getSystemsTraceAuditNumber())
                .eq(AuthFlow::getProcessorExt1, authTransactionContextDTO.getBaseAuthRequestVO().getProcessorExt1())
                .eq(AuthFlow::getCardAcceptorId, authTransactionContextDTO.getBaseAuthRequestVO().getCardAcceptorId())
                .eq(AuthFlow::getStatus, TransactionStatusEnum.SUCCESS.getCode())
                .gt(AuthFlow::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), days))
                .lt(AuthFlow::getCreateTime, DateTimeUtils.getCurrentDateTime()).orderByAsc(AuthFlow::getCreateTime));
        return originalList;
    }

    public void updateAuthFlowToFailAtFinal(AuthFlow authFlow) {
        AuthFlow updateAuthFlow = new AuthFlow();
        updateAuthFlow.setStatus(authFlow.getStatus());
        updateAuthFlow.setResponseCode(authFlow.getResponseCode());
        updateAuthFlow.setResponseMsg(authFlow.getResponseMsg());
        updateAuthFlow.setTransDoneTime(authFlow.getTransDoneTime());
        updateAuthFlow.setUpdateTime(DateTimeUtils.getCurrentDateTime());
        int update = authFlowMapper.update(updateAuthFlow,
            new LambdaQueryWrapper<AuthFlow>()
                .eq(AuthFlow::getId, authFlow.getId())
                .eq(AuthFlow::getCreateTime, authFlow.getCreateTime()));

        log.info("更新交易状态: {}, 影响行数: {}", JSON.toJSONString(updateAuthFlow), update);
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的键值
     * @return 获取到的锁对象，如果获取失败返回null
     */
    protected RLock acquireLock(String lockKey) {
        RLock lock = redissonLockUtil.getLock(lockKey);
        return (lock != null && lock.tryLock()) ? lock : null;
    }

    /**
     * 释放分布式锁
     *
     * @param lock 锁对象
     */
    protected void releaseLock(RLock lock) {
        if (lock != null && lock.isHeldByCurrentThread()) {
            redissonLockUtil.unlock(lock);
        }
    }

    public void sendTransactionNotification(AuthTransactionContextDTO context) {
        if (context.isThirdPartyAuthorizationFlag()){
            log.warn("第三方授权交易不发送通知: {}", context.getBaseAuthRequestVO().getRequestId());
            return;
        }
        if (context.getAuthFlow()== null){
            log.info("交易流程信息为空，不发送通知: {}", context.getBaseAuthRequestVO().getRequestId());
            return;
        }
        if (!TransactionStatusEnum.SUCCESS.getCode().equals(context.getAuthFlow().getStatus())){
            log.info("交易状态不是成功，不发送通知: {}, 状态: {}",
                context.getBaseAuthRequestVO().getRequestId(), context.getAuthFlow().getStatus());
            return;
        }
        
        try {
            // TODO: 添加判断条件
            AuthResultWebHookRequestVO webHookRequestVO = new AuthResultWebHookRequestVO();
            webHookRequestVO.setTransactionId(context.getTransactionId());
            
            if (context.getBaseAuthResponseVO() != null) {
                webHookRequestVO.setOrganizationNo(context.getBaseAuthRequestVO().getMID());
                webHookRequestVO.setCardId(context.getBaseAuthRequestVO().getGatewayCardId());
                webHookRequestVO.setTransactionCurrency(context.getBaseAuthRequestVO().getTransCurrency());
                webHookRequestVO.setTransactionAmount(context.getBaseAuthRequestVO().getTransAmount());
                webHookRequestVO.setCardholderCurrency(context.getBaseAuthRequestVO().getCardholderCurrency());
                webHookRequestVO.setCardholderAmount(context.getBaseAuthRequestVO().getCardholderAmount());
            }
            
            if (context.getOrganizationCustomerCardInfo() != null) {
                webHookRequestVO.setCustomerId(context.getOrganizationCustomerCardInfo().getCustomerId());
            }
            
            if (context.getTransactionTypeEnum() != null) {
                webHookRequestVO.setTransactionDirection(context.getTransactionTypeEnum().getDirection().getValue());
                webHookRequestVO.setTransactionType(context.getTransactionTypeEnum().getCode());
            }
            
            if (context.getAuthFlow()!= null){
                webHookRequestVO.setServicePointCardCode(context.getAuthFlow().getPosEntryMode());
                webHookRequestVO.setServicePointPinCode(context.getAuthFlow().getPointPinCode());
                webHookRequestVO.setServicePointConditionCode(context.getAuthFlow().getPosConditionCode());
                webHookRequestVO.setTransmissionDateTime(DateUtils.formatDate(
                    context.getAuthFlow().getCreateTime(), "yyyyMMddHHmmss"));
                webHookRequestVO.setReferenceNumber(context.getAuthFlow().getAcquireReferenceNo());
                webHookRequestVO.setApprovalCode(context.getAuthFlow().getApproveCode());
                webHookRequestVO.setAuthStatus(context.getAuthFlow().getStatus());
                webHookRequestVO.setCardAcceptorNameLocation(context.getAuthFlow().getMerchantName()+">"+context.getAuthFlow().getCardAcceptorCity()+context.getAuthFlow().getCardAcceptorCountryCode());
                webHookRequestVO.setMerchantType(context.getAuthFlow().getMcc());
                webHookRequestVO.setCardAcceptorTerminalCode(context.getAuthFlow().getCardAcceptorTid());
                webHookRequestVO.setCardAcceptorIdentification(context.getAuthFlow().getCardAcceptorTid());
                webHookRequestVO.setOriginalTransactionId(context.getAuthFlow().getOriginalId());
                webHookRequestVO.setOriginalTransmissionDateTime(context.getAuthFlow().getOriginalTransTime());
            }
            
            // TODO: 填什么字段
            webHookRequestVO.setRequestCurrency(null);
            webHookRequestVO.setRequestAmount(null);
            webHookRequestVO.setNetworkReferenceId(null);
            
            // TODO 补充url取值
            externalWebhookService.callApiByOrganizationAsyncWithRetry("", webHookRequestVO, null, Void.class)
                .exceptionally(throwable -> {
                    log.error("异步发送交易通知失败: transactionId={}, 错误={}", 
                        context.getTransactionId(), throwable.getMessage(), throwable);
                    return new WebHookResult<Void>(webHookRequestVO.getRequestNo(), 
                        KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode(), 
                        "Async notification failed: " + throwable.getMessage());
                });
                
        } catch (Exception e) {
            log.error("构建交易通知请求失败: transactionId={}, 错误={}", 
                context.getTransactionId(), e.getMessage(), e);
        }
    }
}
