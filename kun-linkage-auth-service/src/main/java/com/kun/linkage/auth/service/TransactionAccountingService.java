package com.kun.linkage.auth.service;

import brave.Tracer;
import com.kun.common.util.mq.RocketMqService;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.auth.facade.vo.mq.OrganizationTransAccountingReversalVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.OrganizationTransAccounting;
import com.kun.linkage.common.db.mapper.OrganizationTransAccountingMapper;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.enums.OrganizationPoolCurrencyCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
public class TransactionAccountingService {

    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;

    @Resource
    private OrganizationTransAccountingMapper organizationTransAccountingMapper;

    @Resource
    private KunDigitalCurrencyExchangeService kunDigitalCurrencyExchangeService;

    @Resource
    protected RocketMqService rocketMqService;

    @Resource
    private Tracer tracer;

    /**
     * 交易记账
     *
     * @return 交易结果
     */
    public BaseAuthResponseVO transactionAccounting(AuthTransactionContextDTO authTransactionContextDTO) {
        OrganizationTransAccounting organizationTransAccounting =
            this.initializeOrganizationTransAccounting(authTransactionContextDTO);
        authTransactionContextDTO.setOrganizationTransAccounting(organizationTransAccounting);
        if (DigitalCurrencyEnum.contains(authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode())) {
            // 扣除币种是数币
            log.info("交易记商户数币账开始,扣除币种:{}",
                authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
            return handleDigitalCurrencyTransaction(authTransactionContextDTO);
        } else {
            log.info("交易记商户法币账开始,扣除币种:{}",
                authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
            // 扣除币种是法币
            return handleFiatCurrencyTransaction(authTransactionContextDTO);
        }
    }

    private OrganizationTransAccounting initializeOrganizationTransAccounting(
        AuthTransactionContextDTO authTransactionContextDTO) {
        OrganizationTransAccounting organizationTransAccounting = new OrganizationTransAccounting();
        organizationTransAccounting.setId(String.valueOf(uidGenerator.getUID()));
        organizationTransAccounting.setOrganizationNo(
            authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationNo());
        organizationTransAccounting.setCustomerId(
            authTransactionContextDTO.getOrganizationCustomerCardInfo().getCustomerId());
        organizationTransAccounting.setRequestNo(String.valueOf(uidGenerator.getUID()));
        organizationTransAccounting.setCardId(authTransactionContextDTO.getOrganizationCustomerCardInfo().getCardId());
        organizationTransAccounting.setTransTime(authTransactionContextDTO.getAuthFlow().getCreateTime());
        organizationTransAccounting.setTransAmount(
            authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount());
        organizationTransAccounting.setTransCurrencyCode(
            authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency());
        organizationTransAccounting.setTransCurrencyPrecision(
            authTransactionContextDTO.getAuthFlow().getCardholderCurrencyExponent());
        organizationTransAccounting.setTransId(authTransactionContextDTO.getTransactionId());
        // 业务方填写
        organizationTransAccounting.setFxRate(null);
        organizationTransAccounting.setDeductCurrencyCode(
            authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
        // 业务方填写
        organizationTransAccounting.setDeductProcessor(null);
        // TODO 从币种表取精度
        organizationTransAccounting.setDeductCurrencyPrecision(
            OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(organizationTransAccounting.getDeductCurrencyCode()));
        // 业务方填写
        organizationTransAccounting.setDeductAmount(null);
        // 业务方填写
        organizationTransAccounting.setDeductTotalAmount(null);
        organizationTransAccounting.setBookkeepStatus(OperationStatusEnum.PENDING.getStatus());
        organizationTransAccounting.setBookkeepReversalCount(0);
        organizationTransAccounting.setCreateTime(DateTimeUtils.getCurrentDateTime());
        organizationTransAccounting.setLastModifyTime(organizationTransAccounting.getCreateTime());
        return organizationTransAccounting;
    }

    private BaseAuthResponseVO handleDigitalCurrencyTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        authTransactionContextDTO.getOrganizationTransAccounting()
            .setDeductProcessor(DeductProcessorEnum.KUN.getValue());
        try {
            // 换汇并记账
            this.processingDigitalCurrencyExchange(authTransactionContextDTO);
            return null;
        } catch (BusinessException e) {
            log.error("处理卡交易商户数币记账法币记账异常", e);
            // 设置错误信息
            authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(e.getErrorCode());
            return authTransactionContextDTO.getBaseAuthResponseVO();
        } catch (Exception e) {
            log.error("处理卡交易商户数币记账法币记账异常", e);
            authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
            return authTransactionContextDTO.getBaseAuthResponseVO();
        }
    }

    private void processingDigitalCurrencyExchange(AuthTransactionContextDTO authTransactionContextDTO) {
        log.info("处理卡交易商户数币记账法币转数币汇率计算,交易ID:{}", authTransactionContextDTO.getTransactionId());
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationTransAccounting organizationTransAccounting =
            authTransactionContextDTO.getOrganizationTransAccounting();
        if (!(StringUtils.equals(organizationTransAccounting.getTransCurrencyCode(),
            FiatCurrencyEnum.USD.getCurrencyCode()) && StringUtils.equals(
            organizationTransAccounting.getDeductCurrencyCode(), DigitalCurrencyEnum.USDT.getValue()))) {
            // 注意注意!!!!  kun的接口币对必须是数币在前、法币在后,返回的也是数币兑法币的汇率
            fxRate = kunDigitalCurrencyExchangeService.askPrice(authTransactionContextDTO.getOrganizationBasicInfo(),
                organizationTransAccounting.getTransAmount(), KunSideTypeEnum.BUY,
                organizationTransAccounting.getDeductCurrencyCode(),
                organizationTransAccounting.getTransCurrencyCode());
        }
        organizationTransAccounting.setFxRate(fxRate);
        log.info("处理卡交易商户数币记账法币转数币汇率计算结束,交易ID:{}, 汇率:{}",
            authTransactionContextDTO.getTransactionId(), organizationTransAccounting.getFxRate());
        organizationTransAccounting.setDeductAmount(organizationTransAccounting.getTransAmount().multiply(fxRate)
            .setScale(organizationTransAccounting.getDeductCurrencyPrecision(), RoundingMode.UP));
        organizationTransAccounting.setDeductTotalAmount(organizationTransAccounting.getDeductAmount());
        log.info("处理卡交易商户数币记账法币转数币汇率计算结束,交易ID:{}, 扣除金额:{}",
            authTransactionContextDTO.getTransactionId(), organizationTransAccounting.getDeductAmount());
        // 插入记账记录
        this.insertOrganizationTransAccounting(organizationTransAccounting);
        // 调用账户动账
        try {
            Result<KunDebitSubRsp> kunDebitSubRspResult =
                kunDigitalCurrencyExchangeService.kunDebitSub(authTransactionContextDTO.getOrganizationBasicInfo(),
                    organizationTransAccounting.getRequestNo(), organizationTransAccounting.getDeductCurrencyCode(),
                    organizationTransAccounting.getDeductTotalAmount(),
                    KunAndPayXRemarkEnum.TRANSACTION_BPC.getRemark());
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (kunDebitSubRspResult != null && StringUtils.equals(kunDebitSubRspResult.getCode(),
                String.valueOf(HttpStatus.SC_OK)) && kunDebitSubRspResult.getData() != null) {
                if (StringUtils.equals(kunDebitSubRspResult.getData().getStatus(),
                    OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("处理卡交易商户数币记账扣款成功,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                        authTransactionContextDTO.getTransactionId(),
                        organizationTransAccounting.getDeductCurrencyCode(),
                        organizationTransAccounting.getDeductAmount());
                    organizationTransAccounting.setBookkeepStatus(OperationStatusEnum.SUCCESS.getStatus());
                } else if (StringUtils.equals(kunDebitSubRspResult.getData().getStatus(),
                    OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败不需要冲账
                    log.info("处理卡交易商户数币记账扣款失败,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                        authTransactionContextDTO.getTransactionId(),
                        organizationTransAccounting.getDeductCurrencyCode(),
                        organizationTransAccounting.getDeductAmount());
                    organizationTransAccounting.setBookkeepStatus(OperationStatusEnum.FAIL.getStatus());
                    throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
                } else {
                    log.info("处理卡交易商户数币记账扣款状态未知,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                        authTransactionContextDTO.getTransactionId(),
                        organizationTransAccounting.getDeductCurrencyCode(),
                        organizationTransAccounting.getDeductAmount());
                    organizationTransAccounting.setBookkeepStatus(OperationStatusEnum.FAIL.getStatus());
                    throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
                }
            } else {
                log.info("处理卡交易商户数币记账扣款异常,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), organizationTransAccounting.getDeductCurrencyCode(),
                    organizationTransAccounting.getDeductAmount());
                organizationTransAccounting.setBookkeepStatus(OperationStatusEnum.FAIL.getStatus());
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
            }
        } catch (Exception e) {
            log.error("处理卡交易商户数币记账扣款异常,发送冲账事件到mq", e);
            this.sendOrganizationTransAccountingReversalToMq(organizationTransAccounting,
                authTransactionContextDTO.getOrganizationBasicInfo().getMpcToken(),
                authTransactionContextDTO.getOrganizationBasicInfo().getMpcGroupCode());
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
        }
    }

    private void sendOrganizationTransAccountingReversalToMq(OrganizationTransAccounting organizationTransAccounting,
        String mpcToken, String mpcGroupCode) {
        OrganizationTransAccountingReversalVO organizationTransAccountingReversalVO =
            new OrganizationTransAccountingReversalVO();
        organizationTransAccountingReversalVO.setOrganizationTransAccountingId(organizationTransAccounting.getId());
        organizationTransAccountingReversalVO.setCreateTime(organizationTransAccounting.getCreateTime());
        organizationTransAccountingReversalVO.setMpcToken(mpcToken);
        organizationTransAccountingReversalVO.setMpcGroupCode(mpcGroupCode);
        organizationTransAccountingReversalVO.getLogContext().setTraceId(tracer.currentSpan().context().traceIdString());
        organizationTransAccountingReversalVO.getLogContext().setSpanId(tracer.currentSpan().context().spanIdString());
        log.info("发送机构记账冲正通知事件到mq, 组织号: {}, 交易ID: {}, 记账ID: {}",
            organizationTransAccounting.getOrganizationNo(), organizationTransAccounting.getTransId(),
            organizationTransAccounting.getId());
        // 发送MQ消息
        rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_TRANS_ACCOUNTING_REVERSAL_TOPIC,
            organizationTransAccountingReversalVO, 2000, MqTopicConstant.RETRY_DELAYS[organizationTransAccounting.getBookkeepReversalCount()]);
        log.info("发送机构记账冲正通知事件到mq成功, 组织号: {}, 交易ID: {}, 记账ID: {}",
            organizationTransAccounting.getOrganizationNo(), organizationTransAccounting.getTransId(),
            organizationTransAccounting.getId());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertOrganizationTransAccounting(OrganizationTransAccounting organizationTransAccounting) {
        int insert = organizationTransAccountingMapper.insert(organizationTransAccounting);
        if (insert <= 0) {
            log.error("插入记账记录失败, 组织号: {}, 交易ID: {}", organizationTransAccounting.getOrganizationNo(),
                organizationTransAccounting.getTransId());
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
        }
    }

    private BaseAuthResponseVO handleFiatCurrencyTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        // 处理法币交易逻辑
        return null;
    }
}
