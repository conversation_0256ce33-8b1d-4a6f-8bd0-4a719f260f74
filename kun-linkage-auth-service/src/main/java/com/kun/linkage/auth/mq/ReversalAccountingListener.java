package com.kun.linkage.auth.mq;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.service.TransactionService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.auth.utils.AuthLarkAlarmUtil;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.common.util.mq.RocketMqService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 记账冲正消息监听器
 * <p>
 * 负责处理记账超时后的冲正操作，主要功能：
 * 1. 接收冲正消息
 * 2. 解析交易上下文
 * 3. 执行冲正操作
 * 4. 记录处理结果
 * 5. 失败重试机制（最多重试5次，间隔分别为10秒、30秒、1分钟、5分钟、30分钟）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RocketMQMessageListener(
    topic = MqTopicConstant.AUTH_ACCOUNTING_REVERSAL_TOPIC,
    consumerGroup = MqConsumerGroupConstant.KL_AUTH_ACCOUNTING_REVERSAL_GROUP,
    messageModel = MessageModel.CLUSTERING
)
public class ReversalAccountingListener implements RocketMQListener<AuthTransactionContextDTO> {

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private RocketMqService rocketMqService;

    @Autowired
    private AuthLarkAlarmUtil authLarkAlarmUtil;
    @Resource
    private Tracer tracer;

    // 重试次数和延迟时间配置
    private static final int[] RETRY_DELAYS = {
        MqTopicConstant.DELAY_LEVEL_1S,    // 1秒
        MqTopicConstant.DELAY_LEVEL_10S,    // 10秒
        MqTopicConstant.DELAY_LEVEL_30S,    // 30秒
        MqTopicConstant.DELAY_LEVEL_1M,    // 1分钟
        MqTopicConstant.DELAY_LEVEL_5M, // 5分钟
        MqTopicConstant.DELAY_LEVEL_30M // 30分钟
    };
    private static final int MAX_RETRY_TIMES = RETRY_DELAYS.length;

    @NewSpan
    @Override
    public void onMessage(AuthTransactionContextDTO authTransactionContextDTO) {
        try {
            // 设置日志追踪上下文
            if (tracer.currentSpan() != null && authTransactionContextDTO.getLogContext() != null) {
                tracer.currentSpan().tag("traceId", authTransactionContextDTO.getLogContext().getTraceId());
            }
            log.info("收到冲正消息: {}", JSON.toJSONString(authTransactionContextDTO));
            
            // 获取当前重试次数
            int retryTimes = getRetryTimes(authTransactionContextDTO);
            
            // 执行冲正操作
            Result<Void> result = transactionService.reverseTransactionAccounting(authTransactionContextDTO);
            
            if (!result.isSuccess()) {
                log.error("冲正操作失败, transactionId: {}, error: {}, 当前重试次数: {}", 
                    authTransactionContextDTO.getTransactionId(), 
                    result.getMessage(),
                    retryTimes
                );
                
                // 判断是否需要重试
                if (retryTimes < MAX_RETRY_TIMES) {
                    // 更新重试次数
                    updateRetryTimes(authTransactionContextDTO, retryTimes + 1);
                    
                    // 计算下次重试的延迟时间
                    int delayLevel = RETRY_DELAYS[retryTimes];
                    
                    // 发送延迟消息
                    rocketMqService.delayedSend(
                        MqTopicConstant.AUTH_ACCOUNTING_REVERSAL_TOPIC,
                        authTransactionContextDTO,
                        10,
                        delayLevel
                    );
                    
                    log.info("冲正操作失败，将在{}毫秒后进行第{}次重试, transactionId: {}", 
                        delayLevel,
                        retryTimes + 1,
                        authTransactionContextDTO.getTransactionId()
                    );
                } else {
                    log.error("冲正操作失败，已达到最大重试次数{}, transactionId: {}",
                        MAX_RETRY_TIMES,
                        authTransactionContextDTO.getTransactionId()
                    );
                    // 发送Lark告警通知
                    authLarkAlarmUtil.sendAccountingReversalAlarm(authTransactionContextDTO,
                        result.getMessage(), "冲正失败", MAX_RETRY_TIMES);
                    // TODO: 可以考虑将失败的消息发送到死信队列，等待人工处理
                }
            } else {
                log.info("冲正操作成功, transactionId: {}", 
                    authTransactionContextDTO.getTransactionId()
                );
            }
        } catch (BusinessException e) {
            log.error("处理冲正消息时发生业务异常: {}", e.getMessage(), e);
            handleRetry(authTransactionContextDTO, e);
        } catch (Exception e) {
            log.error("处理冲正消息时发生系统异常: {}", e.getMessage(), e);
            handleRetry(authTransactionContextDTO, e);
        }
    }

    /**
     * 获取当前重试次数
     */
    private int getRetryTimes(AuthTransactionContextDTO authTransactionContextDTO) {
        // 从上下文中获取重试次数，如果没有则默认为0
        return authTransactionContextDTO.getRetryTimes() != null ? 
            authTransactionContextDTO.getRetryTimes() : 0;
    }

    /**
     * 更新重试次数
     */
    private void updateRetryTimes(AuthTransactionContextDTO authTransactionContextDTO, int retryTimes) {
        authTransactionContextDTO.setRetryTimes(retryTimes);
    }

    /**
     * 处理重试逻辑
     */
    private void handleRetry(AuthTransactionContextDTO authTransactionContextDTO, Exception e) {
        int retryTimes = getRetryTimes(authTransactionContextDTO);
        
        if (retryTimes < MAX_RETRY_TIMES) {
            // 更新重试次数
            updateRetryTimes(authTransactionContextDTO, retryTimes + 1);
            
            // 计算下次重试的延迟时间
            long delayTime = RETRY_DELAYS[retryTimes];
            
            // 发送延迟消息
            rocketMqService.delayedSend(
                MqTopicConstant.AUTH_ACCOUNTING_REVERSAL_TOPIC,
                authTransactionContextDTO,
                delayTime,
                retryTimes + 1
            );
            
            log.info("发生异常，将在{}毫秒后进行第{}次重试, transactionId: {}, error: {}", 
                delayTime, 
                retryTimes + 1,
                authTransactionContextDTO.getTransactionId(),
                e.getMessage()
            );
        } else {
            log.error("发生异常，已达到最大重试次数{}, transactionId: {}, error: {}",
                MAX_RETRY_TIMES,
                authTransactionContextDTO.getTransactionId(),
                e.getMessage()
            );
            // 发送Lark告警通知
            authLarkAlarmUtil.sendAccountingReversalAlarm(authTransactionContextDTO,
                e.getMessage(), "系统异常", MAX_RETRY_TIMES);

        }
    }


}