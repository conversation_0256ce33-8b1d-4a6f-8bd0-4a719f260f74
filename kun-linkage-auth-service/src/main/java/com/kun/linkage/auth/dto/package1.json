{"acquireReferenceNo": "************", "cardAcceptorId": "***************", "cardAcceptorTid": "RECTRS01", "cardholderAmount": 35.0, "cardholderCurrency": "USD", "cardholderCurrencyExponent": 2, "cardholderMarkupAmount": 35.0, "conversionRateCardholderBilling": 0.1, "data": {"requestId": "S20250731000000044", "transType": 1, "authorizationType": 11, "accountingDirection": 1, "referenceNo": "************", "transId": "S20250731000000044", "originalTransId": "", "cardId": "870357958VC00000021", "channelData": {"accountIdentification": "T*********355", "acquiringInstitutionIdentificationCode": "476119", "additionalAmounts": {"acquirerFeeAmount": "D************", "issuerFeeAmountAndCurrency": {"feeAmoun": "************", "feeCurrency": "840", "feeSign": "C"}, "transactionAmountAccountCurrency": "***********"}, "additionalData": {"acquierInstitueIdentifier": "9002", "acquirerCountryCode": "344", "acquirerNetworkIdentifier": "0999", "cardId": "************", "cardType": "00", "customerId": "*********", "customerMobilePhone": "***********", "feTraceNumber": "************", "feTransactionDateAndTime": "**************", "feTransactionNumber": "************", "issuerNetworkIdentifier": "0002", "localTransactionDateAndTime": "************", "networkReferenceData": "***************", "posEnvironmen": "R", "senderReferenceNumber": "************", "settlementType": "2", "svfeTransactionType": "680", "transactionRiskScore": "09"}, "amountAccount": "************", "amountCardholderBilling": "************", "amountTransaction": "************", "cardAcceptorIdentificationCode": "***************", "cardAcceptorNameAndLocation": {"countryCode": "HK", "merchantNumberOfBankName": "ACQUIRER NAME>HongKong               "}, "cardAcceptorTerminalIdentification": "RECTRS01", "conversionRateAccount": "********", "conversionRateCardholderBilling": "********", "currencyCodeAccount": "840", "currencyCodeCardholderBilling": "840", "currencyCodeTransaction": "840", "dateExpiration": "2806", "dateTimeLocalTransaction": "************", "merchantType": "5411", "messageType": "1100", "pointOfServiceDateCode": {"cardCaptureCapabilit": "0", "cardDataInputCapability": "0", "cardDataInputMode": "V", "cardDataOutputCapability": "0", "cardPresence": "0", "cardholderAuthenticationCapability": "0", "cardholderAuthenticationEntity": "5", "cardholderAuthenticationMethod": "0", "cardholderPresenceIndicator": "5", "operatingEnvironmen": "1", "pinCaptureCapability": "0", "terminalOutputCapability": "0"}, "processingCode": "000000", "retrievalReferenceNumber": "************", "settlementDate": "250731", "svfeIssuerInstitutionIdentifier": "7091", "systemsTraceAuditNumber": "731044", "transmissionDateAndTime": "**********"}}, "errorCode": "0000", "errorMessage": "Success", "gatewayCardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "issuerCardId": "************", "mID": "********", "markupAmount": 0.0, "markupRate": 0, "mcc": "5411", "originalTransId": "", "processor": "BPC-GW", "processorCardId": "870357958VC00000021", "processorExt1": "************", "requestId": "SG20250731000000044", "status": "SUCC", "systemsTraceAuditNumber": "731044", "transAmount": 35.0, "transCurrency": "USD", "transCurrencyExponent": 2, "transId": "SG20250731000000044", "transType": "010000"}