package com.kun.linkage.auth.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.auth.facade.api.bean.req.PageQueryTransactionDetailReq;
import com.kun.linkage.auth.facade.api.bean.res.PageQueryTransactionDetailRes;
import com.kun.linkage.auth.service.TransactionDetailBizService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 交易记录相关接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Tag(name = "交易记录相关接口", description = "交易记录相关接口")
@RestController
@RequestMapping("/api/transactionDetail")
public class TransactionDetailController {
    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));
    @Resource
    private TransactionDetailBizService transactionDetailBizService;
    /**
     * 分页查询交易记录
     * @param pageQueryTransactionDetailReq
     * @return
     */
    @Operation(description = "分页查询交易记录", summary = "分页查询交易记录")
    @RequestMapping(value = "/pageQueryTransactionDetail", method = RequestMethod.POST)
    public Result<PageResult<PageQueryTransactionDetailRes>> pageQueryTransactionDetail(
            @RequestBody @Validated PageQueryTransactionDetailReq pageQueryTransactionDetailReq){
        log.info("[分页查询交易记录]入参:{}", JSON.toJSONString(pageQueryTransactionDetailReq));
        return Result.success(transactionDetailBizService.pageQueryTransactionDetail(pageQueryTransactionDetailReq));
    }
}
