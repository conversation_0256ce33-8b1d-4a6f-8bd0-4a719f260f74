package com.kun.linkage.auth.controller;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.kun.linkage.auth.constant.ProcessorTransactionServiceEnum;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.constant.TransactionStatusEnum;
import com.kun.linkage.auth.facade.vo.BaseAuthRequestVO;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.auth.mock.AuthorizationMockExtend;
import com.kun.linkage.auth.service.AbstractTransactionService;
import com.kun.linkage.auth.service.TransactionService;
import com.kun.linkage.auth.utils.CacheKeyUtil;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.auth.utils.ResponseMessageUtil;
import com.kun.linkage.auth.utils.TransactionValidationUtil;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.base.utils.SensitiveDataStringBuilder;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 授权交易控制器
 * <p>
 * 提供授权交易相关接口，包括交易发起、参数校验、错误响应等。
 * 主要功能：
 * 1. 处理授权交易请求
 * 2. 参数校验和验证
 * 3. 分布式锁控制
 * 4. 业务处理流程控制
 * 5. 错误处理和响应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/transaction")
public class TransactionController {

    private static final Logger log = LoggerFactory.getLogger(TransactionController.class);
    
    /**
     * 必需的基础参数字段列表
     */
    private static final List<String> REQUIRED_BASIC_FIELDS = Arrays.asList(
        "requestId", "transId", "processor", "MID", "processorCardId",
        "transCurrency", "cardholderCurrency"
    );
    
    /**
     * 必需的系统追踪号字段列表
     */
    private static final List<String> REQUIRED_TRACE_FIELDS = Arrays.asList(
        "systemsTraceAuditNumber", "acquireReferenceNo"
    );
    
    @Resource
    private AbstractTransactionService abstractTransactionService;

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private RedissonLockUtil redissonLockUtil;

    @Resource
    private AuthorizationMockExtend authorizationMockExtend;

    /**
     * 授权交易接口
     * <p>
     * 处理前端发起的授权交易请求，包含参数校验、分布式锁、业务处理、异常处理等完整流程。
     * 处理流程：
     * 1. 参数校验
     * 2. 获取分布式锁
     * 3. 保存请求日志
     * 4. 数据库参数校验
     * 5. 获取并执行对应的业务处理器
     * 6. 返回处理结果
     *
     * @param authRequestVO 授权交易请求参数
     * @return 交易响应结果
     */
    @PostMapping("auth")
    public BaseAuthResponseVO authTransaction(@RequestBody BaseAuthRequestVO authRequestVO) {
        log.info("开始处理授权交易请求，请求参数: {}", SensitiveDataStringBuilder.parseValues(authRequestVO));
        
        // 初始化交易上下文
        AuthTransactionContextDTO context = initializeTransactionContext(authRequestVO);
        
        // 参数校验
        if (validateRequestParameter(context) != null) {
            return context.getBaseAuthResponseVO();
        }

        // 获取分布式锁并处理交易
        return processTransactionWithLock(context);
    }

    /**
     * 使用分布式锁处理交易
     * 
     * @param context 交易上下文
     * @return 交易响应结果
     */
    private BaseAuthResponseVO processTransactionWithLock(AuthTransactionContextDTO context) {
        BaseAuthRequestVO request = context.getBaseAuthRequestVO();
        String lockKey = CacheKeyUtil.getAuthTransactionKey(
            request.getMID(), request.getProcessor(), request.getTransId());
        RLock lock = null;
        
        try {
            lock = acquireLock(lockKey);
            if (lock == null) {
                return handleLockAcquisitionFailure(context);
            }

            // 处理交易流程
            BaseAuthResponseVO response = processTransaction(context);
            
            // 执行模拟扩展
            authorizationMockExtend.runningMock(context);

            // 设置响应消息
            response.setErrorMessage(ResponseMessageUtil.getResponseMsg(
                response.getReturnCode(), response.getErrorMessage(), i18nMessageService));
            return response;

        } catch (BusinessException e) {
            log.info("业务处理异常: {}", e.getMessage(), e);
            updateTransactionAsFail(context);
            return handleBusinessException(context, e);
        } catch (Exception e) {
            log.info("系统处理异常: {}", e.getMessage(), e);
            updateTransactionAsFail(context);
            return handleSystemException(context);
        } finally {
            // 发送交易通知
            abstractTransactionService.sendTransactionNotification(context);
            releaseLock(lock);
            log.info("交易处理完成，响应数据: {}", JSON.toJSONString(context.getBaseAuthResponseVO()));
        }
    }

    /**
     * 初始化交易上下文
     * 
     * @param authRequestVO 授权请求参数
     * @return 初始化的交易上下文
     */
    private AuthTransactionContextDTO initializeTransactionContext(BaseAuthRequestVO authRequestVO) {
        AuthTransactionContextDTO context = new AuthTransactionContextDTO();
        context.setBaseAuthRequestVO(authRequestVO);
        
        BaseAuthResponseVO responseVO = new BaseAuthResponseVO();
        responseVO.setRequestId(authRequestVO.getRequestId());
        context.setBaseAuthResponseVO(responseVO);
        
        return context;
    }

    /**
     * 获取分布式锁
     * 
     * @param lockKey 锁的键值
     * @return 获取到的锁对象，如果获取失败返回null
     */
    private RLock acquireLock(String lockKey) {
        RLock lock = redissonLockUtil.getLock(lockKey);
        return (lock != null && lock.tryLock()) ? lock : null;
    }

    /**
     * 处理获取锁失败的情况
     * 
     * @param context 交易上下文
     * @return 错误响应
     */
    private BaseAuthResponseVO handleLockAcquisitionFailure(AuthTransactionContextDTO context) {
        log.error("获取分布式锁失败");
        responseError(context, KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR);
        return context.getBaseAuthResponseVO();
    }

    /**
     * 处理交易流程
     * 
     * @param context 交易上下文
     * @return 交易响应结果
     */
    private BaseAuthResponseVO processTransaction(AuthTransactionContextDTO context) {
        // 保存请求日志
        abstractTransactionService.saveRequestLog(context);
        
        // 数据库参数校验
        if (abstractTransactionService.verifyParameterFromDB(context) != null) {
            return context.getBaseAuthResponseVO();
        }

        // 获取并执行业务处理器
        TransactionService transactionService = getTransactionService(context);
        if (transactionService == null) {
            return context.getBaseAuthResponseVO();
        }

        // 执行业务处理
        if (transactionService.processTransaction(context) != null) {
            updateTransactionAsFail(context);
            return context.getBaseAuthResponseVO();
        }

        // 设置成功响应
        setSuccessResponse(context);
        return context.getBaseAuthResponseVO();
    }

    /**
     * 更新交易为失败状态
     * 
     * @param context 交易上下文
     */
    private void updateTransactionAsFail(AuthTransactionContextDTO context) {
        try {
            if (context.getAuthFlow() != null) {
                String currentStatus = context.getAuthFlow().getStatus();
                String successCode = TransactionStatusEnum.SUCCESS.getCode();
                String failedCode = TransactionStatusEnum.FAILED.getCode();
                if (StringUtils.isBlank(context.getBaseAuthResponseVO().getReturnCode())){
                    context.getBaseAuthResponseVO().setReturnCode(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
                }
                
                // 如果交易状态不是成功或失败，则更新为失败状态
                if (!successCode.equals(currentStatus) && !failedCode.equals(currentStatus)) {
                    
                    context.getAuthFlow().setStatus(failedCode);
                    context.getAuthFlow().setResponseCode(context.getBaseAuthResponseVO().getReturnCode());
                    context.getAuthFlow().setResponseMsg(ResponseMessageUtil.getResponseMsg(
                        context.getBaseAuthResponseVO().getReturnCode(), 
                        context.getBaseAuthResponseVO().getErrorMessage(), 
                        i18nMessageService));
                    context.getAuthFlow().setTransDoneTime(DateTimeUtils.getCurrentDateTime());
                    
                    abstractTransactionService.updateAuthFlowToFailAtFinal(context.getAuthFlow());
                }
            }
        } catch (Exception e) {
            log.error("更新交易失败状态异常, 交易ID: {}", 
                context.getAuthFlow() != null ? context.getAuthFlow().getId() : "null", e.getMessage(), e);
        }
    }

    /**
     * 获取交易服务处理器
     * 
     * @param context 交易上下文
     * @return 交易服务处理器，如果未找到返回null
     */
    private TransactionService getTransactionService(AuthTransactionContextDTO context) {
        String processor = context.getBaseAuthRequestVO().getProcessor();
        Class<?> processorClass = ProcessorTransactionServiceEnum.getByProcessor(processor);
        
        if (processorClass == null) {
            log.error("未找到对应的处理器: {}", processor);
            responseError(context, KunLinkageAuthResponseCodeConstant.PROCESSOR_NOT_SUPPORTED, processor);
            return null;
        }

        TransactionService service = (TransactionService) SpringUtil.getBean(processorClass);
        if (service == null) {
            log.error("未找到处理器对应的Bean: {}", processor);
            responseError(context, KunLinkageAuthResponseCodeConstant.PROCESSOR_NOT_SUPPORTED, processor);
            return null;
        }

        return service;
    }

    /**
     * 设置成功响应
     * 
     * @param context 交易上下文
     */
    private void setSuccessResponse(AuthTransactionContextDTO context) {
        String approveCode = context.getAuthFlow() != null ? context.getAuthFlow().getApproveCode() : null;
        ResponseMessageUtil.setSuccessResponse(
            context.getBaseAuthResponseVO(), 
            context.getBaseAuthRequestVO().getRequestId(), 
            approveCode, i18nMessageService);
    }

    /**
     * 处理业务异常
     * 
     * @param context 交易上下文
     * @param e 业务异常
     * @return 错误响应
     */
    private BaseAuthResponseVO handleBusinessException(AuthTransactionContextDTO context, BusinessException e) {
        BaseAuthResponseVO response = context.getBaseAuthResponseVO();
        response.setReturnCode(e.getErrorCode());
        response.setErrorMessage(ResponseMessageUtil.getResponseMsg(e.getErrorCode(), e.getMessage(), i18nMessageService));
        response.setRequestId(context.getBaseAuthRequestVO().getRequestId());
        return response;
    }

    /**
     * 处理系统异常
     * 
     * @param context 交易上下文
     * @return 错误响应
     */
    private BaseAuthResponseVO handleSystemException(AuthTransactionContextDTO context) {
        responseError(context, KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR);
        return context.getBaseAuthResponseVO();
    }

    /**
     * 释放分布式锁
     * 
     * @param lock 锁对象
     */
    private void releaseLock(RLock lock) {
        if (lock != null && lock.isHeldByCurrentThread()) {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 参数校验
     * <p>
     * 校验请求参数的完整性和有效性，若有缺失则直接返回错误响应。
     * 校验项包括：
     * 1. 基础参数（requestId, transId等）
     * 2. 交易类型参数
     * 3. 金额相关参数
     * 4. 系统追踪号
     *
     * @param context 交易上下文
     * @return 错误响应VO，若校验通过返回null
     */
    private BaseAuthResponseVO validateRequestParameter(AuthTransactionContextDTO context) {
        BaseAuthRequestVO request = context.getBaseAuthRequestVO();
        
        // 使用工具类进行参数校验
        return TransactionValidationUtil.validateAllParameters(context, request, 
            field -> {
                responseError(context, KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING, field);
                return null;
            });
    }

    /**
     * 响应错误信息
     * <p>
     * 设置响应VO中的错误码、错误信息和请求ID。
     *
     * @param context 交易上下文
     * @param errorCode 错误码常量
     * @param errorParams 错误参数（用于国际化消息）
     */
    private void responseError(AuthTransactionContextDTO context,
        KunLinkageAuthResponseCodeConstant errorCode, String... errorParams) {
        ResponseMessageUtil.setErrorResponse(
            context.getBaseAuthResponseVO(),
            errorCode.getCode(),
            context.getBaseAuthRequestVO().getRequestId(),
            i18nMessageService,
            errorParams
        );
    }
}
