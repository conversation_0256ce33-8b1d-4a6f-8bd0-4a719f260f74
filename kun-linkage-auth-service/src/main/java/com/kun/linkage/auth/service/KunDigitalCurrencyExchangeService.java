package com.kun.linkage.auth.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
public class KunDigitalCurrencyExchangeService {

    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;

    /**
     * 请求数币兑换价格
     *
     * @param organizationBasicInfo 机构基本信息
     * @param amount 兑换金额
     * @param sideType 交易类型
     *                <p>
     *                1. BUY: 买入
     *                2. SELL: 卖出
     *                </p>
     *                <p>
     *                注意：kun接口币对必须数币在前法币在后
     *                </p>
     *                <p>
     *                例如：数币BTC兑换法币CNY，sideType为BUY时，kun接口的币对应为BTC_CNY；
     *                数币CNY兑换法币BTC，sideType为SELL时，kun接口的币对应为CNY_BTC。
     *                </p>
     *                <p>
     *                注意：kun接口币对必须数币在前法币在后
     *                </p>
     *                <p>
     *                例如：数币BTC兑换法币CNY，sideType为BUY时，kun接口的币对应为BTC_CNY；
     *                数币CNY兑换法币BTC，sideType为SELL时，kun接口的币对应为CNY_BTC。
     *                </p>
     * @param dicitalCurrencyCode 数币币种
     * @param targetCurrencyCode
     * @return
     */
    public BigDecimal askPrice(OrganizationBasicInfo organizationBasicInfo, BigDecimal amount, KunSideTypeEnum sideType,
        String dicitalCurrencyCode, String targetCurrencyCode) {
        log.info("请求数币兑换价格, 机构号: {}, 金额: {}, 交易类型: {}, 来源币种: {}, 目标币种: {}",
            organizationBasicInfo.getOrganizationNo(), amount, sideType, dicitalCurrencyCode, targetCurrencyCode);
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunAskPriceReq.setPayAmount(amount);
        kunAskPriceReq.setSideType(sideType.getType());
        // kun接口币对必须数币在前法币在后
        kunAskPriceReq.setSymbol(dicitalCurrencyCode + "_" + targetCurrencyCode);
        log.info("调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
        Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
        log.info("调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
        if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(
            HttpStatus.SC_OK)) && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData()
            .getPrice() != null) {
            // 此处应使用kun接口返回汇率的导数
            return BigDecimal.ONE.divide(kunAskPriceRspResult.getData().getPrice(), 5, RoundingMode.DOWN);
        } else {
            log.error("调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_KUN_FAIL.getCode());
        }
    }

    /**
     * 调用KUN账户扣除数币
     *
     * @param organizationBasicInfo
     * @param requestNo
     * @param currencyCode
     * @param amount
     * @param remark
     * @return
     */
    public Result<KunDebitSubRsp> kunDebitSub(OrganizationBasicInfo organizationBasicInfo, String requestNo,
        String currencyCode, BigDecimal amount, String remark) {
        KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
        kunDebitSubReq.setToken(organizationBasicInfo.getMpcToken());
        kunDebitSubReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        kunDebitSubReq.setRequestNo(requestNo);
        kunDebitSubReq.setCurrency(currencyCode);
        kunDebitSubReq.setAmount(amount);
        kunDebitSubReq.setRemark(remark);
        log.info("调用KUN账户扣除数币开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
        Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
        log.info("调用KUN账户扣除数币结束,响应参数:{}", JSON.toJSONString(result));
        return result;
    }
}
