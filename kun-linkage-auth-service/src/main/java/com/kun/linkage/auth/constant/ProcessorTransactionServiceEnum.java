package com.kun.linkage.auth.constant;

import com.kun.linkage.auth.service.impl.BPCTransactionService;
import org.apache.commons.lang3.StringUtils;

public enum ProcessorTransactionServiceEnum {

    /**
     * 卡核心交易处理器(BPC通道交易)
     */
    BPC_GW("BPC-GW", BPCTransactionService.class),
    ;

    private String processor;

    private Class beanClass;

    ProcessorTransactionServiceEnum(String processor, Class beanClass) {
        this.processor = processor;
        this.beanClass = beanClass;
    }

    public String getProcessor() {
        return processor;
    }

    public Class getBeanClass() {
        return beanClass;
    }

    public static Class getByProcessor(String processor) {
        if (StringUtils.isBlank(processor)) {
            return null;
        }
        for (ProcessorTransactionServiceEnum value : ProcessorTransactionServiceEnum.values()) {
            if (value.getProcessor().equals(processor)) {
                return value.getBeanClass();
            }
        }
        return null;
    }
}
