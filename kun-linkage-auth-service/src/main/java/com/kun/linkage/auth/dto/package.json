{"logContext": {"traceId": "d4dc0af7996f4f62944f69395ef88296", "spanId": "1"}, "transactionId": "2019996786591866880", "baseAuthRequestVO": {"requestId": "S********000000044", "processor": "BPC-GW", "transType": "010000", "status": "SUCC", "errorCode": "0000", "errorMessage": "Success", "gatewayCardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "processorCardId": "870357958VC00000021", "issuerCardId": "************", "transCurrency": "USD", "transCurrencyExponent": 2, "transAmount": 35.0, "cardholderCurrency": "USD", "cardholderCurrencyExponent": 2, "cardholderAmount": 35.0, "markupAmount": 0.0, "cardholderMarkupAmount": 35.0, "markupRate": 0, "conversionRateCardholderBilling": 0.1, "acquireReferenceNo": "************", "systemsTraceAuditNumber": "731044", "processorExt1": "************", "cardAcceptorId": "***************", "cardAcceptorTid": "RECTRS01", "mcc": "5411", "data": {"requestId": "S********000000044", "transType": 1, "authorizationType": 11, "accountingDirection": 1, "referenceNo": "************", "transId": "S********000000044", "originalTransId": "", "cardId": "870357958VC00000021", "channelData": {"accountIdentification": "T*********355", "acquiringInstitutionIdentificationCode": "476119", "additionalAmounts": {"acquirerFeeAmount": "D************", "issuerFeeAmountAndCurrency": {"feeAmoun": "************", "feeCurrency": "840", "feeSign": "C"}, "transactionAmountAccountCurrency": "***********"}, "additionalData": {"acquierInstitueIdentifier": "9002", "acquirerCountryCode": "344", "acquirerNetworkIdentifier": "0999", "cardId": "************", "cardType": "00", "customerId": "*********", "customerMobilePhone": "***********", "feTraceNumber": "************", "feTransactionDateAndTime": "**************", "feTransactionNumber": "************", "issuerNetworkIdentifier": "0002", "localTransactionDateAndTime": "************", "networkReferenceData": "***************", "posEnvironmen": "R", "senderReferenceNumber": "************", "settlementType": "2", "svfeTransactionType": "680", "transactionRiskScore": "09"}, "amountAccount": "************", "amountCardholderBilling": "************", "amountTransaction": "************", "cardAcceptorIdentificationCode": "***************", "cardAcceptorNameAndLocation": {"countryCode": "HK", "merchantNumberOfBankName": "ACQUIRER NAME>HongKong               "}, "cardAcceptorTerminalIdentification": "RECTRS01", "conversionRateAccount": "********", "conversionRateCardholderBilling": "********", "currencyCodeAccount": "840", "currencyCodeCardholderBilling": "840", "currencyCodeTransaction": "840", "dateExpiration": "2806", "dateTimeLocalTransaction": "************", "merchantType": "5411", "messageType": "1100", "pointOfServiceDateCode": {"cardCaptureCapabilit": "0", "cardDataInputCapability": "0", "cardDataInputMode": "V", "cardDataOutputCapability": "0", "cardPresence": "0", "cardholderAuthenticationCapability": "0", "cardholderAuthenticationEntity": "5", "cardholderAuthenticationMethod": "0", "cardholderPresenceIndicator": "5", "operatingEnvironmen": "1", "pinCaptureCapability": "0", "terminalOutputCapability": "0"}, "processingCode": "000000", "retrievalReferenceNumber": "************", "settlementDate": "250731", "svfeIssuerInstitutionIdentifier": "7091", "systemsTraceAuditNumber": "731044", "transmissionDateAndTime": "**********"}}, "transId": "S********000000044", "originalTransId": "", "mid": "********", "mID": "********"}, "baseAuthResponseVO": {"requestId": "S********000000044", "returnCode": "0000"}, "transactionTypeEnum": "AUTHORIZATION", "transactionTypeCatogoryEnum": "SALES", "organizationBasicInfo": {"id": 9, "organizationNo": "********", "organizationName": "uu international", "businessType": "01", "countryCode": "HK", "status": "VALID", "key": "1938153188920758272", "sensitiveKey": "rw85EKdoILrNaoc/pu0QLTVpsN2uWG+AkNHBqrzFCLU=", "kunMid": "********", "mode": "2", "checkOrganizationAccountFlag": 0, "checkCustomerAccountFlag": 1, "isKycReported": 0, "isKycVerified": 0, "thirdPartyAuthorizationFlag": 0, "mpcTenantId": "1009", "mpcGroupCode": "KVCC", "mpcToken": "KVCC", "poolCurrencyCode": "HKD", "createTime": "2025-06-26T16:31:44", "createUserId": "********", "createUserName": "uu international", "lastModifyTime": "2025-07-29T17:05:43", "lastModifyUserId": "1698616182519705602", "lastModifyUserName": "Administrator"}, "organizationCustomerCardInfo": {"id": 1947560346203185153, "organizationNo": "********", "customerId": "1947198960607137793", "cardScheme": "0", "cardProductCode": "VC03", "cardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "cardNo": "35a02765fe5c7ba278496d60c698882d826049166d7c913e87d44a008b8ff03c", "maskedCardNo": "441359******7535", "currencyCode": "USD", "processor": "BPC-GW", "cardStatus": "NORMAL", "cardActiveStatus": "ACTIVATED", "email": "<EMAIL>", "createTime": "2025-07-22T15:32:25", "lastModifyTime": "2025-07-23T15:44:13"}, "customerBasicAccount": {"id": 1947560348879151105, "organizationNo": "********", "customerId": "1947198960607137793", "accountType": "001", "currencyCode": "USD", "accountNo": "0011947560348383203330", "status": "VALID", "createTime": "2025-07-22T15:32:25", "lastModifyTime": "2025-07-22T15:32:25"}, "authFlow": {"id": "2019996786591866880", "processor": "BPC-GW", "processorRequestId": "S********000000044", "processorTransId": "S********000000044", "originalProcessorTransId": "", "merchantNo": "********", "merchantName": "uu international", "customerId": "1947198960607137793", "status": "PENDING", "mti": "1100", "processingCode": "000000", "systemsTraceAuditNumber": "731044", "gatewayCardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "processorCardId": "870357958VC00000021", "issuerCardId": "************", "maskedCardNo": "441359******7535", "transType": "010000", "cardProductCode": "VC03", "transCurrency": "USD", "transCurrencyExponent": 2, "transAmount": 35.0, "transFee": 0, "cardholderBillingCurrency": "USD", "cardholderCurrencyExponent": 2, "cardholderBillingAmount": 35.0, "cardholderMarkupBillingAmount": 35.0, "markupRate": 0, "markupAmount": 0.0, "posEntryMode": "000150V05000", "transactionLocalDatetime": "************", "conversionRateCardholderBilling": 0.1, "approveCode": "H6Q5IK", "acquireReferenceNo": "************", "cardAcceptorName": "ACQUIRER NAME", "cardAcceptorId": "***************", "cardAcceptorTid": "RECTRS01", "cardAcceptorCountryCode": "HK", "cardAcceptorCity": "HongKong               ", "mcc": "5411", "processorExt1": "************", "remainingTransAmount": 35.0, "remainingBillingAmount": 35.0, "remainingMarkupBillingAmount": 35.0, "clearFlag": "N", "releaseFlag": "L", "transAccountingDate": "********", "createTime": "2025-07-31T02:27:25.000+00:00"}, "authFlowExt": {"authFlowId": "2019996786591866880", "merchantAccountFlag": 0, "cardholderAccountFlag": 1, "kunMid": "********", "thirdPartyAuthorizationFlag": 0, "mpcTenantId": "1009", "mpcGroupCode": "KVCC", "createTime": "2025-07-31T02:27:25.000+00:00"}, "thirdPartyAuthorizationFlag": false}