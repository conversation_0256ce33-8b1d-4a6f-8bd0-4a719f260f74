package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 账户日终快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@TableName("kl_account_daily_snapshot")
public class AccountDailySnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 账户号
     */
    private String accountNo;

    /**
     * 业务系统:KL/VCC
     */
    private String businessSystem;

    /**
     * 会计日
     */
    private String accountingDate;

    /**
     * 快照日期
     */
    private String snapshotDate;

    /**
     * 账户类型001:法币基本账户/002:数币基本账户/003:信用法币账户
     */
    private String accountType;

    /**
     * 币种码
     */
    private String currencyCode;

    /**
     * 币种精度
     */
    private Integer currencyPrecision;

    /**
     * 初始金额
     */
    private BigDecimal initialAmount;

    /**
     * 初始可用金额
     */
    private BigDecimal initialAvailableAmount;

    /**
     * 借记金额
     */
    private BigDecimal debitAmount;

    /**
     * 贷记金额
     */
    private BigDecimal creditAmount;

    /**
     * 最终总余额
     */
    private BigDecimal finalTotalBalanceAmount;

    /**
     * 初始冻结金额
     */
    private BigDecimal initialFrozenAmount;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 释放金额
     */
    private BigDecimal releaseAmount;

    /**
     * 最终冻结金额
     */
    private BigDecimal finalFrozenAmount;

    /**
     * 最终可用余额
     */
    private BigDecimal finalAvailableAmount;

    /**
     * 业务机构号(主要用于页面查询使用)
     */
    private String businessOrganizationNo;

    /**
     * 业务客户号(主要用于页面查询使用)
     */
    private String businessCustomerId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
    public String getBusinessSystem() {
        return businessSystem;
    }

    public void setBusinessSystem(String businessSystem) {
        this.businessSystem = businessSystem;
    }
    public String getAccountingDate() {
        return accountingDate;
    }

    public void setAccountingDate(String accountingDate) {
        this.accountingDate = accountingDate;
    }
    public String getSnapshotDate() {
        return snapshotDate;
    }

    public void setSnapshotDate(String snapshotDate) {
        this.snapshotDate = snapshotDate;
    }
    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Integer getCurrencyPrecision() {
        return currencyPrecision;
    }

    public void setCurrencyPrecision(Integer currencyPrecision) {
        this.currencyPrecision = currencyPrecision;
    }

    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

    public BigDecimal getInitialAvailableAmount() {
        return initialAvailableAmount;
    }

    public void setInitialAvailableAmount(BigDecimal initialAvailableAmount) {
        this.initialAvailableAmount = initialAvailableAmount;
    }

    public BigDecimal getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(BigDecimal debitAmount) {
        this.debitAmount = debitAmount;
    }
    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }
    public BigDecimal getFinalTotalBalanceAmount() {
        return finalTotalBalanceAmount;
    }

    public void setFinalTotalBalanceAmount(BigDecimal finalTotalBalanceAmount) {
        this.finalTotalBalanceAmount = finalTotalBalanceAmount;
    }
    public BigDecimal getInitialFrozenAmount() {
        return initialFrozenAmount;
    }

    public void setInitialFrozenAmount(BigDecimal initialFrozenAmount) {
        this.initialFrozenAmount = initialFrozenAmount;
    }
    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }
    public BigDecimal getReleaseAmount() {
        return releaseAmount;
    }

    public void setReleaseAmount(BigDecimal releaseAmount) {
        this.releaseAmount = releaseAmount;
    }
    public BigDecimal getFinalFrozenAmount() {
        return finalFrozenAmount;
    }

    public void setFinalFrozenAmount(BigDecimal finalFrozenAmount) {
        this.finalFrozenAmount = finalFrozenAmount;
    }
    public BigDecimal getFinalAvailableAmount() {
        return finalAvailableAmount;
    }

    public void setFinalAvailableAmount(BigDecimal finalAvailableAmount) {
        this.finalAvailableAmount = finalAvailableAmount;
    }
    public String getBusinessOrganizationNo() {
        return businessOrganizationNo;
    }

    public void setBusinessOrganizationNo(String businessOrganizationNo) {
        this.businessOrganizationNo = businessOrganizationNo;
    }
    public String getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(String businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AccountDailySnapshot{" +
                "id=" + id +
                ", accountNo='" + accountNo + '\'' +
                ", businessSystem='" + businessSystem + '\'' +
                ", accountingDate='" + accountingDate + '\'' +
                ", snapshotDate='" + snapshotDate + '\'' +
                ", accountType='" + accountType + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", initialAmount=" + initialAmount +
                ", initialAvailableAmount=" + initialAvailableAmount +
                ", debitAmount=" + debitAmount +
                ", creditAmount=" + creditAmount +
                ", finalTotalBalanceAmount=" + finalTotalBalanceAmount +
                ", initialFrozenAmount=" + initialFrozenAmount +
                ", frozenAmount=" + frozenAmount +
                ", releaseAmount=" + releaseAmount +
                ", finalFrozenAmount=" + finalFrozenAmount +
                ", finalAvailableAmount=" + finalAvailableAmount +
                ", businessOrganizationNo='" + businessOrganizationNo + '\'' +
                ", businessCustomerId='" + businessCustomerId + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
