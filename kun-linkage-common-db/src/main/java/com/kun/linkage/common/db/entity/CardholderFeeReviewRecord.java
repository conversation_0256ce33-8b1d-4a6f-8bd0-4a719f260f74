package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 持卡人费用信息审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@TableName("kl_cardholder_fee_review_record")
public class CardholderFeeReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核id
     */
    @TableId(value = "review_id", type = IdType.ASSIGN_ID)
    private Long reviewId;

    /**
     * 操作类型:ADD,MODIFY
     */
    @TableField("operator_type")
    private String operatorType;

    /**
     * 费率信息表id,修改时有值
     */
    @TableField("fee_id")
    private Long feeId;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 卡产品编码
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 生效开始时间
     */
    @TableField("effective_start_time")
    private LocalDateTime effectiveStartTime;

    /**
     * 生效结束时间
     */
    @TableField("effective_end_time")
    private LocalDateTime effectiveEndTime;

    /**
     * 审核状态
     */
    @TableField("review_status")
    private String reviewStatus;

    /**
     * 审核备注
     */
    @TableField("review_Reason")
    private String reviewReason;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 提交人id
     */
    @TableField("submit_user_id")
    private String submitUserId;

    /**
     * 提交人名称
     */
    @TableField("submit_user_name")
    private String submitUserName;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核人id
     */
    @TableField("review_user_id")
    private String reviewUserId;

    /**
     * 审核人名称
     */
    @TableField("review_user_name")
    private String reviewUserName;

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }
    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    public Long getFeeId() {
        return feeId;
    }

    public void setFeeId(Long feeId) {
        this.feeId = feeId;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public LocalDateTime getEffectiveStartTime() {
        return effectiveStartTime;
    }

    public void setEffectiveStartTime(LocalDateTime effectiveStartTime) {
        this.effectiveStartTime = effectiveStartTime;
    }
    public LocalDateTime getEffectiveEndTime() {
        return effectiveEndTime;
    }

    public void setEffectiveEndTime(LocalDateTime effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
    }
    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    public String getReviewReason() {
        return reviewReason;
    }

    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }
    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }
    public LocalDateTime getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(LocalDateTime reviewTime) {
        this.reviewTime = reviewTime;
    }
    public String getReviewUserId() {
        return reviewUserId;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }
    public String getReviewUserName() {
        return reviewUserName;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    @Override
    public String toString() {
        return "CardholderFeeReviewRecord{" +
            "reviewId=" + reviewId +
            ", operatorType=" + operatorType +
            ", feeId=" + feeId +
            ", organizationNo=" + organizationNo +
            ", cardProductCode=" + cardProductCode +
            ", status=" + status +
            ", effectiveStartTime=" + effectiveStartTime +
            ", effectiveEndTime=" + effectiveEndTime +
            ", reviewStatus=" + reviewStatus +
            ", reviewReason=" + reviewReason +
            ", submitTime=" + submitTime +
            ", submitUserId=" + submitUserId +
            ", submitUserName=" + submitUserName +
            ", reviewTime=" + reviewTime +
            ", reviewUserId=" + reviewUserId +
            ", reviewUserName=" + reviewUserName +
        "}";
    }
}
