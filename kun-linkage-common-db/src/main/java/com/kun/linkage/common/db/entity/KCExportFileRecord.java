package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 导出文件记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@TableName("kc_export_file_record")
public class KCExportFileRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件记录ID
     */
    @TableId(value = "file_record_id", type = IdType.ASSIGN_ID)
    private String fileRecordId;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 文件状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败
     */
    @TableField("file_status")
    private String fileStatus;

    /**
     * S3文件URL
     */
    @TableField("s3_url")
    private String s3Url;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 创建用户ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 失败原因
     */
    @TableField("error_message")
    private String errorMessage;

    public String getFileRecordId() {
        return fileRecordId;
    }

    public void setFileRecordId(String fileRecordId) {
        this.fileRecordId = fileRecordId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getS3Url() {
        return s3Url;
    }

    public void setS3Url(String s3Url) {
        this.s3Url = s3Url;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "KCExportFileRecord{" +
            "fileRecordId=" + fileRecordId +
            ", fileName=" + fileName +
            ", fileType=" + fileType +
            ", fileStatus=" + fileStatus +
            ", s3Url=" + s3Url +
            ", fileSize=" + fileSize +
            ", createdBy=" + createdBy +
            ", createdTime=" + createdTime +
            ", updatedTime=" + updatedTime +
            ", errorMessage=" + errorMessage +
        "}";
    }
}
