package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 持卡人费用信息明细审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@TableName("kl_cardholder_fee_detail_review_record")
public class CardholderFeeDetailReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核明细id
     */
    @TableId(value = "review_detail_id", type = IdType.ASSIGN_ID)
    private Long reviewDetailId;

    /**
     * 所属审核id
     */
    @TableField("review_id")
    private Long reviewId;

    /**
     * 操作类型:ADD,MODIFY
     */
    @TableField("operator_type")
    private String operatorType;

    /**
     * 所属费用信息id,修改时有值
     */
    @TableField("fee_id")
    private Long feeId;

    /**
     * 所属费用明细信息id,修改时有值
     */
    @TableField("fee_detail_id")
    private Long feeDetailId;

    /**
     * 手续费类型
     */
    @TableField("fee_type")
    private String feeType;

    /**
     * 金额区间:最小金额
     */
    @TableField("min_amount")
    private BigDecimal minAmount;

    /**
     * 金额区间:最大金额
     */
    @TableField("max_amount")
    private BigDecimal maxAmount;

    /**
     * 划线费率-比例
     */
    @TableField("scribing_proportion_rate")
    private BigDecimal scribingProportionRate;

    /**
     * 划线费率-比例的保底金额
     */
    @TableField("scribing_proportion_min_amount")
    private BigDecimal scribingProportionMinAmount;

    /**
     * 划线费率-比例的封顶金额
     */
    @TableField("scribing_proportion_max_amount")
    private BigDecimal scribingProportionMaxAmount;

    /**
     * 划线费率-固定值
     */
    @TableField("scribing_fixed_amount")
    private BigDecimal scribingFixedAmount;

    /**
     * 实际费率-比例
     */
    @TableField("actual_proportion_rate")
    private BigDecimal actualProportionRate;

    /**
     * 实际费率-比例的保底金额
     */
    @TableField("actual_proportion_min_amount")
    private BigDecimal actualProportionMinAmount;

    /**
     * 实际费率-比例的封顶金额
     */
    @TableField("actual_proportion_max_amount")
    private BigDecimal actualProportionMaxAmount;

    /**
     * 实际费率-固定值
     */
    @TableField("actual_fixed_amount")
    private BigDecimal actualFixedAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    public Long getReviewDetailId() {
        return reviewDetailId;
    }

    public void setReviewDetailId(Long reviewDetailId) {
        this.reviewDetailId = reviewDetailId;
    }
    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }
    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    public Long getFeeId() {
        return feeId;
    }

    public void setFeeId(Long feeId) {
        this.feeId = feeId;
    }
    public Long getFeeDetailId() {
        return feeDetailId;
    }

    public void setFeeDetailId(Long feeDetailId) {
        this.feeDetailId = feeDetailId;
    }
    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }
    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }
    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }
    public BigDecimal getScribingProportionRate() {
        return scribingProportionRate;
    }

    public void setScribingProportionRate(BigDecimal scribingProportionRate) {
        this.scribingProportionRate = scribingProportionRate;
    }
    public BigDecimal getScribingProportionMinAmount() {
        return scribingProportionMinAmount;
    }

    public void setScribingProportionMinAmount(BigDecimal scribingProportionMinAmount) {
        this.scribingProportionMinAmount = scribingProportionMinAmount;
    }
    public BigDecimal getScribingProportionMaxAmount() {
        return scribingProportionMaxAmount;
    }

    public void setScribingProportionMaxAmount(BigDecimal scribingProportionMaxAmount) {
        this.scribingProportionMaxAmount = scribingProportionMaxAmount;
    }
    public BigDecimal getScribingFixedAmount() {
        return scribingFixedAmount;
    }

    public void setScribingFixedAmount(BigDecimal scribingFixedAmount) {
        this.scribingFixedAmount = scribingFixedAmount;
    }
    public BigDecimal getActualProportionRate() {
        return actualProportionRate;
    }

    public void setActualProportionRate(BigDecimal actualProportionRate) {
        this.actualProportionRate = actualProportionRate;
    }
    public BigDecimal getActualProportionMinAmount() {
        return actualProportionMinAmount;
    }

    public void setActualProportionMinAmount(BigDecimal actualProportionMinAmount) {
        this.actualProportionMinAmount = actualProportionMinAmount;
    }
    public BigDecimal getActualProportionMaxAmount() {
        return actualProportionMaxAmount;
    }

    public void setActualProportionMaxAmount(BigDecimal actualProportionMaxAmount) {
        this.actualProportionMaxAmount = actualProportionMaxAmount;
    }
    public BigDecimal getActualFixedAmount() {
        return actualFixedAmount;
    }

    public void setActualFixedAmount(BigDecimal actualFixedAmount) {
        this.actualFixedAmount = actualFixedAmount;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "CardholderFeeDetailReviewRecord{" +
            "reviewDetailId=" + reviewDetailId +
            ", reviewId=" + reviewId +
            ", operatorType=" + operatorType +
            ", feeId=" + feeId +
            ", feeDetailId=" + feeDetailId +
            ", feeType=" + feeType +
            ", minAmount=" + minAmount +
            ", maxAmount=" + maxAmount +
            ", scribingProportionRate=" + scribingProportionRate +
            ", scribingProportionMinAmount=" + scribingProportionMinAmount +
            ", scribingProportionMaxAmount=" + scribingProportionMaxAmount +
            ", scribingFixedAmount=" + scribingFixedAmount +
            ", actualProportionRate=" + actualProportionRate +
            ", actualProportionMinAmount=" + actualProportionMinAmount +
            ", actualProportionMaxAmount=" + actualProportionMaxAmount +
            ", actualFixedAmount=" + actualFixedAmount +
            ", createTime=" + createTime +
        "}";
    }
}
