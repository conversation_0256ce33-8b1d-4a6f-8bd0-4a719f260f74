package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 卡信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-17
 */
@TableName("kc_card_info")
public class CardInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键 id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源系统:VCC;KL
     */
    @TableField("`system`")
    private String system;

    /**
     * 卡片申请流水号
     */
    private String transOrderNo;

    /**
     * 会员编号
     */
    private Long memberId;

    /**
     * 通道返回的卡ID
     */
    private String cardId;

    /**
     * KCard卡ID
     */
    private String kcardId;

    /**
     * 卡片后四位
     */
    private String lastFour;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 币种
     */
    private String ccy;

    /**
     * 可用余额
     */
    private BigDecimal availBalance;

    /**
     * 卡片限额
     */
    private BigDecimal cardLimit;

    /**
     * 单日限额
     */
    private BigDecimal dailyCardLimit;

    /**
     * 冻结金额
     */
    private BigDecimal holdAmt;

    /**
     * 卡状态
     */
    private String cardStatus;

    /**
     * 所属通道
     */
    private String channelNo;


    /**
     * 开卡时间
     */
    private LocalDateTime openTime;

    /**
     * 有效日期
     */
    private LocalDate expDate;

    /**
     * 卡片剩余交易次数
     */
    private Integer remainingTransactionTimes;

    /**
     * 是否限额
     */
    private Boolean isLimit;

    /**
     * 绑定邮箱
     */
    private String email;

    /**
     * 区号
     */
    private String dialCode;

    /**
     * 绑定手机号码
     */
    private String phoneNumber;

    /**
     * 开启Mcc规则类型（WHITE-白名单 BLACK-黑名单）
     */
    private String ruleType;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public Boolean getLimit() {
        return isLimit;
    }

    public void setLimit(Boolean limit) {
        isLimit = limit;
    }

    public String getTransOrderNo() {
        return transOrderNo;
    }

    public void setTransOrderNo(String transOrderNo) {
        this.transOrderNo = transOrderNo;
    }
    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public String getKcardId() {
        return kcardId;
    }

    public void setKcardId(String kcardId) {
        this.kcardId = kcardId;
    }
    public String getLastFour() {
        return lastFour;
    }

    public void setLastFour(String lastFour) {
        this.lastFour = lastFour;
    }
    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }
    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }
    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }
    public BigDecimal getAvailBalance() {
        return availBalance;
    }

    public void setAvailBalance(BigDecimal availBalance) {
        this.availBalance = availBalance;
    }
    public BigDecimal getCardLimit() {
        return cardLimit;
    }

    public void setCardLimit(BigDecimal cardLimit) {
        this.cardLimit = cardLimit;
    }
    public BigDecimal getDailyCardLimit() {
        return dailyCardLimit;
    }

    public void setDailyCardLimit(BigDecimal dailyCardLimit) {
        this.dailyCardLimit = dailyCardLimit;
    }
    public BigDecimal getHoldAmt() {
        return holdAmt;
    }

    public void setHoldAmt(BigDecimal holdAmt) {
        this.holdAmt = holdAmt;
    }
    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }
    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public LocalDateTime getOpenTime() {
        return openTime;
    }

    public void setOpenTime(LocalDateTime openTime) {
        this.openTime = openTime;
    }
    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }
    public Integer getRemainingTransactionTimes() {
        return remainingTransactionTimes;
    }

    public void setRemainingTransactionTimes(Integer remainingTransactionTimes) {
        this.remainingTransactionTimes = remainingTransactionTimes;
    }
    public Boolean getIsLimit() {
        return isLimit;
    }

    public void setIsLimit(Boolean isLimit) {
        this.isLimit = isLimit;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getDialCode() {
        return dialCode;
    }

    public void setDialCode(String dialCode) {
        this.dialCode = dialCode;
    }
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CardInfo{" +
            "id=" + id +
            "system=" + system +
            ", transOrderNo=" + transOrderNo +
            ", memberId=" + memberId +
            ", cardId=" + cardId +
            ", kcardId=" + kcardId +
            ", lastFour=" + lastFour +
            ", cardNo=" + cardNo +
            ", cardName=" + cardName +
            ", ccy=" + ccy +
            ", availBalance=" + availBalance +
            ", cardLimit=" + cardLimit +
            ", dailyCardLimit=" + dailyCardLimit +
            ", holdAmt=" + holdAmt +
            ", cardStatus=" + cardStatus +
            ", channelNo=" + channelNo +
            ", openTime=" + openTime +
            ", expDate=" + expDate +
            ", remainingTransactionTimes=" + remainingTransactionTimes +
            ", isLimit=" + isLimit +
            ", email=" + email +
            ", dialCode=" + dialCode +
            ", phoneNumber=" + phoneNumber +
            ", ruleType=" + ruleType +
            ", createUser=" + createUser +
            ", createTime=" + createTime +
            ", updateUser=" + updateUser +
            ", updateTime=" + updateTime +
        "}";
    }
}
