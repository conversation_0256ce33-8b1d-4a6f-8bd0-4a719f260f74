
package com.kun.linkage.common.db.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Properties;

public class LocalDateTimeDynamicYearQuarterShardingAlgorithm implements StandardShardingAlgorithm<LocalDateTime> {
    private Properties props = new Properties();

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<LocalDateTime> shardingValue) {
        LocalDateTime time = shardingValue.getValue();
        int month = time.getMonthValue();
        int quarter = (month - 1) / 3 + 1;
        String targetTable = shardingValue.getLogicTableName() + "_" + time.getYear() + "Q" + quarter;

        if (availableTargetNames.contains(targetTable)) {
            return targetTable;
        }
        // 表不存在
        throw new RuntimeException("目标表 " + targetTable + " 不存在");
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<LocalDateTime> shardingValue) {
        Collection<String> result = new LinkedHashSet<>();
        LocalDateTime start = shardingValue.getValueRange().lowerEndpoint();
        LocalDateTime end = shardingValue.getValueRange().upperEndpoint();
        // 计算开始季度
        int startYear = start.getYear();
        int startQuarter = (start.getMonthValue() - 1) / 3 + 1;
        // 计算结束季度
        int endYear = end.getYear();
        int endQuarter = (end.getMonthValue() - 1) / 3 + 1;
        // 遍历季度范围
        int currentYear = startYear;
        int currentQuarter = startQuarter;
        while (true) {
            String suffix = currentYear + "Q" + currentQuarter;
            String targetTable = shardingValue.getLogicTableName() + "_" + suffix;
            if (availableTargetNames.contains(targetTable)) {
                result.add(targetTable);
            }
            // 检查是否到达结束季度
            if (currentYear == endYear && currentQuarter == endQuarter) {
                break;
            }
            // 移动到下一个季度
            currentQuarter++;
            if (currentQuarter > 4) {
                currentQuarter = 1;
                currentYear++;
            }
        }
        if (result.isEmpty()) {
            throw new RuntimeException("未找到目标表");
        }
        return result;
    }

    @Override
    public Properties getProps() {
        return props;
    }

    @Override
    public void init(Properties props) {
        this.props = props;
    }

    @Override
    public String getType() {
        return "LOCAL_DATE_TIME_DYNAMIC_YEAR_QUARTER";
    }
}

    