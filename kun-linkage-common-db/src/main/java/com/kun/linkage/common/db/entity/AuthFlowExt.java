package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Kunlinkage授权流水扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@TableName("kl_auth_flow_ext")
public class AuthFlowExt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易ID
     */
    @TableId("auth_flow_id")
    private String authFlowId;

    /**
     * 商户账户标志，0-不检查商户账户，1-检查商户账户
     */
    @TableField("merchant_account_flag")
    private Integer merchantAccountFlag;

    /**
     * 商户账户请求号
     */
    @TableField("merchant_account_request_no")
    private String merchantAccountRequestNo;

    /**
     * 商户账户冲回请求号
     */
    @TableField("merchant_account_reversal_no")
    private String merchantAccountReversalNo;

    /**
     * 持卡人账户标志，0-不检查持卡人账户，1-检查持卡人账户
     */
    @TableField("cardholder_account_flag")
    private Integer cardholderAccountFlag;

    /**
     * 持卡人账户请求号
     */
    @TableField("cardholder_account_request_no")
    private String cardholderAccountRequestNo;

    /**
     * 持卡人账户冲回请求号
     */
    @TableField("cardholder_account_reversal_no")
    private String cardholderAccountReversalNo;

    /**
     * Kun账户标志
     */
    @TableField("kun_account_flag")
    private String kunAccountFlag;

    /**
     * kun对应的商户号
     */
    @TableField("kun_mid")
    private String kunMid;

    /**
     * Kun账户请求号
     */
    @TableField("kun_account_request_no")
    private String kunAccountRequestNo;

    /**
     * Kun账户冲回请求号
     */
    @TableField("kun_account_reversal_no")
    private String kunAccountReversalNo;

    /**
     * Kun账户标志
     */
    @TableField("payx_account_flag")
    private String payxAccountFlag;

    /**
     * payx对应的商户号
     */
    @TableField("payx_mid")
    private String payxMid;

    /**
     * payx账户请求号
     */
    @TableField("payx_account_request_no")
    private String payxAccountRequestNo;

    /**
     * payx账户冲回请求号
     */
    @TableField("payx_account_reversal_no")
    private String payxAccountReversalNo;

    /**
     * 第三方授权标记
     */
    @TableField("third_party_authorization_flag")
    private Integer thirdPartyAuthorizationFlag;

    /**
     * MPC租户ID
     */
    @TableField("mpc_tenant_id")
    private String mpcTenantId;

    /**
     * MPC集团号
     */
    @TableField("mpc_group_code")
    private String mpcGroupCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public String getAuthFlowId() {
        return authFlowId;
    }

    public void setAuthFlowId(String authFlowId) {
        this.authFlowId = authFlowId;
    }
    public Integer getMerchantAccountFlag() {
        return merchantAccountFlag;
    }

    public void setMerchantAccountFlag(Integer merchantAccountFlag) {
        this.merchantAccountFlag = merchantAccountFlag;
    }
    public String getMerchantAccountRequestNo() {
        return merchantAccountRequestNo;
    }

    public void setMerchantAccountRequestNo(String merchantAccountRequestNo) {
        this.merchantAccountRequestNo = merchantAccountRequestNo;
    }
    public String getMerchantAccountReversalNo() {
        return merchantAccountReversalNo;
    }

    public void setMerchantAccountReversalNo(String merchantAccountReversalNo) {
        this.merchantAccountReversalNo = merchantAccountReversalNo;
    }
    public Integer getCardholderAccountFlag() {
        return cardholderAccountFlag;
    }

    public void setCardholderAccountFlag(Integer cardholderAccountFlag) {
        this.cardholderAccountFlag = cardholderAccountFlag;
    }
    public String getCardholderAccountRequestNo() {
        return cardholderAccountRequestNo;
    }

    public void setCardholderAccountRequestNo(String cardholderAccountRequestNo) {
        this.cardholderAccountRequestNo = cardholderAccountRequestNo;
    }
    public String getCardholderAccountReversalNo() {
        return cardholderAccountReversalNo;
    }

    public void setCardholderAccountReversalNo(String cardholderAccountReversalNo) {
        this.cardholderAccountReversalNo = cardholderAccountReversalNo;
    }
    public String getKunAccountFlag() {
        return kunAccountFlag;
    }

    public void setKunAccountFlag(String kunAccountFlag) {
        this.kunAccountFlag = kunAccountFlag;
    }
    public String getKunMid() {
        return kunMid;
    }

    public void setKunMid(String kunMid) {
        this.kunMid = kunMid;
    }
    public String getKunAccountRequestNo() {
        return kunAccountRequestNo;
    }

    public void setKunAccountRequestNo(String kunAccountRequestNo) {
        this.kunAccountRequestNo = kunAccountRequestNo;
    }
    public String getKunAccountReversalNo() {
        return kunAccountReversalNo;
    }

    public void setKunAccountReversalNo(String kunAccountReversalNo) {
        this.kunAccountReversalNo = kunAccountReversalNo;
    }
    public String getPayxAccountFlag() {
        return payxAccountFlag;
    }

    public void setPayxAccountFlag(String payxAccountFlag) {
        this.payxAccountFlag = payxAccountFlag;
    }
    public String getPayxMid() {
        return payxMid;
    }

    public void setPayxMid(String payxMid) {
        this.payxMid = payxMid;
    }
    public String getPayxAccountRequestNo() {
        return payxAccountRequestNo;
    }

    public void setPayxAccountRequestNo(String payxAccountRequestNo) {
        this.payxAccountRequestNo = payxAccountRequestNo;
    }
    public String getPayxAccountReversalNo() {
        return payxAccountReversalNo;
    }

    public void setPayxAccountReversalNo(String payxAccountReversalNo) {
        this.payxAccountReversalNo = payxAccountReversalNo;
    }
    public Integer getThirdPartyAuthorizationFlag() {
        return thirdPartyAuthorizationFlag;
    }

    public void setThirdPartyAuthorizationFlag(Integer thirdPartyAuthorizationFlag) {
        this.thirdPartyAuthorizationFlag = thirdPartyAuthorizationFlag;
    }
    public String getMpcTenantId() {
        return mpcTenantId;
    }

    public void setMpcTenantId(String mpcTenantId) {
        this.mpcTenantId = mpcTenantId;
    }
    public String getMpcGroupCode() {
        return mpcGroupCode;
    }

    public void setMpcGroupCode(String mpcGroupCode) {
        this.mpcGroupCode = mpcGroupCode;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AuthFlowExt{" +
            "authFlowId=" + authFlowId +
            ", merchantAccountFlag=" + merchantAccountFlag +
            ", merchantAccountRequestNo=" + merchantAccountRequestNo +
            ", merchantAccountReversalNo=" + merchantAccountReversalNo +
            ", cardholderAccountFlag=" + cardholderAccountFlag +
            ", cardholderAccountRequestNo=" + cardholderAccountRequestNo +
            ", cardholderAccountReversalNo=" + cardholderAccountReversalNo +
            ", kunAccountFlag=" + kunAccountFlag +
            ", kunMid=" + kunMid +
            ", kunAccountRequestNo=" + kunAccountRequestNo +
            ", kunAccountReversalNo=" + kunAccountReversalNo +
            ", payxAccountFlag=" + payxAccountFlag +
            ", payxMid=" + payxMid +
            ", payxAccountRequestNo=" + payxAccountRequestNo +
            ", payxAccountReversalNo=" + payxAccountReversalNo +
            ", thirdPartyAuthorizationFlag=" + thirdPartyAuthorizationFlag +
            ", mpcTenantId=" + mpcTenantId +
            ", mpcGroupCode=" + mpcGroupCode +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
