package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 技术参数配置表
 * </p>
 *
 * @since 2025-07-28
 */
@Data
@TableName("kl_technical_params")
public class TechnicalParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构编号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * AES密钥（从机构信息表获取）
     */
    @TableField("aes_key")
    private String aesKey;

    /**
     * webhook开关状态：0-关闭，1-开启
     */
    @TableField("webhook_enabled")
    private Integer webhookEnabled;

    /**
     * WebHook地址-KYC
     */
    @TableField("webhook_url_kyc")
    private String webhookUrlKyc;

    /**
     * WebHook地址-转三方授权
     */
    @TableField("webhook_url_third_party_auth")
    private String webhookUrlThirdPartyAuth;

    /**
     * WebHook地址-授权交易结果
     */
    @TableField("webhook_url_auth_result")
    private String webhookUrlAuthResult;

    /**
     * WebHook地址-3DS OTP
     */
    @TableField("webhook_url_3ds_otp")
    private String webhookUrl3dsOtp;

    /**
     * 状态：VALID-有效，INVALID-无效
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建人名称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    /**
     * 最后一次修改人id
     */
    @TableField("last_modify_user_id")
    private String lastModifyUserId;

    /**
     * 最后一次修改人名称
     */
    @TableField("last_modify_user_name")
    private String lastModifyUserName;
}
