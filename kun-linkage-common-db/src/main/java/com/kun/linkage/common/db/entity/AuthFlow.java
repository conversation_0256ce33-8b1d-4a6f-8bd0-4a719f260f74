package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * Kunlinkage授权流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@TableName("kl_auth_flow")
public class AuthFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易ID
     */
    @TableId("id")
    private String id;

    /**
     * Processor
     */
    @TableField("processor")
    private String processor;

    /**
     * 请求ID
     */
    @TableField("processor_request_id")
    private String processorRequestId;

    /**
     * processor交易ID
     */
    @TableField("processor_trans_id")
    private String processorTransId;

    /**
     * processor原交易ID
     */
    @TableField("original_processor_trans_id")
    private String originalProcessorTransId;

    /**
     * 商户号
     */
    @TableField("merchant_no")
    private String merchantNo;

    /**
     * 商户名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 状态,如：PENDING:处理中;SUCCESS:成功;FAIL:失败;
     */
    @TableField("status")
    private String status;

    /**
     * MTI
     */
    @TableField("mti")
    private String mti;

    /**
     * Processing code
     */
    @TableField("processing_code")
    private String processingCode;

    /**
     * 系统跟踪审计号
     */
    @TableField("systems_trace_audit_number")
    private String systemsTraceAuditNumber;

    /**
     * 卡ID
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 网关卡ID
     */
    @TableField("gateway_card_id")
    private String gatewayCardId;

    /**
     * 通道卡ID
     */
    @TableField("processor_card_id")
    private String processorCardId;

    /**
     * 发卡方卡ID
     */
    @TableField("issuer_card_id")
    private String issuerCardId;

    /**
     * 脱敏卡号
     */
    @TableField("masked_card_no")
    private String maskedCardNo;

    /**
     * 交易类型
     */
    @TableField("trans_type")
    private String transType;

    /**
     * 卡产品编号
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 交易币种
     */
    @TableField("trans_currency")
    private String transCurrency;

    /**
     * 交易币种的小数位数
     */
    @TableField("trans_currency_exponent")
    private Integer transCurrencyExponent;

    /**
     * 交易金额
     */
    @TableField("trans_amount")
    private BigDecimal transAmount;

    /**
     * 交易手续费
     */
    @TableField("trans_fee")
    private BigDecimal transFee;

    /**
     * 持卡人账单币种
     */
    @TableField("cardholder_billing_currency")
    private String cardholderBillingCurrency;

    /**
     * 持卡人账单币种的小数位数
     */
    @TableField("cardholder_currency_exponent")
    private Integer cardholderCurrencyExponent;

    /**
     * 持卡人账单金额
     */
    @TableField("cardholder_billing_amount")
    private BigDecimal cardholderBillingAmount;

    /**
     * 持卡人账单金额(含markup)
     */
    @TableField("cardholder_markup_billing_amount")
    private BigDecimal cardholderMarkupBillingAmount;

    /**
     * 持卡人markup费率
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * markup金额
     */
    @TableField("markup_amount")
    private BigDecimal markupAmount;

    /**
     * POS输入方式
     */
    @TableField("pos_entry_mode")
    private String posEntryMode;

    /**
     * PIN码输入方式
     */
    @TableField("point_pin_code")
    private String pointPinCode;

    /**
     * POS条件代码
     */
    @TableField("pos_condition_code")
    private String posConditionCode;

    /**
     * 交易发生地时间
     */
    @TableField("transaction_local_datetime")
    private String transactionLocalDatetime;

    /**
     * 持卡人账单币种与交易币种的汇率
     */
    @TableField("conversion_rate_cardholder_billing")
    private BigDecimal conversionRateCardholderBilling;

    /**
     * 授权码
     */
    @TableField("approve_code")
    private String approveCode;

    /**
     * 参考号
     */
    @TableField("acquire_reference_no")
    private String acquireReferenceNo;

    /**
     * 收单商户名称
     */
    @TableField("card_acceptor_name")
    private String cardAcceptorName;

    /**
     * 收单商户号
     */
    @TableField("card_acceptor_id")
    private String cardAcceptorId;

    /**
     * 收单商户终端号
     */
    @TableField("card_acceptor_tid")
    private String cardAcceptorTid;

    /**
     * 收单商户国家代码
     */
    @TableField("card_acceptor_country_code")
    private String cardAcceptorCountryCode;

    /**
     * 收单商户城市
     */
    @TableField("card_acceptor_city")
    private String cardAcceptorCity;

    /**
     * MCC
     */
    @TableField("mcc")
    private String mcc;

    /**
     * processor扩展字段1
     */
    @TableField("processor_ext1")
    private String processorExt1;

    /**
     * 剩余交易金额
     */
    @TableField("remaining_trans_amount")
    private BigDecimal remainingTransAmount;

    /**
     * 剩余持卡人账单金额(不含markup)
     */
    @TableField("remaining_billing_amount")
    private BigDecimal remainingBillingAmount;

    /**
     * 剩余持卡人账单金额(含markup)
     */
    @TableField("remaining_markup_billing_amount")
    private BigDecimal remainingMarkupBillingAmount;

    /**
     * 响应码
     */
    @TableField("response_code")
    private String responseCode;

    /**
     * 响应消息
     */
    @TableField("response_msg")
    private String responseMsg;

    /**
     * 原KL交易ID
     */
    @TableField("original_id")
    private String originalId;

    /**
     * 原交易processor请求ID
     */
    @TableField("original_processor_request_id")
    private String originalProcessorRequestId;

    /**
     * 原交易时间
     */
    @TableField("original_trans_time")
    private String originalTransTime;

    /**
     * 清分标记
     */
    @TableField("clear_flag")
    private String clearFlag;

    /**
     * 释放标记, L:未施放;R:已释放;NONE:无需释放
     */
    @TableField("release_flag")
    private String releaseFlag;

    /**
     * 释放时间
     */
    @TableField("release_time")
    private Date releaseTime;

    /**
     * 交易会计日
     */
    @TableField("trans_accounting_date")
    private String transAccountingDate;

    /**
     * 清分会计日
     */
    @TableField("clear_accounting_date")
    private String clearAccountingDate;

    /**
     * 交易完成时间
     */
    @TableField("trans_done_time")
    private Date transDoneTime;

    /**
     * 清算金额
     */
    @TableField("clear_amount")
    private BigDecimal clearAmount;

    /**
     * 清算账单金额(不含markup)
     */
    @TableField("clear_bill_amount")
    private BigDecimal clearBillAmount;

    /**
     * 清算账单金额(含markup)
     */
    @TableField("clear_bill_amount_with_markup")
    private BigDecimal clearBillAmountWithMarkup;

    /**
     * 释放交易金额
     */
    @TableField("release_trans_amount")
    private BigDecimal releaseTransAmount;

    /**
     * 释放持卡人markup账单金额
     */
    @TableField("release_markup_billing_amount")
    private BigDecimal releaseMarkupBillingAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
    public String getProcessorRequestId() {
        return processorRequestId;
    }

    public void setProcessorRequestId(String processorRequestId) {
        this.processorRequestId = processorRequestId;
    }
    public String getProcessorTransId() {
        return processorTransId;
    }

    public void setProcessorTransId(String processorTransId) {
        this.processorTransId = processorTransId;
    }
    public String getOriginalProcessorTransId() {
        return originalProcessorTransId;
    }

    public void setOriginalProcessorTransId(String originalProcessorTransId) {
        this.originalProcessorTransId = originalProcessorTransId;
    }
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }
    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }
    public String getProcessingCode() {
        return processingCode;
    }

    public void setProcessingCode(String processingCode) {
        this.processingCode = processingCode;
    }
    public String getSystemsTraceAuditNumber() {
        return systemsTraceAuditNumber;
    }

    public void setSystemsTraceAuditNumber(String systemsTraceAuditNumber) {
        this.systemsTraceAuditNumber = systemsTraceAuditNumber;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public String getGatewayCardId() {
        return gatewayCardId;
    }

    public void setGatewayCardId(String gatewayCardId) {
        this.gatewayCardId = gatewayCardId;
    }
    public String getProcessorCardId() {
        return processorCardId;
    }

    public void setProcessorCardId(String processorCardId) {
        this.processorCardId = processorCardId;
    }
    public String getIssuerCardId() {
        return issuerCardId;
    }

    public void setIssuerCardId(String issuerCardId) {
        this.issuerCardId = issuerCardId;
    }
    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getTransCurrency() {
        return transCurrency;
    }

    public void setTransCurrency(String transCurrency) {
        this.transCurrency = transCurrency;
    }
    public Integer getTransCurrencyExponent() {
        return transCurrencyExponent;
    }

    public void setTransCurrencyExponent(Integer transCurrencyExponent) {
        this.transCurrencyExponent = transCurrencyExponent;
    }
    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }
    public BigDecimal getTransFee() {
        return transFee;
    }

    public void setTransFee(BigDecimal transFee) {
        this.transFee = transFee;
    }
    public String getCardholderBillingCurrency() {
        return cardholderBillingCurrency;
    }

    public void setCardholderBillingCurrency(String cardholderBillingCurrency) {
        this.cardholderBillingCurrency = cardholderBillingCurrency;
    }
    public Integer getCardholderCurrencyExponent() {
        return cardholderCurrencyExponent;
    }

    public void setCardholderCurrencyExponent(Integer cardholderCurrencyExponent) {
        this.cardholderCurrencyExponent = cardholderCurrencyExponent;
    }
    public BigDecimal getCardholderBillingAmount() {
        return cardholderBillingAmount;
    }

    public void setCardholderBillingAmount(BigDecimal cardholderBillingAmount) {
        this.cardholderBillingAmount = cardholderBillingAmount;
    }
    public BigDecimal getCardholderMarkupBillingAmount() {
        return cardholderMarkupBillingAmount;
    }

    public void setCardholderMarkupBillingAmount(BigDecimal cardholderMarkupBillingAmount) {
        this.cardholderMarkupBillingAmount = cardholderMarkupBillingAmount;
    }
    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }
    public BigDecimal getMarkupAmount() {
        return markupAmount;
    }

    public void setMarkupAmount(BigDecimal markupAmount) {
        this.markupAmount = markupAmount;
    }
    public String getPosEntryMode() {
        return posEntryMode;
    }

    public void setPosEntryMode(String posEntryMode) {
        this.posEntryMode = posEntryMode;
    }
    public String getPointPinCode() {
        return pointPinCode;
    }

    public void setPointPinCode(String pointPinCode) {
        this.pointPinCode = pointPinCode;
    }
    public String getPosConditionCode() {
        return posConditionCode;
    }

    public void setPosConditionCode(String posConditionCode) {
        this.posConditionCode = posConditionCode;
    }
    public String getTransactionLocalDatetime() {
        return transactionLocalDatetime;
    }

    public void setTransactionLocalDatetime(String transactionLocalDatetime) {
        this.transactionLocalDatetime = transactionLocalDatetime;
    }
    public BigDecimal getConversionRateCardholderBilling() {
        return conversionRateCardholderBilling;
    }

    public void setConversionRateCardholderBilling(BigDecimal conversionRateCardholderBilling) {
        this.conversionRateCardholderBilling = conversionRateCardholderBilling;
    }
    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }
    public String getAcquireReferenceNo() {
        return acquireReferenceNo;
    }

    public void setAcquireReferenceNo(String acquireReferenceNo) {
        this.acquireReferenceNo = acquireReferenceNo;
    }
    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }
    public String getCardAcceptorId() {
        return cardAcceptorId;
    }

    public void setCardAcceptorId(String cardAcceptorId) {
        this.cardAcceptorId = cardAcceptorId;
    }
    public String getCardAcceptorTid() {
        return cardAcceptorTid;
    }

    public void setCardAcceptorTid(String cardAcceptorTid) {
        this.cardAcceptorTid = cardAcceptorTid;
    }
    public String getCardAcceptorCountryCode() {
        return cardAcceptorCountryCode;
    }

    public void setCardAcceptorCountryCode(String cardAcceptorCountryCode) {
        this.cardAcceptorCountryCode = cardAcceptorCountryCode;
    }
    public String getCardAcceptorCity() {
        return cardAcceptorCity;
    }

    public void setCardAcceptorCity(String cardAcceptorCity) {
        this.cardAcceptorCity = cardAcceptorCity;
    }
    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }
    public String getProcessorExt1() {
        return processorExt1;
    }

    public void setProcessorExt1(String processorExt1) {
        this.processorExt1 = processorExt1;
    }
    public BigDecimal getRemainingTransAmount() {
        return remainingTransAmount;
    }

    public void setRemainingTransAmount(BigDecimal remainingTransAmount) {
        this.remainingTransAmount = remainingTransAmount;
    }
    public BigDecimal getRemainingBillingAmount() {
        return remainingBillingAmount;
    }

    public void setRemainingBillingAmount(BigDecimal remainingBillingAmount) {
        this.remainingBillingAmount = remainingBillingAmount;
    }
    public BigDecimal getRemainingMarkupBillingAmount() {
        return remainingMarkupBillingAmount;
    }

    public void setRemainingMarkupBillingAmount(BigDecimal remainingMarkupBillingAmount) {
        this.remainingMarkupBillingAmount = remainingMarkupBillingAmount;
    }
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }
    public String getResponseMsg() {
        return responseMsg;
    }

    public void setResponseMsg(String responseMsg) {
        this.responseMsg = responseMsg;
    }
    public String getOriginalId() {
        return originalId;
    }

    public void setOriginalId(String originalId) {
        this.originalId = originalId;
    }
    public String getOriginalProcessorRequestId() {
        return originalProcessorRequestId;
    }

    public void setOriginalProcessorRequestId(String originalProcessorRequestId) {
        this.originalProcessorRequestId = originalProcessorRequestId;
    }
    public String getOriginalTransTime() {
        return originalTransTime;
    }

    public void setOriginalTransTime(String originalTransTime) {
        this.originalTransTime = originalTransTime;
    }
    public String getClearFlag() {
        return clearFlag;
    }

    public void setClearFlag(String clearFlag) {
        this.clearFlag = clearFlag;
    }
    public String getReleaseFlag() {
        return releaseFlag;
    }

    public void setReleaseFlag(String releaseFlag) {
        this.releaseFlag = releaseFlag;
    }
    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }
    public String getTransAccountingDate() {
        return transAccountingDate;
    }

    public void setTransAccountingDate(String transAccountingDate) {
        this.transAccountingDate = transAccountingDate;
    }
    public String getClearAccountingDate() {
        return clearAccountingDate;
    }

    public void setClearAccountingDate(String clearAccountingDate) {
        this.clearAccountingDate = clearAccountingDate;
    }
    public Date getTransDoneTime() {
        return transDoneTime;
    }

    public void setTransDoneTime(Date transDoneTime) {
        this.transDoneTime = transDoneTime;
    }
    public BigDecimal getClearAmount() {
        return clearAmount;
    }

    public void setClearAmount(BigDecimal clearAmount) {
        this.clearAmount = clearAmount;
    }
    public BigDecimal getClearBillAmount() {
        return clearBillAmount;
    }

    public void setClearBillAmount(BigDecimal clearBillAmount) {
        this.clearBillAmount = clearBillAmount;
    }
    public BigDecimal getClearBillAmountWithMarkup() {
        return clearBillAmountWithMarkup;
    }

    public void setClearBillAmountWithMarkup(BigDecimal clearBillAmountWithMarkup) {
        this.clearBillAmountWithMarkup = clearBillAmountWithMarkup;
    }
    public BigDecimal getReleaseTransAmount() {
        return releaseTransAmount;
    }

    public void setReleaseTransAmount(BigDecimal releaseTransAmount) {
        this.releaseTransAmount = releaseTransAmount;
    }
    public BigDecimal getReleaseMarkupBillingAmount() {
        return releaseMarkupBillingAmount;
    }

    public void setReleaseMarkupBillingAmount(BigDecimal releaseMarkupBillingAmount) {
        this.releaseMarkupBillingAmount = releaseMarkupBillingAmount;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AuthFlow{" +
            "id=" + id +
            ", processor=" + processor +
            ", processorRequestId=" + processorRequestId +
            ", processorTransId=" + processorTransId +
            ", originalProcessorTransId=" + originalProcessorTransId +
            ", merchantNo=" + merchantNo +
            ", merchantName=" + merchantName +
            ", customerId=" + customerId +
            ", status=" + status +
            ", mti=" + mti +
            ", processingCode=" + processingCode +
            ", systemsTraceAuditNumber=" + systemsTraceAuditNumber +
            ", cardId=" + cardId +
            ", gatewayCardId=" + gatewayCardId +
            ", processorCardId=" + processorCardId +
            ", issuerCardId=" + issuerCardId +
            ", maskedCardNo=" + maskedCardNo +
            ", transType=" + transType +
            ", cardProductCode=" + cardProductCode +
            ", transCurrency=" + transCurrency +
            ", transCurrencyExponent=" + transCurrencyExponent +
            ", transAmount=" + transAmount +
            ", transFee=" + transFee +
            ", cardholderBillingCurrency=" + cardholderBillingCurrency +
            ", cardholderCurrencyExponent=" + cardholderCurrencyExponent +
            ", cardholderBillingAmount=" + cardholderBillingAmount +
            ", cardholderMarkupBillingAmount=" + cardholderMarkupBillingAmount +
            ", markupRate=" + markupRate +
            ", markupAmount=" + markupAmount +
            ", posEntryMode=" + posEntryMode +
            ", pointPinCode=" + pointPinCode +
            ", posConditionCode=" + posConditionCode +
            ", transactionLocalDatetime=" + transactionLocalDatetime +
            ", conversionRateCardholderBilling=" + conversionRateCardholderBilling +
            ", approveCode=" + approveCode +
            ", acquireReferenceNo=" + acquireReferenceNo +
            ", cardAcceptorName=" + cardAcceptorName +
            ", cardAcceptorId=" + cardAcceptorId +
            ", cardAcceptorTid=" + cardAcceptorTid +
            ", cardAcceptorCountryCode=" + cardAcceptorCountryCode +
            ", cardAcceptorCity=" + cardAcceptorCity +
            ", mcc=" + mcc +
            ", processorExt1=" + processorExt1 +
            ", remainingTransAmount=" + remainingTransAmount +
            ", remainingBillingAmount=" + remainingBillingAmount +
            ", remainingMarkupBillingAmount=" + remainingMarkupBillingAmount +
            ", responseCode=" + responseCode +
            ", responseMsg=" + responseMsg +
            ", originalId=" + originalId +
            ", originalProcessorRequestId=" + originalProcessorRequestId +
            ", originalTransTime=" + originalTransTime +
            ", clearFlag=" + clearFlag +
            ", releaseFlag=" + releaseFlag +
            ", releaseTime=" + releaseTime +
            ", transAccountingDate=" + transAccountingDate +
            ", clearAccountingDate=" + clearAccountingDate +
            ", transDoneTime=" + transDoneTime +
            ", clearAmount=" + clearAmount +
            ", clearBillAmount=" + clearBillAmount +
            ", clearBillAmountWithMarkup=" + clearBillAmountWithMarkup +
            ", releaseTransAmount=" + releaseTransAmount +
            ", releaseMarkupBillingAmount=" + releaseMarkupBillingAmount +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
