package com.kun.linkage.facade.api.bean.req;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.facade.api.bean.MessageNoticeBaseBean;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

public class SenSmsReq extends MessageNoticeBaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机区号 必填
     */
    @Schema(description = "手机区号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String phoneArea;

    /**
     * 机构号
     */
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 手机号 必填
     */
    @Schema(description = "手机号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String phone;

    /**
     * 请求参数 key;V1,V2,V3
     */
    @Schema(description = "请求参数 key;V1,V2,V3")
    Map<String,String> params;

    public String getPhoneArea() {
        return phoneArea;
    }

    public void setPhoneArea(String phoneArea) {
        this.phoneArea = phoneArea;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }
}
