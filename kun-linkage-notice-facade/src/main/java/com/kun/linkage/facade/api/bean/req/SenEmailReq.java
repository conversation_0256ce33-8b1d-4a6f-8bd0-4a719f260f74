package com.kun.linkage.facade.api.bean.req;

import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.facade.api.bean.MessageNoticeBaseBean;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

public class SenEmailReq extends MessageNoticeBaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构号
     */
    @Schema(description = "机构号")
    private String organizationNo;

    /**
     * 收件人邮箱
     */
    @Schema(description = "收件人邮箱")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String recipientEmail;

    /**
     * 抄送邮箱
     */
    private String emailCc;

    /**
     * 请求参数 key;V1,V2,V3
     */
    @Schema(description = "请求参数 key;V1,V2,V3")
    private Map<String,String> params;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getRecipientEmail() {
        return recipientEmail;
    }

    public void setRecipientEmail(String recipientEmail) {
        this.recipientEmail = recipientEmail;
    }

    public String getEmailCc() {
        return emailCc;
    }

    public void setEmailCc(String emailCc) {
        this.emailCc = emailCc;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }
}
