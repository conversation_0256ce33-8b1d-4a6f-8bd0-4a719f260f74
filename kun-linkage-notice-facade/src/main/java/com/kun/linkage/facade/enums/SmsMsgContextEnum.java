package com.kun.linkage.facade.enums;

public enum SmsMsgContextEnum {

    V1("V1", "上下文头", "Context header"),
    V2("V2", "一次性密码", "OTP"),
    V3("V3", "交易类型", "TransType"),
    V4("V4", "交易金额", "TransAmt"),
    V5("V5", "交易货币", "TransCcy"),
    V6("V6", "账户金额", "AccAmt"),
    V7("V7", "账户货币", "AccCcy"),
    V8("V8", "联系信息", "ContactInfo"),
    V9("V9", "卡号（后4位）", "CardNumber(last 4)"),
    V10("V10", "商户名称", "TransMerchant"),
    V11("V11", "事件时间", "eventTime"),
    V12("V12", "交易参考", "TransReference"),
    V13("V13", "账户余额阈值", "AccBalanceThreshold"),
    V14("V14", "账户余额货币", "AccBalanceCcy");

    private final String key;
    private final String chineseDescription;
    private final String englishDescription;

    SmsMsgContextEnum(String key, String chineseDescription, String englishDescription) {
        this.key = key;
        this.chineseDescription = chineseDescription;
        this.englishDescription = englishDescription;
    }

    public String getKey() {
        return key;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }

    // 根据 key 获取枚举项
    public static SmsMsgContextEnum fromKey(String key) {
        for (SmsMsgContextEnum context : SmsMsgContextEnum.values()) {
            if (context.getKey().equalsIgnoreCase(key)) {
                return context;
            }
        }
        throw new IllegalArgumentException("No enum constant with key " + key);
    }
}
