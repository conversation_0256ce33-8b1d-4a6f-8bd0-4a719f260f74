package com.kun.linkage.notice.controller.api;


import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.facade.api.bean.req.SenEmailReq;
import com.kun.linkage.facade.api.bean.req.SenSmsReq;
import com.kun.linkage.notice.service.impl.MessageSendServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/send")
public class MessageSendController {
    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));

    @Autowired
    private MessageSendServiceImpl noticeService;

    /**
     * 发送短信
     * @param senSmsReq 请求入参
     * @return 成功或者失败，看具体响应码
     */
    @PostMapping("/sendSms")
    public Result<Void> sendSms(@RequestBody @Validated SenSmsReq senSmsReq) {
        log.info("[发送短信]入参:{}", JSON.toJSONString(senSmsReq));
        try {
            return noticeService.sendSms(senSmsReq);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * 发送邮件
     * @param senEmailReq 发送邮件入参
     * @return 成功或者失败，看具体响应码
     */
    @PostMapping("/sendEmail")
    public Result<Void> sendEmail(@RequestBody @Validated SenEmailReq senEmailReq) {
        log.info("[发送邮件]入参:{}", JSON.toJSONString(senEmailReq));
        try {
            return noticeService.sendEmail(senEmailReq);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }
}
