package com.kun.linkage.notice.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kun.linkage.common.db.entity.MessageSendRecord;
import com.kun.linkage.common.db.mapper.MessageSendRecordMapper;
import com.kun.linkage.facade.api.bean.MessageNoticeBaseBean;
import com.kun.linkage.notice.service.MessageSendRecordService;
import org.springframework.stereotype.Service;

@Service
public class MessageSendRecordServiceImpl extends ServiceImpl<MessageSendRecordMapper, MessageSendRecord> implements MessageSendRecordService {


    @Override
    public boolean addMessage(MessageNoticeBaseBean messageNoticeBaseBean) {
        MessageSendRecord record = new MessageSendRecord();
        return false;
    }
}
