package com.kun.linkage.notice.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.alibaba.fastjson.JSON;
import com.kun.common.enums.MailType;
import com.kun.common.util.mail.MailUtils;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.db.entity.EmailTemplate;
import com.kun.linkage.common.db.entity.MessageSendRecord;
import com.kun.linkage.common.db.entity.SmsTemplate;
import com.kun.linkage.common.db.mapper.MessageSendRecordMapper;
import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import com.kun.linkage.facade.api.bean.req.SenEmailReq;
import com.kun.linkage.facade.api.bean.req.SenSmsReq;
import com.kun.linkage.facade.constants.NoticeTipConstant;
import com.kun.linkage.facade.constants.RedisKeyConstant;
import com.kun.linkage.facade.enums.MailPoolEnum;
import com.kun.linkage.facade.enums.MessageTypeEnum;
import com.kun.linkage.facade.enums.SendStatusEnum;
import com.kun.linkage.facade.enums.SystemEnum;
import com.kun.linkage.notice.config.MailPoolUPlusConfig;
import com.kun.linkage.notice.service.EmailTemplateService;
import com.kun.linkage.notice.service.SmsTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class MessageSendServiceImpl {

    private static final Logger log = LoggerFactory.getLogger(MessageSendServiceImpl.class);

    @Resource
    private SmsTemplateService smsTemplateService;
    @Resource
    private EmailTemplateService emailTemplateService;
    @Resource
    private MessageSendRecordMapper messageSendRecordMapper;
    @Resource
    private MailUtils mailUtils;
    @Resource
    private AliSmsSendServiceImpl aliSmsSendServiceImpl;
    @Resource
    private MailPoolUPlusConfig mailPoolUPlusConfig;
    @Resource
    private RedissonCacheUtil redissonCacheUtil;


    /**
     * 发送短信请求
     * @param senSmsReq 发送短信
     * @return 成功失败
     */
    public Result<Void> sendSms(SenSmsReq senSmsReq) {
        String templateNo = senSmsReq.getTemplateNo();
        String templateLanguage = senSmsReq.getTemplateLanguage();
        Map<String, String> params = senSmsReq.getParams();
        String phoneArea = senSmsReq.getPhoneArea();
        if(phoneArea.contains("+")){
            phoneArea = phoneArea.replace("+","");
        }
        String phone = senSmsReq.getPhone();
        phone = phoneArea + phone;

        SmsTemplate smsTemplate = smsTemplateService.selectByTemplateId(templateNo,templateLanguage);
        if (smsTemplate == null) {
            log.error("SMS template not exists, templateNo:{}", templateNo);
            return Result.fail(NoticeTipConstant.TEMPLATE_DATA_NOT_FOUND);
        }
        //区分模板短信以及非模板短信，如果是模板短信使用模板号发送
        String templateContent = smsTemplate.getTemplateContent();
        String templateContentKey = smsTemplate.getTemplateContentKey();


        Boolean b = cheekTemplateContentKey(templateContentKey, params);
        if(!b){
            log.error("SMS template content not exists, templateNo:{}", templateNo);
            return Result.fail(CommonTipConstant.REQUEST_PARAM_ERROR);
        }

        //发送非模板短信的才需要填充模板,发送模板短信的直接发对应的模板号以及模板内容
        if(YesFlagEnum.NO.getNumValue().equals(smsTemplate.getTemplateSmsFlag())){
            templateContent =  fillTemplate(templateContent,params);
        }

        Boolean sendSmsB = this.sendSms(phone, templateContent, templateNo,senSmsReq,smsTemplate.getTemplateSmsFlag());
        if(!sendSmsB){
            return Result.fail(NoticeTipConstant.SMS_SEND_FAILURE);
        }
        //将剩余发送次数保存
        return Result.success();
    }

    /**
     * 邮件发送
     * @param senEmailReq 请求参数
     * @return 成功失败
     */
    public Result<Void> sendEmail(SenEmailReq senEmailReq) {
        log.info("[发送邮件池]入参:{}", JSON.toJSONString(senEmailReq));
        String templateNo = senEmailReq.getTemplateNo();
        String templateLanguage = senEmailReq.getTemplateLanguage();

        Map<String, String> params = senEmailReq.getParams();

        EmailTemplate emailTemplate = emailTemplateService.selectByTemplateId(templateNo,templateLanguage);
        if (emailTemplate == null) {
            log.error("Email template not exists, templateNo:{}", templateNo);
            return Result.fail(NoticeTipConstant.TEMPLATE_DATA_NOT_FOUND);
        }

        String templateContent = emailTemplate.getTemplateContent();
        String templateContentKey = emailTemplate.getTemplateContentKey();
        String emailTitle = emailTemplate.getEmailTitle();

        Boolean b = cheekTemplateContentKey(templateContentKey, params);
        if(!b){
            log.error("Email template content not exists, templateNo:{}", templateNo);
            return Result.fail(CommonTipConstant.REQUEST_PARAM_ERROR);
        }
        //填充模板内容
        templateContent =  fillTemplate(templateContent,params);
        String sourceFrom = senEmailReq.getSourceFrom();
        Boolean sendResult = false;
        if(SystemEnum.VCC.getValue().equalsIgnoreCase(sourceFrom)){
            //短信邮件
            sendResult = sendEmail(templateContent, emailTitle, senEmailReq);
        }else {
            //U+的邮件发送
            sendResult = sendEmailPool(templateContent, emailTitle, senEmailReq);
        }

        if(!sendResult){
            return Result.fail(NoticeTipConstant.EMAIL_SEND_FAILURE);
        }
        return Result.success();
    }

    /**
     * 邮件池发送
     * @return 成功失败
     */
    public Boolean sendEmailPool(String templateContent,String emailTitle,SenEmailReq senEmailReq) {
        String today = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        //获取可用的邮件账号
        MailAccount mailAccount = getAvailableAccount(today);
        if(null == mailAccount){
            log.error("邮件已经超过当日最大限度");
            return false;
        }

        //邮件发送
        List<String> recipientEmailList = Arrays.asList(senEmailReq.getRecipientEmail().split(","));
        List<String> ccEmailList = new ArrayList<>();
        if(StringUtils.isNotBlank(senEmailReq.getEmailCc())){
            ccEmailList = Arrays.asList(senEmailReq.getEmailCc().split(","));
        }

        String messageId = MailUtil.send(mailAccount, recipientEmailList, ccEmailList, null, emailTitle, templateContent, true);

        //邮件发送完成之后需要+1
        String redisKey = RedisKeyConstant.KL_NOTICE_SEND_SMS_LIMIT+ today;
        String user = mailAccount.getUser();
        redisKey = redisKey + user;
        //更新发送次数
        redissonCacheUtil.atomicIncrement(redisKey);

        //判断是否成功
        Boolean sendResult = true;
        String sendStatus  = SendStatusEnum.SUCCESS.getCode();
        if(StringUtils.isBlank(messageId)){
            log.error("Email send failure, templateNo:{}", senEmailReq.getTemplateNo());
            sendResult = false;
            sendStatus  = SendStatusEnum.FAILED.getCode();
        }


        //添加发送请求记录
        Date date = new Date();
        MessageSendRecord messageSendRecord = new MessageSendRecord();
        messageSendRecord.setSourceFrom(senEmailReq.getSourceFrom());
        messageSendRecord.setOrganizationNo(senEmailReq.getOrganizationNo());
        messageSendRecord.setMessageType(MessageTypeEnum.EMAIL.getCode());
        messageSendRecord.setRecipient(senEmailReq.getRecipientEmail());
        messageSendRecord.setTemplateNo(senEmailReq.getTemplateNo());
        if(null != senEmailReq.getParams()){
            messageSendRecord.setReqParam(senEmailReq.getParams().toString());
        }
        messageSendRecord.setSendStatus(sendStatus);
        messageSendRecord.setSendTime(date);
        messageSendRecord.setUpdateTime(date);
        messageSendRecordMapper.insert(messageSendRecord);

        return sendResult;
    }


    /**
     * 获取可用的邮件账号
     */
    private MailAccount getAvailableAccount(String today) {

        //获取账号
        List<MailAccount> emailAccounts = mailPoolUPlusConfig.getEmailAccounts();
        String initRedisKey = RedisKeyConstant.KL_NOTICE_SEND_SMS_LIMIT+ today;
        String accountTotalRedisKey = RedisKeyConstant.KL_NOTICE_SEND_SMS_NUMBER_OF_ACCOUNT_CHANGES+ today;

        //判断邮件池已经用完
        if(redissonCacheUtil.exists(accountTotalRedisKey)){
         Long total = redissonCacheUtil.getAtomicLong(accountTotalRedisKey);
            if(total > emailAccounts.size()){
                //可能存在添加账号的情况,添加账号，并且没有使用;账号使用-1
                MailAccount mailAccount = emailAccounts.get(emailAccounts.size() - 1);
                String redisKey = initRedisKey + mailAccount.getUser();
                if(!redissonCacheUtil.exists(redisKey)){
                    redissonCacheUtil.atomicDecrement(accountTotalRedisKey);
                }else {
                    log.error("所有账号均达上限，总账号数:{}，配置数:{}", total, emailAccounts.size());
                    return null;
                }

            }
        }
        String lastAccount = null;
        Integer count = 0;
        for (MailAccount mailAccount : emailAccounts) {
            //拿到账号判断这个账号是否已经大于450;如果是的话需要切换到下一个账号
            count = count +1;
            String user = mailAccount.getUser();
            String redisKey = initRedisKey + user;
            if(!redissonCacheUtil.exists(redisKey)){
                //初始化自增+1
                redissonCacheUtil.atomicSet(redisKey,1,1L, TimeUnit.DAYS);
                //如果不存在，或小于count 开始加一
                if(!redissonCacheUtil.exists(accountTotalRedisKey) || redissonCacheUtil.getAtomicLong(accountTotalRedisKey) < count){
                    redissonCacheUtil.atomicSet(redisKey,1,1L, TimeUnit.DAYS);
                }
                log.info("启用新账号:{}，当前总账号数:{}", user, redissonCacheUtil.getAtomicLong(accountTotalRedisKey));
                return mailAccount;
            }
            //有值，如果小于限制的条数拿到账号退出,大于继续下一轮
            Long total = redissonCacheUtil.getAtomicLong(redisKey);
            if(total < mailPoolUPlusConfig.getSendLimit()){
                log.info("复用账号:{}，发送量:{}", user, total);
                return mailAccount;
            }

            lastAccount = user;
        }

        log.error("邮件池已耗尽，最后检查账号:{}，总配置数:{}", lastAccount, emailAccounts.size());
        // 所有账号耗尽，更新总账号数为配置数（便于后续快速判断）
        redissonCacheUtil.atomicIncrement(accountTotalRedisKey);
        return null;

    }

    public static void main(String[] args) {

    }

    /**
     * todo 没有接入短信
     * 发送短信模板
     * @param phone 手机号
     * @param templateContent 短信内容
     * @param templateNo 模板编号
     * @param templateSmsFlag 是否是模板短信;1:模板短信;0:非模板短信
     * @return 成功或者失败的短信模板数据
     */
    private Boolean sendSms(String phone,String templateContent,String templateNo,SenSmsReq senSmsReq,Integer templateSmsFlag){
        Boolean sendSmsResult = false;
        if(YesFlagEnum.NO.getNumValue().equals(templateSmsFlag)){
            sendSmsResult = aliSmsSendServiceImpl.sendMessageToGlobe(phone,null,templateContent);
        }else {
            Map<String, String> params = senSmsReq.getParams();
            sendSmsResult = aliSmsSendServiceImpl.sendMessageWithTemplate(phone,null,templateNo, JSON.toJSONString(params));
        }

        String sendStatus  = SendStatusEnum.FAILED.getCode();

        if(sendSmsResult){

            sendStatus = SendStatusEnum.SUCCESS.getCode();
        }

        //添加发送请求记录
        Date date = new Date();
        MessageSendRecord messageSendRecord = new MessageSendRecord();
        messageSendRecord.setSourceFrom(senSmsReq.getSourceFrom());
        messageSendRecord.setOrganizationNo(senSmsReq.getOrganizationNo());
        messageSendRecord.setMessageType(MessageTypeEnum.SMS.getCode());
        messageSendRecord.setRecipient(phone);
        messageSendRecord.setTemplateNo(senSmsReq.getTemplateNo());
        messageSendRecord.setReqParam(senSmsReq.getParams().toString());
        messageSendRecord.setSendStatus(sendStatus);
        messageSendRecord.setSendTime(date);
        messageSendRecord.setUpdateTime(date);
        messageSendRecordMapper.insert(messageSendRecord);

        return sendSmsResult;
    }

    /**
     * 邮件发送
     * @param templateContent 邮件发送内容
     * @param senEmailReq 邮件请求数据
     * @return
     */
    private Boolean sendEmail(String templateContent,String emailTitle,SenEmailReq senEmailReq) {

        String recipientEmail = senEmailReq.getRecipientEmail();
        //todo 没有实现cc方法
        String emailCc = senEmailReq.getEmailCc();

        boolean result = mailUtils.send(Collections.singletonList(recipientEmail),
                emailTitle, templateContent, MailType.NOTIFY);


        String sendStatus  = SendStatusEnum.FAILED.getCode();
        if(result){
            sendStatus = SendStatusEnum.SUCCESS.getCode();
        }
        //添加发送请求记录
        Date date = new Date();
        MessageSendRecord messageSendRecord = new MessageSendRecord();
        messageSendRecord.setSourceFrom(senEmailReq.getSourceFrom());
        messageSendRecord.setOrganizationNo(senEmailReq.getOrganizationNo());
        messageSendRecord.setMessageType(MessageTypeEnum.EMAIL.getCode());
        messageSendRecord.setRecipient(recipientEmail);
        messageSendRecord.setTemplateNo(senEmailReq.getTemplateNo());
        messageSendRecord.setReqParam(senEmailReq.getParams().toString());
        messageSendRecord.setSendStatus(sendStatus);
        messageSendRecord.setSendTime(date);
        messageSendRecord.setUpdateTime(date);
        messageSendRecordMapper.insert(messageSendRecord);

        return result;
    }



    /**
     * 检查templateContentKey 与 params 是否一一对应
     * @param templateContentKey 模板内容中的key
     * @param params 请求参数
     * @return true,false
     */
    private static Boolean cheekTemplateContentKey(String templateContentKey, Map<String, String> params){

        if((null == params || params.isEmpty()) && StringUtils.isBlank(templateContentKey) ){
            return true;
        }

        // 处理其中一个为空的情况
        if (StringUtils.isBlank(templateContentKey) || (params == null || params.isEmpty())) {
            log.error("校验模板内容，请求参数与模板参数不一致; templateContentKey: {}, params: {}", templateContentKey, params);
            return false;
        }

        // 将templateContentKey按逗号分隔并去除前后空格，生成列表
        List<String> contentKeyList = Arrays.stream(templateContentKey.split(","))
                .map(String::trim) // 去除每个元素的前后空格
                .collect(Collectors.toList());

        //校验大小是否一致
        if(contentKeyList.size() != params.size()){
            log.error("校验模板内容，请求参数与模板参数不一致;templateContentKey:{},params:{}", templateContentKey, params);
            return false;
        }

        // 校验 templateContentKey 和 params 中的键是否一一对应
        for (String contentKey : contentKeyList) {
            if (!params.containsKey(contentKey)) {
                log.error("校验模板内容，请求参数与模板参数不一致;找不到对应的键: {}", contentKey);
                return false;
            }
        }

        return true;
    }


    /**
     * 填充模板
     * @param templateContent 模板内容
     * @param params 模板中的参数
     * @return 填充后的模板数据
     */
    private static String fillTemplate(String templateContent, Map<String, String> params) {
        for (Map.Entry<String, String> entry : params.entrySet()) {
            // 用Map中的值替换模板字符串中的[键]
            templateContent = templateContent.replace("[" + entry.getKey() + "]", entry.getValue());
        }
        return templateContent;
    }

}
