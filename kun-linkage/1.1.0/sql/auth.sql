-- 该脚本用于创建KL外部Webhook日志表，支持分区存储
-- 该表用于记录外部Webhook请求和响应的日志信息，便于后续的审计和问题排查
-- 分区策略为按月分区，分区范围从2025年5月开始，直到2030年1月
-- 每个分区包含一个月的数据，分区字段为create_date
-- 分区的创建时间和更新时间均为当前时间戳
-- 主键为id和create_date的复合主键
-- 每个分区的最大值为TO_DAYS('2031-01-01')，用于存储超出2030年的数据
-- 分区的创建时间和更新时间均为当前时间戳
-- 每个分区的最大值为TO_DAYS('2031-01-01')，用于存储超出2030年的数据
drop table if exists kl_external_webhook_log;
create table kl_external_webhook_log
(
    id          varchar(32)    not null comment '主键ID',
    request_id  varchar(64)    null comment '请求ID',
    webhook_url varchar(256)   not null comment 'Webhook URL',
    trace_id    varchar(64)    not null comment '追踪ID',
    span_id     varchar(64)    not null comment 'Span ID',
    method      varchar(16)    not null comment 'HTTP方法',
    request     longtext                default null comment '请求内容',
    response    longtext                default null comment '响应内容',
    http_status tinyint(3)              default null comment 'HTTP状态码',
    error_msg   varchar(512)   null comment '错误信息',
    cost        decimal(10, 3) null comment '请求耗时（毫秒）',
    status      varchar(32)    null default null comment '状态，S-成功，F-失败, T-超时, UE-未知错误',
    create_date datetime                default current_timestamp comment '创建时间',
    update_date datetime                default current_timestamp on update current_timestamp comment '更新时间',
    PRIMARY KEY (id, create_date)  -- 复合主键包含分区字段
) comment 'KL外部Webhook日志表'
    PARTITION BY RANGE (TO_DAYS(create_date)) (
        -- 2025年分区 (从5月开始)
        PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
        PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
        PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
        PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
        PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
        PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
        PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
        PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),

        -- 2026年分区
        PARTITION p202601 VALUES LESS THAN (TO_DAYS('2026-02-01')),
        PARTITION p202602 VALUES LESS THAN (TO_DAYS('2026-03-01')),
        PARTITION p202603 VALUES LESS THAN (TO_DAYS('2026-04-01')),
        PARTITION p202604 VALUES LESS THAN (TO_DAYS('2026-05-01')),
        PARTITION p202605 VALUES LESS THAN (TO_DAYS('2026-06-01')),
        PARTITION p202606 VALUES LESS THAN (TO_DAYS('2026-07-01')),
        PARTITION p202607 VALUES LESS THAN (TO_DAYS('2026-08-01')),
        PARTITION p202608 VALUES LESS THAN (TO_DAYS('2026-09-01')),
        PARTITION p202609 VALUES LESS THAN (TO_DAYS('2026-10-01')),
        PARTITION p202610 VALUES LESS THAN (TO_DAYS('2026-11-01')),
        PARTITION p202611 VALUES LESS THAN (TO_DAYS('2026-12-01')),
        PARTITION p202612 VALUES LESS THAN (TO_DAYS('2027-01-01')),

        -- 2027年分区
        PARTITION p202701 VALUES LESS THAN (TO_DAYS('2027-02-01')),
        PARTITION p202702 VALUES LESS THAN (TO_DAYS('2027-03-01')),
        PARTITION p202703 VALUES LESS THAN (TO_DAYS('2027-04-01')),
        PARTITION p202704 VALUES LESS THAN (TO_DAYS('2027-05-01')),
        PARTITION p202705 VALUES LESS THAN (TO_DAYS('2027-06-01')),
        PARTITION p202706 VALUES LESS THAN (TO_DAYS('2027-07-01')),
        PARTITION p202707 VALUES LESS THAN (TO_DAYS('2027-08-01')),
        PARTITION p202708 VALUES LESS THAN (TO_DAYS('2027-09-01')),
        PARTITION p202709 VALUES LESS THAN (TO_DAYS('2027-10-01')),
        PARTITION p202710 VALUES LESS THAN (TO_DAYS('2027-11-01')),
        PARTITION p202711 VALUES LESS THAN (TO_DAYS('2027-12-01')),
        PARTITION p202712 VALUES LESS THAN (TO_DAYS('2028-01-01')),

        -- 2028年分区
        PARTITION p202801 VALUES LESS THAN (TO_DAYS('2028-02-01')),
        PARTITION p202802 VALUES LESS THAN (TO_DAYS('2028-03-01')),
        PARTITION p202803 VALUES LESS THAN (TO_DAYS('2028-04-01')),
        PARTITION p202804 VALUES LESS THAN (TO_DAYS('2028-05-01')),
        PARTITION p202805 VALUES LESS THAN (TO_DAYS('2028-06-01')),
        PARTITION p202806 VALUES LESS THAN (TO_DAYS('2028-07-01')),
        PARTITION p202807 VALUES LESS THAN (TO_DAYS('2028-08-01')),
        PARTITION p202808 VALUES LESS THAN (TO_DAYS('2028-09-01')),
        PARTITION p202809 VALUES LESS THAN (TO_DAYS('2028-10-01')),
        PARTITION p202810 VALUES LESS THAN (TO_DAYS('2028-11-01')),
        PARTITION p202811 VALUES LESS THAN (TO_DAYS('2028-12-01')),
        PARTITION p202812 VALUES LESS THAN (TO_DAYS('2029-01-01')),

        -- 2029年分区
        PARTITION p202901 VALUES LESS THAN (TO_DAYS('2029-02-01')),
        PARTITION p202902 VALUES LESS THAN (TO_DAYS('2029-03-01')),
        PARTITION p202903 VALUES LESS THAN (TO_DAYS('2029-04-01')),
        PARTITION p202904 VALUES LESS THAN (TO_DAYS('2029-05-01')),
        PARTITION p202905 VALUES LESS THAN (TO_DAYS('2029-06-01')),
        PARTITION p202906 VALUES LESS THAN (TO_DAYS('2029-07-01')),
        PARTITION p202907 VALUES LESS THAN (TO_DAYS('2029-08-01')),
        PARTITION p202908 VALUES LESS THAN (TO_DAYS('2029-09-01')),
        PARTITION p202909 VALUES LESS THAN (TO_DAYS('2029-10-01')),
        PARTITION p202910 VALUES LESS THAN (TO_DAYS('2029-11-01')),
        PARTITION p202911 VALUES LESS THAN (TO_DAYS('2029-12-01')),
        PARTITION p202912 VALUES LESS THAN (TO_DAYS('2030-01-01')),

        -- 2030年分区
        PARTITION p203001 VALUES LESS THAN (TO_DAYS('2030-02-01')),
        PARTITION p203002 VALUES LESS THAN (TO_DAYS('2030-03-01')),
        PARTITION p203003 VALUES LESS THAN (TO_DAYS('2030-04-01')),
        PARTITION p203004 VALUES LESS THAN (TO_DAYS('2030-05-01')),
        PARTITION p203005 VALUES LESS THAN (TO_DAYS('2030-06-01')),
        PARTITION p203006 VALUES LESS THAN (TO_DAYS('2030-07-01')),
        PARTITION p203007 VALUES LESS THAN (TO_DAYS('2030-08-01')),
        PARTITION p203008 VALUES LESS THAN (TO_DAYS('2030-09-01')),
        PARTITION p203009 VALUES LESS THAN (TO_DAYS('2030-10-01')),
        PARTITION p203010 VALUES LESS THAN (TO_DAYS('2030-11-01')),
        PARTITION p203011 VALUES LESS THAN (TO_DAYS('2030-12-01')),
        PARTITION p203012 VALUES LESS THAN (TO_DAYS('2031-01-01')),

        -- 默认分区，用于存储超出2030年的数据
        PARTITION p_default VALUES LESS THAN MAXVALUE
        );


create table kl_organization_trans_accounting_202507
(
    id                        varchar(32)                                not null comment '主键ID'
        primary key,
    organization_no           varchar(16)                                null comment '机构号',
    customer_id               varchar(64)                                null comment '客户号',
    request_no                varchar(64)                                null comment '记账请求流水号',
    card_id                   varchar(64)                                null comment '卡id',
    trans_time                datetime                                   null comment '交易时间',
    trans_amount              decimal(20, 3)  default 0.000              null comment '交易金额(卡片币种)',
    trans_currency_code       varchar(3)                                 null comment '交易币种(卡片币种)',
    trans_currency_precision  int             default 2                  null comment '交易币种精度(卡片币种)',
    trans_id                  varchar(64)                                null comment '交易流水号',
    fx_rate                   decimal(10, 5)  default 1.00000            null comment '换汇汇率',
    deduct_currency_code      varchar(16)                                null comment '扣除币种',
    deduct_processor          varchar(16)                                null comment '扣除处理方(KUN和PAYX)',
    deduct_currency_precision int                                        null comment '扣除币种精度',
    deduct_amount             decimal(30, 16) default 0.0000000000000000 null comment '扣除本金金额',
    deduct_total_amount       decimal(30, 16) default 0.0000000000000000 null comment '扣除总金额',
    bookkeep_status           varchar(16)                                null comment '记账状态:SUCCESS,FAIL,PENDING',
    fail_message              varchar(128)                               null comment '失败信息',
    bookkeep_reversal_count   int             default 0                  null comment '记账冲正次数',
    bookkeep_reversal_status  varchar(16)                                null comment '记账冲正状态:SUCCESS,FAIL,PENDING',
    create_time               datetime                                   null comment '创建时间',
    last_modify_time          datetime                                   null comment '最后一次修改时间',
    key idx_organization_no(organization_no),
    key idx_trans_id(trans_id),
    key idx_bookkeep_status(bookkeep_status),
    key idx_bookkeep_reversal_status(bookkeep_reversal_status),
    key idx_create_time(create_time)
)
    comment '商户交易记账明细表';


create table kl_organization_trans_accounting_202508 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202509 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202510 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202511 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202512 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202601 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202602 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202603 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202604 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202605 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202606 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202607 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202608 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202609 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202610 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202611 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202612 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202701 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202702 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202703 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202704 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202705 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202706 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202707 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202708 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202709 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202710 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202711 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202712 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202801 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202802 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202803 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202804 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202805 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202806 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202807 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202808 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202809 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202810 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202811 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202812 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202901 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202902 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202903 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202904 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202905 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202906 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202907 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202908 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202909 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202910 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202911 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_202912 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203001 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203002 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203003 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203004 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203005 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203006 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203007 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203008 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203009 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203010 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203011 like kl_organization_trans_accounting_202507;
create table kl_organization_trans_accounting_203012 like kl_organization_trans_accounting_202507;

-- 调整字段位置
alter table kl_auth_flow_202505 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202506 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202507 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202508 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202509 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202510 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202511 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202512 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202601 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202602 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202603 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202604 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202605 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202606 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202607 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202608 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202609 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202610 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202611 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202612 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202701 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202702 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202703 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202704 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202705 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202706 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202707 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202708 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202709 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202710 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202711 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202712 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202801 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202802 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202803 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202804 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202805 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202806 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202807 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202808 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202809 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202810 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202811 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202812 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202901 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202902 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202903 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202904 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202905 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202906 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202907 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202908 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202909 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202910 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202911 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_202912 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203001 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203002 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203003 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203004 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203005 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203006 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203007 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203008 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203009 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203010 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203011 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;
alter table kl_auth_flow_203012 add third_party_authorization_flag tinyint null comment '第三方授权标记' after release_markup_billing_amount;

-- 处理历史数据
update kl_auth_flow_202505 t1
    join kl_auth_flow_ext_202505 t2 on t2.auth_flow_id = t1.id
    set t1.third_party_authorization_flag = t2.third_party_authorization_flag
where t2.third_party_authorization_flag is not null
  and t1.third_party_authorization_flag is null;
update kl_auth_flow_202506 t1
    join kl_auth_flow_ext_202506 t2 on t2.auth_flow_id = t1.id
    set t1.third_party_authorization_flag = t2.third_party_authorization_flag
where t2.third_party_authorization_flag is not null
  and t1.third_party_authorization_flag is null;
update kl_auth_flow_202507 t1
    join kl_auth_flow_ext_202507 t2 on t2.auth_flow_id = t1.id
    set t1.third_party_authorization_flag = t2.third_party_authorization_flag
where t2.third_party_authorization_flag is not null
  and t1.third_party_authorization_flag is null;
update kl_auth_flow_202508 t1
    join kl_auth_flow_ext_202508 t2 on t2.auth_flow_id = t1.id
    set t1.third_party_authorization_flag = t2.third_party_authorization_flag
where t2.third_party_authorization_flag is not null
  and t1.third_party_authorization_flag is null;