-- 导出文件记录表
CREATE TABLE `kc_export_file_record` (
  `file_record_id` varchar(64) NOT NULL COMMENT '文件记录ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型',
  `file_status` varchar(20) NOT NULL COMMENT '文件状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败',
  `s3_url` varchar(500) DEFAULT NULL COMMENT 'S3文件URL',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `created_by` varchar(64) NULL COMMENT '创建用户ID',
  `created_time` datetime NULL COMMENT '创建时间',
  `updated_time` datetime NULL COMMENT '更新时间',
  `error_message` text COMMENT '失败原因',
  PRIMARY KEY (`file_record_id`),
  KEY `idx_file_type_status` (`file_type`, `file_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导出文件记录表';


INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-export-file-record' as code,
             '导出文件记录查询'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id > 0;

INSERT INTO `vcc_data_dict` (`dict_type`, `dict_type_second`, `dict_value`, `cn_desc`, `en_desc`, `valid_flag`, `dict_index`, `create_time`, `create_user_id`, `update_time`, `update_user_id`)
VALUES ('KC_EXPORT_FILE_TYPE', NULL, 'VISA_GW_SETTLEMENT_DATA', 'VISA-GW清算数据', 'VISA-GW清算数据', 1, 1, now(), NULL, now(), NULL);
