CREATE TABLE `kc_clearing_info_2025_q1` (
                                           `clearing_id` bigint NOT NULL COMMENT '清算主键id',
                                           `clearing_no` varchar(32) DEFAULT NULL COMMENT '清算流水号',
                                           `clearing_file_name` varchar(255) DEFAULT NULL COMMENT '清分文件名称',
                                           `clearing_file_id` bigint DEFAULT NULL COMMENT '关联清算文件主键id',
                                           `channel_source` varchar(16)  DEFAULT NULL COMMENT '通道来源;PBC-GW；',
                                           `system` varchar(32) DEFAULT NULL COMMENT '来源系统:VCC;KL',
                                           `clearing_date` date DEFAULT NULL COMMENT '清算日期',
                                           `auth_id` bigint DEFAULT NULL COMMENT '关联授权流水表的主键id',
                                           `trans_id` varchar(64) DEFAULT NULL COMMENT '授权流水表中的交易id',
                                           `auth_remain_auth_amt` decimal(18,3) DEFAULT NULL COMMENT '授权流水中的剩余可用金额；不更新',
                                           `trans_code` varchar(4)  DEFAULT NULL COMMENT 'base ii文件里的trans_code;05:消费;06:退货；07：取现;25:消费查询;26:退货撤销；27：取现撤销',
                                           `auth_date` varchar(8) DEFAULT NULL COMMENT '授权日期',
                                           `original_clearing_no` varchar(32) DEFAULT NULL COMMENT '原清算流水号',
                                           `customer_mer_id` varchar(64)  DEFAULT NULL COMMENT '商户id',
                                           `card_acceptor_id` varchar(64) DEFAULT NULL COMMENT '商户号',
                                           `card_acceptor_name` varchar(255)  DEFAULT NULL COMMENT '商户名称',
                                           `card_acceptor_country_code` varchar(3)  DEFAULT NULL COMMENT '商户国家代码',
                                           `card_acceptor_tid` varchar(32) DEFAULT NULL COMMENT '终端号',
                                           `transaction_type` varchar(16) DEFAULT NULL COMMENT '交易类型',
                                           `transaction_date` varchar(32)  DEFAULT NULL COMMENT '交易时间;YYYYMMDD',
                                           `transaction_datetime` varchar(32)  DEFAULT NULL COMMENT '交易时间;YYYYMMDD hhmmss',
                                           `transaction_currency_no` varchar(8)  DEFAULT NULL COMMENT '交易币种;三位数字',
                                           `transaction_currency_code` varchar(8) DEFAULT NULL COMMENT '交易币种;三位字母',
                                           `transaction_currency_precision` int DEFAULT NULL COMMENT '交易币种精度',
                                           `clear_amount` decimal(18,3) DEFAULT NULL COMMENT '清分金额',
                                           `reference_no` varchar(128) DEFAULT NULL COMMENT '参考号;F37',
                                           `trace_audit_no` varchar(128) DEFAULT NULL COMMENT '审计追踪:F11',
                                           `processor_card_id` varchar(128)  DEFAULT NULL COMMENT '卡id',
                                           `kcard_id` varchar(64) DEFAULT NULL COMMENT 'kcard系统卡id',
                                           `card_last_four` varchar(4) DEFAULT NULL COMMENT '卡号后4位',
                                           `cardholder_currency_no` varchar(8)  DEFAULT NULL COMMENT '持卡人币种;三位数字',
                                           `cardholder_currency_code` varchar(8) DEFAULT NULL COMMENT '持卡人币种;三位字母',
                                           `cardholder_currency_precision` int DEFAULT NULL COMMENT '持卡人币种精度',
                                           `cardholder_amount` decimal(18,3) DEFAULT NULL COMMENT '持卡人金额',
                                           `acq_arn` varchar(32) DEFAULT NULL COMMENT '收单参考号',
                                           `auth_amount` decimal(18,3) DEFAULT NULL COMMENT '交易金额',
                                           `difference_flag` int DEFAULT NULL COMMENT '差异标记',
                                           `transaction_amount_offset` decimal(18,3) DEFAULT NULL COMMENT '不含markup交易的差异金额',
                                           `markup_rate` decimal(8,4) DEFAULT NULL COMMENT '清算当时markup利率',
                                           `cardholder_markup_amount` decimal(18,3) DEFAULT NULL COMMENT '清算当时markup金额',
                                           `cardholder_billing_amount_with_markup` decimal(18,3) DEFAULT NULL COMMENT '持卡人账单币种金额(含markup)',
                                           `clearing_status` varchar(8) DEFAULT NULL COMMENT '清算状态;成功;失败(只有异常才会有失败)',
                                           `error_flag` int DEFAULT '0' COMMENT '是否异常;0:否；1:是;没有匹配到授权流水;以及arn没有匹配上原清分数据',
                                           `error_reason` varchar(64) DEFAULT NULL COMMENT '差异原因',
                                           `auth_code` varchar(6) DEFAULT NULL COMMENT 'F38:清分文件授权码；',
                                           `pos_entry_mode_tcr0` varchar(32) DEFAULT NULL COMMENT 'PosEntryMode',
                                           `acquiring_Identifier_tcr0` varchar(32)  DEFAULT NULL COMMENT 'AcquiringIdentifier ',
                                           `card_acceptor_mcc` varchar(8) DEFAULT NULL COMMENT 'MerchantCategoryCode',
                                           `cpd` varchar(8) DEFAULT NULL COMMENT 'CentralProcessingDate格式:YYYYMMDD',
                                           `intechange_fee_amt` decimal(18,6) DEFAULT NULL COMMENT 'InterchangeFeeAmount',
                                           `intechange_fee_sign` varchar(4) DEFAULT NULL COMMENT 'InterchangeFeeSign',
                                           `pos_environment_tcr1` varchar(32) DEFAULT NULL COMMENT 'PosEnvironment',
                                           `fx_rate_source_tcr5` varchar(32) DEFAULT NULL COMMENT 'Source Currency to Base Currency Exchange Rate ',
                                           `fx_rate_destination_tcr5` varchar(32) DEFAULT NULL COMMENT 'Base Currency to Destination Currency Exchange Rate ',
                                           `authorization_response_code_tcr5` varchar(32) DEFAULT NULL COMMENT 'AuthorizationResponseCode',
                                           `multiple_clearing_sequence_number_tcr5` varchar(4) DEFAULT NULL COMMENT 'Multiple Clearing Sequence Number',
                                           `multiple_clearing_sequence_count_tcr5` varchar(4) DEFAULT NULL COMMENT 'Multiple Clearing Sequence Count',
                                           `mvv` varchar(10)  DEFAULT NULL COMMENT 'Merchant Verification Value ',
                                           `reversal_flag` int DEFAULT NULL COMMENT '撤销标记;0:未取消;1:已取消;',
                                           `processor_request_id` varchar(64) DEFAULT NULL COMMENT '通道请求流水号',
                                           `original_processor_request_id` varchar(64) DEFAULT NULL COMMENT '原交易通道请求流水号',
                                           `fe_transaction_number` varchar(64) DEFAULT NULL COMMENT 'FE交易号',
                                           `cardholder_billing_amount_offset` decimal(18,3) DEFAULT NULL COMMENT '持卡人账单金额差额',
                                           `cardholder_markup_billing_amount_offset` decimal(18,3) DEFAULT NULL COMMENT '持卡人账单金额差额(含markup)',
                                           `card_schema_product_id` varchar(32) DEFAULT NULL COMMENT '卡组产品ID',
                                           `response_message` varchar(255) DEFAULT NULL COMMENT '响应消息',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `acquiring_id` varchar(8) DEFAULT NULL COMMENT '收单机构ID',
                                           `clearing_type` varchar(16)  DEFAULT NULL COMMENT '清分类型',
                                           `remaining_clear_amount` decimal(18,3) DEFAULT NULL COMMENT '剩余清算金额',
                                           `original_clearing_id` bigint DEFAULT NULL COMMENT '源清算流水数据',
                                           `auth_remain_bill_amt` decimal(18,3) DEFAULT NULL COMMENT '授权流水剩余账单金额 不含markup',
                                           `auth_remain_bill_amt_with_markup` decimal(18,3) DEFAULT NULL COMMENT '授权流水剩余账单总金额（含markup）',
                                           `auth_remain_frozen_amt` decimal(18,3) DEFAULT NULL COMMENT '授权流水剩余冻结金额(含markUp)',
                                           `auth_markup_rate` decimal(18,4) DEFAULT NULL COMMENT '授权流水表中的markup利率',
                                           `notify_flag` int DEFAULT NULL COMMENT '是否需要通知;0,不需要通知;1:已经通知',
                                           `notify_results` int DEFAULT NULL COMMENT '通知结果;1:成功;2失败',
                                           `card_product_code` varchar(64) DEFAULT NULL COMMENT '卡产品编号',
                                           `card_no_last_four` varchar(4) DEFAULT NULL COMMENT '卡号后四位',
                                           `usage_cod` varchar(4) DEFAULT NULL COMMENT 'Usage Code',
                                           `reason_code` varchar(4) DEFAULT NULL COMMENT 'Reason Code',
                                           `merchant_postal_code` varchar(16) DEFAULT NULL COMMENT 'Merchant Postal Code',
                                           `merchant_city` varchar(16) DEFAULT NULL COMMENT 'merchant city',
                                           PRIMARY KEY (`clearing_id`),
                                           KEY `idx_auth_id` (`auth_id`),
                                           KEY `idx_clearing_date` (`clearing_date`)
) COMMENT='通用清分表';


CREATE TABLE IF NOT EXISTS kc_clearing_info_2025_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2025_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2025_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2026_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2026_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2026_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2026_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2027_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2027_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2027_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2027_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2028_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2028_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2028_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2028_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2029_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2029_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2029_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2029_Q4 LIKE kc_clearing_info_2025_q1;


CREATE TABLE IF NOT EXISTS kc_clearing_info_2030_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2030_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2030_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2030_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2031_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2031_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2031_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2031_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2032_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2032_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2032_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2032_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2033_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2033_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2033_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2033_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2034_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2034_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2034_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2034_Q4 LIKE kc_clearing_info_2025_q1;

CREATE TABLE IF NOT EXISTS kc_clearing_info_2035_Q1 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2035_Q2 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2035_Q3 LIKE kc_clearing_info_2025_q1;
CREATE TABLE IF NOT EXISTS kc_clearing_info_2035_Q4 LIKE kc_clearing_info_2025_q1;




CREATE TABLE `kc_visa_base_05_data` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                        `file_name` varchar(255) DEFAULT NULL COMMENT '清分文件',
                                        `transaction_code` varchar(4) DEFAULT NULL COMMENT '交易代码',
                                        `transaction_code_qualifier_0` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（0组）',
                                        `transaction_component_sequence_number_0` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（0组）',
                                        `account_number_0` varchar(128) DEFAULT NULL COMMENT '账号（0组）',
                                        `account_number_extension_0` varchar(8) DEFAULT NULL COMMENT '账号扩展信息（0组）',
                                        `floor_limit_indicator_0` varchar(4) DEFAULT NULL COMMENT '最低限额指示器（0组）',
                                        `crb_or_exception_file_indicator_0` varchar(4) DEFAULT NULL COMMENT 'CRB或异常文件指示器（0组）',
                                        `reserved1_0` varchar(4) DEFAULT NULL COMMENT '预留字段1（0组）',
                                        `acquirer_reference_number_0` varchar(32) DEFAULT NULL COMMENT '收单机构参考号（0组）',
                                        `acquirer_business_id_0` varchar(16) DEFAULT NULL COMMENT '收单机构业务ID（0组）',
                                        `purchase_date_0` varchar(8) DEFAULT NULL COMMENT '购买日期（0组，格式：YYYYMMDD）',
                                        `destination_amount_0` varchar(16) DEFAULT NULL COMMENT '目标金额（0组）',
                                        `destination_currency_code_0` varchar(4) DEFAULT NULL COMMENT '目标币种代码（0组，遵循ISO 4217）',
                                        `source_amount_0` varchar(16) DEFAULT NULL COMMENT '源金额（0组）',
                                        `source_currency_code_0` varchar(4) DEFAULT NULL COMMENT '源币种代码（0组，遵循ISO 4217）',
                                        `merchant_name_0` varchar(32) DEFAULT NULL COMMENT '商户名称（0组）',
                                        `merchant_city_0` varchar(16) DEFAULT NULL COMMENT '商户所在城市（0组）',
                                        `merchant_country_code_0` varchar(4) DEFAULT NULL COMMENT '商户所在国家代码（0组，遵循ISO 3166）',
                                        `merchant_category_code_0` varchar(4) DEFAULT NULL COMMENT '商户类别代码（0组，MCC码）',
                                        `merchant_zip_code_0` varchar(8) DEFAULT NULL COMMENT '商户邮政编码（0组）',
                                        `merchant_state_or_province_code_0` varchar(4) DEFAULT NULL COMMENT '商户所在州/省代码（0组）',
                                        `requested_payment_service_0` varchar(4) DEFAULT NULL COMMENT '请求的支付服务（0组）',
                                        `number_of_payment_forms_0` varchar(4) DEFAULT NULL COMMENT '支付形式数量（0组）',
                                        `usage_code_0` varchar(4) DEFAULT NULL COMMENT '使用代码（0组）',
                                        `reason_code_0` varchar(4) DEFAULT NULL COMMENT '原因代码（0组）',
                                        `settlement_flag_0` varchar(4) DEFAULT NULL COMMENT '清算标志（0组）',
                                        `authorization_characteristics_indicator_0` varchar(4) DEFAULT NULL COMMENT '授权特征指示器（0组）',
                                        `authorization_code_0` varchar(8) DEFAULT NULL COMMENT '授权码（0组）',
                                        `pos_terminal_capability_0` varchar(4) DEFAULT NULL COMMENT 'POS终端能力（0组）',
                                        `reserved2_0` varchar(4) DEFAULT NULL COMMENT '预留字段2（0组）',
                                        `cardholder_id_method_0` varchar(4) DEFAULT NULL COMMENT '持卡人身份验证方式（0组）',
                                        `collection_only_flag_0` varchar(4) DEFAULT NULL COMMENT '仅收款标志（0组）',
                                        `pos_entry_mode_0` varchar(4) DEFAULT NULL COMMENT 'POS录入模式（0组）',
                                        `central_processing_date_0` varchar(4) DEFAULT NULL COMMENT '中心处理日期（0组，格式：MMDD）',
                                        `reimbursement_attribute_0` varchar(4) DEFAULT NULL COMMENT '报销属性（0组）',
                                        `transaction_code_qualifier_1` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（1组）',
                                        `transaction_component_sequence_number_1` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（1组）',
                                        `business_format_code_1` varchar(4) DEFAULT NULL COMMENT '业务格式代码（1组）',
                                        `token_assurance_method_1` varchar(4) DEFAULT NULL COMMENT '令牌验证方式（1组）',
                                        `rate_table_id_1` varchar(8) DEFAULT NULL COMMENT '费率表ID（1组）',
                                        `reserved1_1` varchar(8) DEFAULT NULL COMMENT '预留字段1（1组）',
                                        `reserved2_1` varchar(8) DEFAULT NULL COMMENT '预留字段2（1组）',
                                        `documentation_indicator_1` varchar(4) DEFAULT NULL COMMENT '凭证指示器（1组）',
                                        `member_message_text_1` varchar(64) DEFAULT NULL COMMENT '会员消息文本（1组）',
                                        `special_condition_indicators_1` varchar(4) DEFAULT NULL COMMENT '特殊条件指示器（1组）',
                                        `fee_program_indicator_1` varchar(4) DEFAULT NULL COMMENT '费用计划指示器（1组）',
                                        `issuer_charge_1` varchar(4) DEFAULT NULL COMMENT '发卡行费用（1组）',
                                        `persistent_fx_applied_indicator_1` varchar(4) DEFAULT NULL COMMENT '持久汇率应用指示器（1组）',
                                        `card_acceptor_id_1` varchar(16) DEFAULT NULL COMMENT '收单机构ID（1组）',
                                        `terminal_id_1` varchar(16) DEFAULT NULL COMMENT '终端ID（1组）',
                                        `national_reimbursement_fee_1` varchar(16) DEFAULT NULL COMMENT '国家报销费用（1组）',
                                        `mail_or_phone_or_electronic_commerce_and_payment_indicator_1` varchar(4) DEFAULT NULL COMMENT '邮件/电话/电子商务支付指示器（1组）',
                                        `special_chargeback_indicator_1` varchar(4) DEFAULT NULL COMMENT '特殊退单指示器（1组）',
                                        `conversion_date_1` varchar(4) DEFAULT NULL COMMENT '转换日期（1组，格式：MMDD）',
                                        `additional_token_response_information_1` varchar(4) DEFAULT NULL COMMENT '附加令牌响应信息（1组）',
                                        `reserved3_1` varchar(4) DEFAULT NULL COMMENT '预留字段3（1组）',
                                        `acceptance_terminal_indicator_1` varchar(4) DEFAULT NULL COMMENT '受理终端指示器（1组）',
                                        `prepaid_card_indicator_1` varchar(4) DEFAULT NULL COMMENT '预付卡指示器（1组）',
                                        `service_development_field_1` varchar(4) DEFAULT NULL COMMENT '服务开发字段（1组）',
                                        `avs_response_code_1` varchar(4) DEFAULT NULL COMMENT '地址验证服务响应码（1组）',
                                        `authorization_source_code_1` varchar(4) DEFAULT NULL COMMENT '授权来源代码（1组）',
                                        `purchase_identifier_format_1` varchar(4) DEFAULT NULL COMMENT '购买标识格式（1组）',
                                        `account_selection_1` varchar(4) DEFAULT NULL COMMENT '账户选择（1组）',
                                        `installment_payment_count_1` varchar(4) DEFAULT NULL COMMENT '分期付款次数（1组）',
                                        `purchase_identifier_1` varchar(32) DEFAULT NULL COMMENT '购买标识（1组）',
                                        `cashback_1` varchar(16) DEFAULT NULL COMMENT '返现金额（1组）',
                                        `chip_condition_code_1` varchar(4) DEFAULT NULL COMMENT '芯片状态代码（1组）',
                                        `pos_environment_1` varchar(4) DEFAULT NULL COMMENT 'POS环境（1组）',
                                        `transaction_code_qualifier_4_sd` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（4SD组）',
                                        `transaction_component_sequence_number_4_sd` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（4SD组）',
                                        `agent_unique_id_4_sd` varchar(8) DEFAULT NULL COMMENT '代理唯一ID（4SD组）',
                                        `reserved1_4_sd` varchar(8) DEFAULT NULL COMMENT '预留字段1（4SD组）',
                                        `network_identification_code_4_sd` varchar(8) DEFAULT NULL COMMENT '网络识别代码（4SD组）',
                                        `contact_for_information_4_sd` varchar(32) DEFAULT NULL COMMENT '信息联系方（4SD组）',
                                        `adjustment_processing_indicator_4_sd` varchar(4) DEFAULT NULL COMMENT '调整处理指示器（4SD组）',
                                        `message_reason_code_4_sd` varchar(4) DEFAULT NULL COMMENT '消息原因代码（4SD组）',
                                        `surcharge_amount_4_sd` varchar(8) DEFAULT NULL COMMENT '附加费金额（4SD组）',
                                        `surcharge_credit_debit_indicator_4_sd` varchar(4) DEFAULT NULL COMMENT '附加费借贷指示器（4SD组）',
                                        `visa_internal_use_only_4_sd` varchar(16) DEFAULT NULL COMMENT 'Visa内部使用字段（4SD组）',
                                        `additional_transaction_fee1_amount_4_sd` varchar(8) DEFAULT NULL COMMENT '附加交易费1金额（4SD组）',
                                        `additional_transaction_fee2_amount_4_sd` varchar(8) DEFAULT NULL COMMENT '附加交易费2金额（4SD组）',
                                        `total_discount_amount_4_sd` varchar(4) DEFAULT NULL COMMENT '总折扣金额（4SD组）',
                                        `reserved2_4_sd` varchar(8) DEFAULT NULL COMMENT '预留字段2（4SD组）',
                                        `surcharge_amount_in_billing_currency_4_sd` varchar(8) DEFAULT NULL COMMENT '账单币种附加费金额（4SD组）',
                                        `money_transfer_foreign_exchange_fee_4_sd` varchar(8) DEFAULT NULL COMMENT '汇款外汇费用（4SD组）',
                                        `payment_account_reference_4_sd` varchar(32) DEFAULT NULL COMMENT '支付账户参考号（4SD组）',
                                        `token_requestor_id_4_sd` varchar(16) DEFAULT NULL COMMENT '令牌请求者ID（4SD组）',
                                        `reserved3_4_sd` varchar(16) DEFAULT NULL COMMENT '预留字段3（4SD组）',
                                        `transaction_code_qualifier_4_pd` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（4PD组）',
                                        `transaction_component_sequence_number_4_pd` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（4PD组）',
                                        `business_format_code_4_pd` varchar(4) DEFAULT NULL COMMENT '业务格式代码（4PD组）',
                                        `reserved1_4_pd` varchar(16) DEFAULT NULL COMMENT '预留字段1（4PD组）',
                                        `promotion_type_4_pd` varchar(4) DEFAULT NULL COMMENT '促销类型（4PD组）',
                                        `promotion_code_4_pd` varchar(32) DEFAULT NULL COMMENT '促销代码（4PD组）',
                                        `reserved2_4_pd` varchar(130) DEFAULT NULL COMMENT '预留字段2（4PD组）',
                                        `transaction_code_qualifier_4_df` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（4DF组）',
                                        `transaction_component_sequence_number_4_df` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（4DF组）',
                                        `business_format_code_4_df` varchar(4) DEFAULT NULL COMMENT '业务格式代码（4DF组）',
                                        `agent_unique_id_4_df` varchar(8) DEFAULT NULL COMMENT '代理唯一ID（4DF组）',
                                        `reserved1_4_df` varchar(8) DEFAULT NULL COMMENT '预留字段1（4DF组）',
                                        `network_identification_code_4_df` varchar(32) DEFAULT NULL COMMENT '网络识别代码（4DF组）',
                                        `contact_for_information_4_df` varchar(4) DEFAULT NULL COMMENT '信息联系方（4DF组）',
                                        `adjustment_processing_indicator_4_df` varchar(4) DEFAULT NULL COMMENT '调整处理指示器（4DF组）',
                                        `message_reason_code_4_df` varchar(4) DEFAULT NULL COMMENT '消息原因代码（4DF组）',
                                        `dispute_condition_4_df` varchar(4) DEFAULT NULL COMMENT '争议条件（4DF组）',
                                        `vrol_financial_id_4_df` varchar(16) DEFAULT NULL COMMENT 'VROL财务ID（4DF组）',
                                        `vrol_case_number_4_df` varchar(16) DEFAULT NULL COMMENT 'VROL案例编号（4DF组）',
                                        `vrol_bundle_case_number_4_df` varchar(16) DEFAULT NULL COMMENT 'VROL捆绑案例编号（4DF组）',
                                        `client_case_number_4_df` varchar(32) DEFAULT NULL COMMENT '客户案例编号（4DF组）',
                                        `dispute_status_4_df` varchar(4) DEFAULT NULL COMMENT '争议状态（4DF组）',
                                        `surcharge_amount_4_df` varchar(8) DEFAULT NULL COMMENT '附加费金额（4DF组）',
                                        `surcharge_credit_debit_indicator_4_df` varchar(4) DEFAULT NULL COMMENT '附加费借贷指示器（4DF组）',
                                        `reserved2_4_df` varchar(64) DEFAULT NULL COMMENT '预留字段2（4DF组）',
                                        `transaction_code_qualifier_4_sp` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（4SP组）',
                                        `transaction_component_sequence_number_4_sp` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（4SP组）',
                                        `business_format_code_4_sp` varchar(4) DEFAULT NULL COMMENT '业务格式代码（4SP组）',
                                        `agent_unique_id_4_sp` varchar(8) DEFAULT NULL COMMENT '代理唯一ID（4SP组）',
                                        `reserved1_4_sp` varchar(8) DEFAULT NULL COMMENT '预留字段1（4SP组）',
                                        `network_identification_code_4_sp` varchar(4) DEFAULT NULL COMMENT '网络识别代码（4SP组）',
                                        `contact_for_information_4_sp` varchar(32) DEFAULT NULL COMMENT '信息联系方（4SP组）',
                                        `adjustment_processing_indicator_4_sp` varchar(4) DEFAULT NULL COMMENT '调整处理指示器（4SP组）',
                                        `message_reason_code_4_sp` varchar(4) DEFAULT NULL COMMENT '消息原因代码（4SP组）',
                                        `surcharge_amount_4_sp` varchar(8) DEFAULT NULL COMMENT '附加费金额（4SP组）',
                                        `surcharge_credit_debit_indicator_4_sp` varchar(4) DEFAULT NULL COMMENT '附加费借贷指示器（4SP组）',
                                        `visa_internal_use_only_4_sp` varchar(16) DEFAULT NULL COMMENT 'Visa内部使用字段（4SP组）',
                                        `promotion_type_4_sp` varchar(4) DEFAULT NULL COMMENT '促销类型（4SP组）',
                                        `promotion_code_4_sp` varchar(32) DEFAULT NULL COMMENT '促销代码（4SP组）',
                                        `surcharge_amount_in_cardholder_billing_currency_4_sp` varchar(8) DEFAULT NULL COMMENT '持卡人账单币种附加费金额（4SP组）',
                                        `payment_account_reference_4_sp` varchar(32) DEFAULT NULL COMMENT '支付账户参考号（4SP组）',
                                        `token_requestor_id_4_sp` varchar(16) DEFAULT NULL COMMENT '令牌请求者ID（4SP组）',
                                        `additional_transaction_fee1_amount_4_sp` varchar(8) DEFAULT NULL COMMENT '附加交易费1金额（4SP组）',
                                        `total_discount_amount_4_sp` varchar(8) DEFAULT NULL COMMENT '总折扣金额（4SP组）',
                                        `reserved2_4_sp` varchar(4) DEFAULT NULL COMMENT '预留字段2（4SP组）',
                                        `transaction_code_qualifier_5` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（5组）',
                                        `transaction_component_sequence_number_5` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（5组）',
                                        `transaction_identifier_5` varchar(16) DEFAULT NULL COMMENT '交易标识（5组）',
                                        `authorized_amount_5` varchar(16) DEFAULT NULL COMMENT '授权金额（5组）',
                                        `authorization_currency_code_5` varchar(4) DEFAULT NULL COMMENT '授权币种代码（5组，遵循ISO 4217）',
                                        `authorization_response_code_5` varchar(4) DEFAULT NULL COMMENT '授权响应码（5组）',
                                        `validation_code_5` varchar(4) DEFAULT NULL COMMENT '验证代码（5组）',
                                        `excluded_transaction_identifier_reason_5` varchar(4) DEFAULT NULL COMMENT '排除交易标识原因（5组）',
                                        `reserved1_5` varchar(4) DEFAULT NULL COMMENT '预留字段1（5组）',
                                        `reserved2_5` varchar(4) DEFAULT NULL COMMENT '预留字段2（5组）',
                                        `multiple_clearing_sequence_number_5` varchar(4) DEFAULT NULL COMMENT '多清算序列号（5组）',
                                        `multiple_clearing_sequence_count_5` varchar(4) DEFAULT NULL COMMENT '多清算序列总数（5组）',
                                        `market_specific_authorization_data_indicator_5` varchar(4) DEFAULT NULL COMMENT '市场特定授权数据指示器（5组）',
                                        `total_authorized_amount_5` varchar(16) DEFAULT NULL COMMENT '总授权金额（5组）',
                                        `information_indicator_5` varchar(4) DEFAULT NULL COMMENT '信息指示器（5组）',
                                        `merchant_telephone_number_5` varchar(16) DEFAULT NULL COMMENT '商户电话号码（5组）',
                                        `additional_data_indicator_5` varchar(4) DEFAULT NULL COMMENT '附加数据指示器（5组）',
                                        `merchant_volume_indicator_5` varchar(4) DEFAULT NULL COMMENT '商户交易量指示器（5组）',
                                        `electronic_commerce_goods_indicator_5` varchar(4) DEFAULT NULL COMMENT '电子商务商品指示器（5组）',
                                        `merchant_verification_value_5` varchar(16) DEFAULT NULL COMMENT '商户验证值（5组，MVS）',
                                        `interchange_fee_amount_5` varchar(16) DEFAULT NULL COMMENT '交换费金额（5组）',
                                        `interchange_fee_sign_5` varchar(4) DEFAULT NULL COMMENT '交换费符号（5组，+表示借方，-表示贷方）',
                                        `source_currency_to_base_currency_exchange_rate_5` varchar(8) DEFAULT NULL COMMENT '源币种到基准币种汇率（5组）',
                                        `base_currency_to_destination_currency_exchange_rate_5` varchar(8) DEFAULT NULL COMMENT '基准币种到目标币种汇率（5组）',
                                        `optional_issuer_isa_amount_5` varchar(16) DEFAULT NULL COMMENT '可选发卡行ISA金额（5组）',
                                        `product_id_5` varchar(4) DEFAULT NULL COMMENT '产品ID（5组）',
                                        `program_id_5` varchar(8) DEFAULT NULL COMMENT '计划ID（5组）',
                                        `dynamic_currency_conversion_indicator_5` varchar(4) DEFAULT NULL COMMENT '动态货币转换指示器（5组）',
                                        `account_type_identification_5` varchar(4) DEFAULT NULL COMMENT '账户类型标识（5组）',
                                        `spend_qualified_indicator_5` varchar(4) DEFAULT NULL COMMENT '消费合格指示器（5组）',
                                        `pan_token_5` varchar(16) DEFAULT NULL COMMENT 'PAN令牌（5组，支付卡号令牌）',
                                        `reserved3_5` varchar(4) DEFAULT NULL COMMENT '预留字段3（5组）',
                                        `account_funding_source_5` varchar(4) DEFAULT NULL COMMENT '账户资金来源（5组）',
                                        `cvv2_result_code_5` varchar(4) DEFAULT NULL COMMENT 'CVV2结果码（5组）',
                                        `transaction_code_qualifier_6` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（6组）',
                                        `transaction_component_sequence_number_6` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（6组）',
                                        `local_tax_6` varchar(16) DEFAULT NULL COMMENT '地方税（6组）',
                                        `local_tax_included_6` varchar(4) DEFAULT NULL COMMENT '地方税是否包含（6组，Y/N）',
                                        `national_tax_6` varchar(16) DEFAULT NULL COMMENT '国家税（6组）',
                                        `national_tax_included_6` varchar(4) DEFAULT NULL COMMENT '国家税是否包含（6组，Y/N）',
                                        `merchant_vat_registration_business_reference_number_6` varchar(32) DEFAULT NULL COMMENT '商户增值税注册业务参考号（6组）',
                                        `customer_vat_regisration_number_6` varchar(16) DEFAULT NULL COMMENT '客户增值税注册号（6组）',
                                        `visa_merchant_identifier_6` varchar(8) DEFAULT NULL COMMENT 'Visa商户标识（6组）',
                                        `reserved_6` varchar(4) DEFAULT NULL COMMENT '预留字段（6组）',
                                        `summary_commodity_code_6` varchar(4) DEFAULT NULL COMMENT '汇总商品代码（6组）',
                                        `other_tax_6` varchar(16) DEFAULT NULL COMMENT '其他税费（6组）',
                                        `message_identifier_6` varchar(16) DEFAULT NULL COMMENT '消息标识（6组）',
                                        `time_of_purchase_6` varchar(4) DEFAULT NULL COMMENT '购买时间（6组，格式：HHMM）',
                                        `customer_code_reference_identifier_cri_6` varchar(20) DEFAULT NULL COMMENT '客户代码参考标识（6组，CRI）',
                                        `non_fuel_product_code1_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码1（6组）',
                                        `non_fuel_product_code2_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码2（6组）',
                                        `non_fuel_product_code3_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码3（6组）',
                                        `non_fuel_product_code4_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码4（6组）',
                                        `non_fuel_product_code5_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码5（6组）',
                                        `non_fuel_product_code6_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码6（6组）',
                                        `non_fuel_product_code7_6` varchar(4) DEFAULT NULL COMMENT '非燃料产品代码7（6组）',
                                        `non_fuel_product_code8_6` varchar(16) DEFAULT NULL COMMENT '非燃料产品代码8（6组）',
                                        `merchant_postal_code_6` varchar(16) DEFAULT NULL COMMENT '商户邮政编码（6组）',
                                        `reserved2_6` varchar(4) DEFAULT NULL COMMENT '预留字段2（6组）',
                                        `transaction_code_qualifier_7` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（7组）',
                                        `transaction_component_sequence_number_7` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（7组）',
                                        `transaction_type_7` varchar(4) DEFAULT NULL COMMENT '交易类型（7组）',
                                        `card_sequence_number_7` varchar(4) DEFAULT NULL COMMENT '卡片序列号（7组）',
                                        `terminal_transaction_date_7` varchar(8) DEFAULT NULL COMMENT '终端交易日期（7组，格式：YYYYMMDD）',
                                        `terminal_capability_profile_7` varchar(8) DEFAULT NULL COMMENT '终端能力配置文件（7组）',
                                        `terminal_country_code_7` varchar(4) DEFAULT NULL COMMENT '终端所在国家代码（7组，遵循ISO 3166）',
                                        `terminal_serial_number_7` varchar(8) DEFAULT NULL COMMENT '终端序列号（7组）',
                                        `unpredictable_number_7` varchar(8) DEFAULT NULL COMMENT '不可预测数（7组，用于安全验证）',
                                        `application_transaction_counter_7` varchar(4) DEFAULT NULL COMMENT '应用交易计数器（7组，ATC）',
                                        `application_interchange_profile_7` varchar(4) DEFAULT NULL COMMENT '应用交换配置文件（7组，AIP）',
                                        `cryptogram_7` varchar(16) DEFAULT NULL COMMENT '密码（7组，用于交易安全验证）',
                                        `issuer_application_data_byte2_7` varchar(4) DEFAULT NULL COMMENT '发卡行应用数据字节2（7组）',
                                        `issuer_application_data_byte3_7` varchar(4) DEFAULT NULL COMMENT '发卡行应用数据字节3（7组）',
                                        `terminal_verification_results_7` varchar(16) DEFAULT NULL COMMENT '终端验证结果（7组，TVR）',
                                        `issuer_application_data_byte4_7` varchar(16) DEFAULT NULL COMMENT '发卡行应用数据字节4（7组）',
                                        `cryptogram_amount_7` varchar(16) DEFAULT NULL COMMENT '密码金额（7组）',
                                        `issuer_application_data_byte8_7` varchar(4) DEFAULT NULL COMMENT '发卡行应用数据字节8（7组）',
                                        `issuer_application_data_byte9_16_7` varchar(16) DEFAULT NULL COMMENT '发卡行应用数据字节9-16（7组）',
                                        `issuer_application_data_byte1_7` varchar(4) DEFAULT NULL COMMENT '发卡行应用数据字节1（7组）',
                                        `issuer_application_data_byte17_7` varchar(4) DEFAULT NULL COMMENT '发卡行应用数据字节17（7组）',
                                        `issuer_application_data_byte18_32_7` varchar(32) DEFAULT NULL COMMENT '发卡行应用数据字节18-32（7组）',
                                        `form_factor_indicator_7` varchar(8) DEFAULT NULL COMMENT '形态因子指示器（7组，如实体卡/虚拟卡）',
                                        `issuer_script1_results_7` varchar(16) DEFAULT NULL COMMENT '发卡行脚本1结果（7组）',
                                        `transaction_code_qualifier_d_fs` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（D_FS组，燃料服务）',
                                        `transaction_component_sequence_number_d_fs` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（D_FS组）',
                                        `business_format_code_d_fs` varchar(4) DEFAULT NULL COMMENT '业务格式代码（D_FS组）',
                                        `charging_power_output_capacity_d_fs` varchar(8) DEFAULT NULL COMMENT '充电功率输出容量（D_FS组，单位：kW）',
                                        `charging_reason_code_d_fs` varchar(4) DEFAULT NULL COMMENT '充电原因代码（D_FS组）',
                                        `total_time_plugged_in_d_fs` varchar(8) DEFAULT NULL COMMENT '总插电时间（D_FS组，单位：分钟）',
                                        `total_charging_time_d_fs` varchar(8) DEFAULT NULL COMMENT '总充电时间（D_FS组，单位：分钟）',
                                        `start_time_of_charge_d_fs` varchar(8) DEFAULT NULL COMMENT '充电开始时间（D_FS组，格式：HHMMSS）',
                                        `finish_time_of_charge_d_fs` varchar(8) DEFAULT NULL COMMENT '充电结束时间（D_FS组，格式：HHMMSS）',
                                        `estimated_km_miles_added_d_fs` varchar(8) DEFAULT NULL COMMENT '预估增加里程（D_FS组，单位：km/英里）',
                                        `carbon_footprint_d_fs` varchar(16) DEFAULT NULL COMMENT '碳足迹（D_FS组，单位：kg CO₂）',
                                        `estimated_vehicle_km_miles_available_d_fs` varchar(8) DEFAULT NULL COMMENT '预估车辆可用里程（D_FS组，单位：km/英里）',
                                        `maximum_power_dispensed_d_fs` varchar(8) DEFAULT NULL COMMENT '最大输出功率（D_FS组，单位：kW）',
                                        `connector_type_d_fs` varchar(4) DEFAULT NULL COMMENT '连接器类型（D_FS组）',
                                        `discount_method_d_fs` varchar(4) DEFAULT NULL COMMENT '折扣方式（D_FS组）',
                                        `discount_agent_d_fs` varchar(4) DEFAULT NULL COMMENT '折扣代理（D_FS组）',
                                        `discount_plan_id_d_fs` varchar(4) DEFAULT NULL COMMENT '折扣计划ID（D_FS组）',
                                        `client_id_d_fs` varchar(16) DEFAULT NULL COMMENT '客户ID（D_FS组）',
                                        `reserved_d_fs` varchar(86) DEFAULT NULL COMMENT '预留字段（D_FS组）',
                                        `transaction_code_qualifier_d_ip` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（D_IP组，分期付款）',
                                        `transaction_component_sequence_number_d_ip` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（D_IP组）',
                                        `business_format_code_d_ip` varchar(4) DEFAULT NULL COMMENT '业务格式代码（D_IP组）',
                                        `instalment_payment_total_amount_d_ip` varchar(16) DEFAULT NULL COMMENT '分期付款总金额（D_IP组）',
                                        `instalment_payment_currency_code_d_ip` varchar(4) DEFAULT NULL COMMENT '分期付款币种代码（D_IP组，遵循ISO 4217）',
                                        `number_of_installments_d_ip` varchar(4) DEFAULT NULL COMMENT '分期期数（D_IP组）',
                                        `amount_of_each_instalment_d_ip` varchar(16) DEFAULT NULL COMMENT '每期分期金额（D_IP组）',
                                        `instalment_payment_number_d_ip` varchar(4) DEFAULT NULL COMMENT '分期付款序号（D_IP组）',
                                        `frequency_of_installments_d_ip` varchar(4) DEFAULT NULL COMMENT '分期频率（D_IP组，如每月/每季度）',
                                        `plan_owner_d_ip` varchar(4) DEFAULT NULL COMMENT '计划所有者（D_IP组）',
                                        `plan_registration_system_identifier_d_ip` varchar(16) DEFAULT NULL COMMENT '计划注册系统标识（D_IP组）',
                                        `reserved_d_ip` varchar(126) DEFAULT NULL COMMENT '预留字段（D_IP组）',
                                        `transaction_code_qualifier_d_rp` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（D_RP组，循环支付）',
                                        `transaction_component_sequence_number_d_rp` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（D_RP组）',
                                        `business_format_code_d_rp` varchar(4) DEFAULT NULL COMMENT '业务格式代码（D_RP组）',
                                        `recurring_payment_type_d_rp` varchar(4) DEFAULT NULL COMMENT '循环支付类型（D_RP组）',
                                        `payment_amount_indicator_per_transaction_d_rp` varchar(4) DEFAULT NULL COMMENT '每笔交易金额指示器（D_RP组）',
                                        `number_of_recurring_payment_d_rp` varchar(4) DEFAULT NULL COMMENT '循环支付次数（D_RP组）',
                                        `frequency_of_recurring_payment_d_rp` varchar(4) DEFAULT NULL COMMENT '循环支付频率（D_RP组）',
                                        `registration_reference_number_d_rp` varchar(36) DEFAULT NULL COMMENT '注册参考号（D_RP组）',
                                        `maximum_recurring_payment_amount_d_rp` varchar(16) DEFAULT NULL COMMENT '最大循环支付金额（D_RP组）',
                                        `validation_indicator_d_rp` varchar(4) DEFAULT NULL COMMENT '验证指示器（D_RP组）',
                                        `reserved_d_rp` varchar(126) DEFAULT NULL COMMENT '预留字段（D_RP组）',
                                        `transaction_code_qualifier_d_oc` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（D_OC组，其他消费）',
                                        `transaction_component_sequence_number_d_oc` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（D_OC组）',
                                        `business_format_code_d_oc` varchar(4) DEFAULT NULL COMMENT '业务格式代码（D_OC组）',
                                        `recipient_name_d_oc` varchar(32) DEFAULT NULL COMMENT '接收方名称（D_OC组）',
                                        `purpose_of_payment_d_oc` varchar(16) DEFAULT NULL COMMENT '支付目的（D_OC组）',
                                        `pre_currency_conversion_amount_d_oc` varchar(16) DEFAULT NULL COMMENT '货币转换前金额（D_OC组）',
                                        `pre_currency_conversion_currency_code_d_oc` varchar(4) DEFAULT NULL COMMENT '货币转换前币种代码（D_OC组，遵循ISO 4217）',
                                        `acceptor_legal_business_name_d_oc` varchar(32) DEFAULT NULL COMMENT '受理方法定企业名称（D_OC组）',
                                        `payment_facilitator_name_d_oc` varchar(32) DEFAULT NULL COMMENT '支付服务商名称（D_OC组）',
                                        `customer_reference_code_d_oc` varchar(4) DEFAULT NULL COMMENT '客户参考代码（D_OC组）',
                                        `identification_type_code_d_oc` varchar(4) DEFAULT NULL COMMENT '证件类型代码（D_OC组）',
                                        `identification_subtype_d_oc` varchar(4) DEFAULT NULL COMMENT '证件子类型（D_OC组）',
                                        `identification_value_d_oc` varchar(36) DEFAULT NULL COMMENT '证件值（D_OC组，如证件号码）',
                                        `identification_issuing_country_code_d_oc` varchar(4) DEFAULT NULL COMMENT '证件发行国家代码（D_OC组，遵循ISO 3166）',
                                        `reserved_d_oc` varchar(12) DEFAULT NULL COMMENT '预留字段（D_OC组）',
                                        `transaction_code_qualifier_e` varchar(4) DEFAULT NULL COMMENT '交易代码限定符（E组）',
                                        `transaction_component_sequence_number_e` varchar(4) DEFAULT NULL COMMENT '交易组件序列号（E组）',
                                        `business_format_code_e` varchar(4) DEFAULT NULL COMMENT '业务格式代码（E组）',
                                        `vfc_payment_credential_e` varchar(32) DEFAULT NULL COMMENT 'VFC支付凭证（E组）',
                                        `account_rule_identifier_e` varchar(48) DEFAULT NULL COMMENT '账户规则标识（E组）',
                                        `reserved_e` varchar(100) DEFAULT NULL COMMENT '预留字段（E组）',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `idx_file_name` (`file_name`) USING BTREE,
                                        KEY `idx_transaction_code` (`transaction_code`) USING BTREE,
                                        KEY `idx_usage_code_0` (`usage_code_0`) USING BTREE
) COMMENT='Visa清分base05清分解析文件';


CREATE TABLE `kc_visa_base_10_data` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `file_name` varchar(255)  DEFAULT NULL COMMENT '清分文件',
                                        `transaction_code` varchar(2)  DEFAULT NULL COMMENT '交易代码',
                                        `transaction_code_qualifier_0` varchar(1)  DEFAULT NULL COMMENT '交易代码限定符_0',
                                        `transaction_component_sequence_number_0` varchar(1)  DEFAULT NULL COMMENT '交易组件序列号_0',
                                        `destination_identifier_0` varchar(6)  DEFAULT NULL COMMENT '目标标识符_0',
                                        `source_identifier_0` varchar(6)  DEFAULT NULL COMMENT '源标识符_0',
                                        `reason_code_0` varchar(4)  DEFAULT NULL COMMENT '原因代码_0',
                                        `country_code_0` varchar(3)  DEFAULT NULL COMMENT '国家代码_0',
                                        `event_date_0` varchar(4)  DEFAULT NULL COMMENT '事件日期(MMDD)_0',
                                        `account_number_0` varchar(16)  DEFAULT NULL COMMENT '账户号码_0',
                                        `account_number_extension_0` varchar(3)  DEFAULT NULL COMMENT '账户号码扩展_0',
                                        `destination_amount_0` varchar(12)  DEFAULT NULL COMMENT '目标金额_0',
                                        `destination_currency_code_0` varchar(3)  DEFAULT NULL COMMENT '目标货币代码_0',
                                        `source_amount_0` varchar(12)  DEFAULT NULL COMMENT '源金额_0',
                                        `source_currency_code_0` varchar(3)  DEFAULT NULL COMMENT '源货币代码_0',
                                        `message_text_0` varchar(70)  DEFAULT NULL COMMENT '消息文本_0',
                                        `settlement_flag_0` varchar(1)  DEFAULT NULL COMMENT '结算标志_0',
                                        `transaction_identifier_0` varchar(15)  DEFAULT NULL COMMENT '交易标识符_0',
                                        `reserved_0` varchar(1)  DEFAULT NULL COMMENT '保留字段_0',
                                        `central_processing_date_0` varchar(4)  DEFAULT NULL COMMENT '中央处理日期(YDDD)_0',
                                        `reimbursement_attribute_0` varchar(1)  DEFAULT NULL COMMENT '报销属性_0',
                                        `transaction_code_qualifier_1` varchar(1)  DEFAULT NULL COMMENT '交易代码限定符_1',
                                        `transaction_component_sequence_number_1` varchar(1)  DEFAULT NULL COMMENT '交易组件序列号_1',
                                        `rate_table_id_1` varchar(15)  DEFAULT NULL COMMENT '费率表ID_1',
                                        `reserved_1` varchar(159)  DEFAULT NULL COMMENT '保留字段_1',
                                        `transaction_code_qualifier_4` varchar(1)  DEFAULT NULL COMMENT '交易代码限定符_4',
                                        `transaction_component_sequence_number_4` varchar(1)  DEFAULT NULL COMMENT '交易组件序列号_4',
                                        `business_format_code_4` varchar(2)  DEFAULT NULL COMMENT '业务格式代码_4',
                                        `promotion_type_4` varchar(2)  DEFAULT NULL COMMENT '促销类型_4',
                                        `promotion_code_4` varchar(25)  DEFAULT NULL COMMENT '促销代码_4',
                                        `network_identification_code_4` varchar(4)  DEFAULT NULL COMMENT '网络识别代码_4',
                                        `reserved_4` varchar(131)  DEFAULT NULL COMMENT '保留字段_4',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `idx_file_name` (`file_name`) USING BTREE
)  COMMENT='Visa TC10/20base文件解析';


CREATE TABLE `kc_visa_base_33_data` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `file_name` varchar(255)  DEFAULT NULL COMMENT '清分文件',
                                        `transaction_code` varchar(2)  DEFAULT NULL COMMENT '交易代码',
                                        `transaction_code_qualifier_0` varchar(1)  DEFAULT NULL COMMENT '交易代码限定符_0',
                                        `transaction_component_sequence_number_0` varchar(1)  DEFAULT NULL COMMENT '交易组件序列号_0',
                                        `destination_identifier_0` varchar(6)  DEFAULT NULL COMMENT '目标标识符_0',
                                        `source_identifier_0` varchar(6)  DEFAULT NULL COMMENT '源标识符_0',
                                        `vcr_record_identifier_0` varchar(3)  DEFAULT NULL COMMENT 'VCR记录标识符_0',
                                        `dispute_status_0` varchar(2)  DEFAULT NULL COMMENT '争议状态_0',
                                        `dispute_transaction_code_0` varchar(2)  DEFAULT NULL COMMENT '争议交易代码_0',
                                        `dispute_transaction_code_qualifier_0` varchar(1)  DEFAULT NULL COMMENT '争议交易代码限定符_0',
                                        `originator_recipient_indicator_0` varchar(1)  DEFAULT NULL COMMENT '发起方接收方指示符_0',
                                        `account_number_0` varchar(16)  DEFAULT NULL COMMENT '账户号码_0',
                                        `account_number_extension_0` varchar(3)  DEFAULT NULL COMMENT '账户号码扩展_0',
                                        `acquirer_reference_number_0` varchar(23)  DEFAULT NULL COMMENT '收单行参考号_0',
                                        `purchase_date_0` varchar(4)  DEFAULT NULL COMMENT '购买日期_0',
                                        `source_amount_0` varchar(12)  DEFAULT NULL COMMENT '源金额_0',
                                        `source_currency_code_0` varchar(3)  DEFAULT NULL COMMENT '源货币代码_0',
                                        `merchant_name_0` varchar(25)  DEFAULT NULL COMMENT '商户名称_0',
                                        `merchant_city_0` varchar(13)  DEFAULT NULL COMMENT '商户城市_0',
                                        `merchant_country_code_0` varchar(3)  DEFAULT NULL COMMENT '商户国家代码_0',
                                        `merchant_category_code_0` varchar(4)  DEFAULT NULL COMMENT '商户类别代码_0',
                                        `merchant_state_province_code_0` varchar(3)  DEFAULT NULL COMMENT '商户州/省代码_0',
                                        `merchant_zip_code_0` varchar(5)  DEFAULT NULL COMMENT '商户邮政编码_0',
                                        `requested_payment_service_0` varchar(1)  DEFAULT NULL COMMENT '请求的支付服务_0',
                                        `authorization_code_0` varchar(6)  DEFAULT NULL COMMENT '授权代码_0',
                                        `pos_entry_mode_0` varchar(2)  DEFAULT NULL COMMENT 'POS输入模式_0',
                                        `central_processing_date_0` varchar(4)  DEFAULT NULL COMMENT '中央处理日期_0',
                                        `card_acceptor_id_0` varchar(15)  DEFAULT NULL COMMENT '收卡人ID_0',
                                        `reimbursement_attribute_0` varchar(1)  DEFAULT NULL COMMENT '报销属性_0',
                                        `transaction_code_qualifier_1` varchar(1)  DEFAULT NULL COMMENT '交易代码限定符_1',
                                        `transaction_component_sequence_number_1` varchar(1)  DEFAULT NULL COMMENT '交易组件序列号_1',
                                        `network_identification_code_1` varchar(4)  DEFAULT NULL COMMENT '网络识别代码_1',
                                        `dispute_condition_1` varchar(3)  DEFAULT NULL COMMENT '争议条件_1',
                                        `vrql_financial_id_1` varchar(11)  DEFAULT NULL COMMENT 'VRQL财务ID_1',
                                        `vrql_case_number_1` varchar(10)  DEFAULT NULL COMMENT 'VRQL案例号_1',
                                        `vrql_bundle_case_number_1` varchar(10)  DEFAULT NULL COMMENT 'VRQL捆绑案例号_1',
                                        `client_case_number_1` varchar(20)  DEFAULT NULL COMMENT '客户案例号_1',
                                        `reserved1_1` varchar(4)  DEFAULT NULL COMMENT '保留字段1_1',
                                        `multiple_clearing_sequence_number_1` varchar(2)  DEFAULT NULL COMMENT '多次清算序列号_1',
                                        `multiple_clearing_sequence_count_1` varchar(2)  DEFAULT NULL COMMENT '多次清算序列计数_1',
                                        `product_id_1` varchar(2)  DEFAULT NULL COMMENT '产品ID_1',
                                        `spend_qualified_indicator_1` varchar(1)  DEFAULT NULL COMMENT '消费合格指示符_1',
                                        `dispute_financial_reason_code_1` varchar(2)  DEFAULT NULL COMMENT '争议财务原因代码_1',
                                        `settlement_flag_1` varchar(1)  DEFAULT NULL COMMENT '结算标志_1',
                                        `usage_code_1` varchar(1)  DEFAULT NULL COMMENT '使用代码_1',
                                        `transaction_identifier_1` varchar(15)  DEFAULT NULL COMMENT '交易标识符_1',
                                        `acquirers_business_id_1` varchar(8)  DEFAULT NULL COMMENT '收单行商业ID_1',
                                        `original_transaction_amount_1` varchar(12)  DEFAULT NULL COMMENT '原始交易金额_1',
                                        `original_transaction_currency_code_1` varchar(3)  DEFAULT NULL COMMENT '原始交易货币代码_1',
                                        `special_chargeback_indicator_1` varchar(1)  DEFAULT NULL COMMENT '特殊退单指示符_1',
                                        `destination_source_settlement_amount_1` varchar(12)  DEFAULT NULL COMMENT '目标/源结算金额_1',
                                        `destination_source_settlement_currency_1` varchar(3)  DEFAULT NULL COMMENT '目标/源结算货币_1',
                                        `source_settlement_amount_sign_1` varchar(1)  DEFAULT NULL COMMENT '源结算金额符号_1',
                                        `rate_table_id_1` varchar(5)  DEFAULT NULL COMMENT '费率表ID_1',
                                        `reserved2_1` varchar(31)  DEFAULT NULL COMMENT '保留字段2_1',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `idx_file_name` (`file_name`) USING BTREE
)  COMMENT='Visa baseTC33数据';

CREATE TABLE `kc_clearing_error` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                     `clearing_id` bigint DEFAULT NULL COMMENT '清算表主键id',
                                     `clearing_no` varchar(32)  DEFAULT NULL COMMENT '清算流水号',
                                     `channel_source` varchar(16)  DEFAULT NULL COMMENT '通道来源;YW:yeewallex；',
                                     `system` varchar(32)  DEFAULT NULL COMMENT '来源系统:VCC;KL',
                                     `clearing_date` date DEFAULT NULL COMMENT '清算日期',
                                     `auth_id` bigint DEFAULT NULL COMMENT '关联授权流水表的主键id',
                                     `trans_code` varchar(4)  DEFAULT NULL COMMENT '05:消费;06:退货；07：取现;25:消费查询;26:退货撤销；27：取现撤销',
                                     `customer_mer_id` varchar(64)  DEFAULT NULL COMMENT '商户号',
                                     `merchant_name` varchar(255)  DEFAULT NULL COMMENT '商户名称',
                                     `merchant_country_code` varchar(3)  DEFAULT NULL COMMENT '商户国家代码',
                                     `transaction_date` varchar(32)  DEFAULT NULL COMMENT '交易时间',
                                     `transaction_currency_code` varchar(8)  DEFAULT NULL COMMENT '交易币种',
                                     `transaction_amount` decimal(18,2) DEFAULT NULL COMMENT '交易金额',
                                     `reference_no` varchar(128)  DEFAULT NULL COMMENT '参考号;F37',
                                     `trace_audit_no` varchar(128)  DEFAULT NULL COMMENT '审计追踪:F11',
                                     `card_id` varchar(128)  DEFAULT NULL COMMENT '卡id',
                                     `kcard_id` varchar(64)  DEFAULT NULL COMMENT 'kcard系统卡id',
                                     `card_last_four` varchar(4)  DEFAULT NULL COMMENT '卡号后4位',
                                     `cardholder_currency_code` varchar(8)  DEFAULT NULL COMMENT '持卡人币种',
                                     `cardholder_amount` decimal(18,2) DEFAULT NULL COMMENT '持卡人金额',
                                     `acq_arn` varchar(32)  DEFAULT NULL COMMENT '收单参考号',
                                     `auth_code` varchar(6)  DEFAULT NULL COMMENT 'F38:清分文件授权码；',
                                     `system_auth_code` varchar(64)  DEFAULT NULL COMMENT '系统授权号',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
)  COMMENT='清算异常表';


CREATE TABLE `kc_exception_info` (
                                     `exception_id` bigint NOT NULL COMMENT '主键id',
                                     `clearing_id` bigint DEFAULT NULL COMMENT '清算表主键id',
                                     `clearing_no` varchar(32)  DEFAULT NULL COMMENT '清算流水号',
                                     `channel_source` varchar(16)  DEFAULT NULL COMMENT '通道来源;YW:yeewallex；',
                                     `system` varchar(32)  DEFAULT NULL COMMENT '来源系统:VCC;KL',
                                     `clearing_date` date DEFAULT NULL COMMENT '清算日期',
                                     `auth_id` bigint DEFAULT NULL COMMENT '关联授权流水表的主键id',
                                     `trans_code` varchar(4)  DEFAULT NULL COMMENT '05:消费;06:退货；07：取现;25:消费查询;26:退货撤销；27：取现撤销',
                                     `customer_mer_id` varchar(64)  DEFAULT NULL COMMENT '商户号',
                                     `card_acceptor_name` varchar(255)  DEFAULT NULL COMMENT '商户名称',
                                     `card_acceptor_country_code` varchar(3)  DEFAULT NULL COMMENT '商户国家代码',
                                     `transaction_date` varchar(32)  DEFAULT NULL COMMENT '交易时间',
                                     `transaction_currency_code` varchar(8)  DEFAULT NULL COMMENT '交易币种',
                                     `transaction_amount` decimal(18,2) DEFAULT NULL COMMENT '交易金额',
                                     `reference_no` varchar(128)  DEFAULT NULL COMMENT '参考号;F37',
                                     `trace_audit_no` varchar(128)  DEFAULT NULL COMMENT '审计追踪:F11',
                                     `processor_card_id` varchar(128)  DEFAULT NULL COMMENT '卡id',
                                     `kcard_id` varchar(64)  DEFAULT NULL COMMENT 'kcard系统卡id',
                                     `card_last_four` varchar(4)  DEFAULT NULL COMMENT '卡号后4位',
                                     `cardholder_currency_code` varchar(8)  DEFAULT NULL COMMENT '持卡人币种',
                                     `cardholder_amount` decimal(18,2) DEFAULT NULL COMMENT '持卡人金额',
                                     `acq_arn` varchar(32)  DEFAULT NULL COMMENT '收单参考号',
                                     `auth_amount` decimal(18,2) DEFAULT NULL COMMENT '授权交易金额',
                                     `difference_flag` int DEFAULT '0' COMMENT '差异金额标记;0:无差异金额;1:有差异金额',
                                     `difference_amount` decimal(18,2) DEFAULT NULL COMMENT '差异金额；清算的交易金额-授权的交易金额',
                                     `markup_rate` decimal(6,2) DEFAULT NULL COMMENT 'markup利率',
                                     `cardhold_markup_amount` decimal(18,2) DEFAULT NULL COMMENT '如果有差异金额的情况下:持卡人markup金额;',
                                     `auth_code` varchar(6)  DEFAULT NULL COMMENT 'F38:清分文件授权码；',
                                     `pos_entry_mode_tcr0` varchar(32)  DEFAULT NULL COMMENT 'PosEntryMode',
                                     `acquiring_Identifier_tcr0` varchar(32)  DEFAULT NULL COMMENT 'AcquiringIdentifier ',
                                     `card_acceptor_mcc` varchar(8)  DEFAULT NULL COMMENT 'MerchantCategoryCode',
                                     `cpd` varchar(8)  DEFAULT NULL COMMENT 'CentralProcessingDate格式:YYYYMMDD',
                                     `intechange_fee_amt` decimal(18,6) DEFAULT NULL COMMENT 'InterchangeFeeAmount',
                                     `intechange_fee_sign` varchar(4)  DEFAULT NULL COMMENT 'InterchangeFeeSign',
                                     `pos_environment_tcr1` varchar(32)  DEFAULT NULL COMMENT 'PosEnvironment',
                                     `fx_rate_source_tcr5` varchar(32)  DEFAULT NULL COMMENT 'Source Currency to Base Currency Exchange Rate ',
                                     `fx_rate_destination_tcr5` varchar(32)  DEFAULT NULL COMMENT 'Base Currency to Destination Currency Exchange Rate ',
                                     `authorization_response_code_tcr5` varchar(32)  DEFAULT NULL COMMENT 'AuthorizationResponseCode',
                                     `multiple_clearing_sequence_number_tcr5` varchar(4)  DEFAULT NULL COMMENT 'Multiple Clearing Sequence Number',
                                     `multiple_clearing_sequence_count_tcr5` varchar(4)  DEFAULT NULL COMMENT 'Multiple Clearing Sequence Count',
                                     `mvv` varchar(32)  DEFAULT NULL COMMENT 'Merchant Verification Value ',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `transaction_type` varchar(16)  DEFAULT NULL COMMENT '交易类型',
                                     `clear_amount` decimal(18,3) DEFAULT NULL COMMENT '清分金额',
                                     `error_reason` varchar(64)  DEFAULT NULL COMMENT '差异原因',
                                     `process_status` varchar(32) DEFAULT NULL COMMENT '处理状态',
                                     `operation_type` varchar(32) DEFAULT NULL COMMENT '操作类型',
                                     `operation_user_name` varchar(64) DEFAULT NULL COMMENT '操作人',
                                     `operation_date_time` datetime DEFAULT NULL COMMENT '操作日期',
                                     `transaction_currency_no` varchar(4) DEFAULT NULL COMMENT '交易币种;三位数字',
                                     `cardholder_currency_no` varchar(4) DEFAULT NULL COMMENT '持卡人币种;三位数字',
                                     PRIMARY KEY (`exception_id`) USING BTREE
)  COMMENT='清算05没有匹配上授权,25没有匹配上清分数据';


CREATE TABLE `kc_vrol_dispute` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
                                   `clearing_file_id` bigint DEFAULT NULL COMMENT '关联文件主键id',
                                   `trans_code` varchar(2)  DEFAULT NULL COMMENT '交易代码',
                                   `destination_id` varchar(6)  DEFAULT NULL COMMENT '目标标识符',
                                   `source_id` varchar(6)  DEFAULT NULL COMMENT '源标识符',
                                   `dispute_status` varchar(2)  DEFAULT NULL COMMENT '争议状态',
                                   `dispute_tc` varchar(2)  DEFAULT NULL COMMENT '争议交易代码',
                                   `dispute_tcq` varchar(1)  DEFAULT NULL COMMENT '争议交易代码限定符',
                                   `originator_recipient_indicator` varchar(1)  DEFAULT NULL COMMENT '发起方接收方指示符',
                                   `kcard_id` varchar(16)  DEFAULT NULL COMMENT '卡标识',
                                   `processor_card_id` varchar(16)  DEFAULT NULL COMMENT '处理方卡标识',
                                   `pan` varchar(128)  DEFAULT NULL COMMENT '主账号(PAN)',
                                   `arn` varchar(23)  DEFAULT NULL COMMENT '收单行参考号(ARN)',
                                   `transaction_date` varchar(8)  DEFAULT NULL COMMENT '交易日期',
                                   `source_amt` varchar(12)  DEFAULT NULL COMMENT '源金额',
                                   `source_ccy` varchar(3)  DEFAULT NULL COMMENT '源货币代码',
                                   `card_acceptor_id` varchar(15)  DEFAULT NULL COMMENT '收卡人ID',
                                   `card_acceptor_name` varchar(25)  DEFAULT NULL COMMENT '收卡人名称',
                                   `card_acceptor_country_code` varchar(3)  DEFAULT NULL COMMENT '收卡人国家代码',
                                   `card_acceptor_mcc` varchar(4)  DEFAULT NULL COMMENT '收卡人商户类别代码(MCC)',
                                   `requested_payment_service` varchar(1)  DEFAULT NULL COMMENT '请求的支付服务',
                                   `approve_code` varchar(6)  DEFAULT NULL COMMENT '授权代码',
                                   `pos_entry_mode` varchar(2)  DEFAULT NULL COMMENT 'POS输入模式',
                                   `cpd` varchar(4)  DEFAULT NULL COMMENT '中央处理日期(CPD)',
                                   `dispute_condition` varchar(3)  DEFAULT NULL COMMENT '争议条件',
                                   `vrol_financial_id` varchar(11)  DEFAULT NULL COMMENT 'VRQL财务ID',
                                   `vrol_case_no` varchar(10)  DEFAULT NULL COMMENT 'VRQL案例号',
                                   `vorl_bundle_case_no` varchar(10)  DEFAULT NULL COMMENT 'VRQL捆绑案例号',
                                   `client_case_no` varchar(20)  DEFAULT NULL COMMENT '客户案例号',
                                   `multiple_clearing_seq_no` varchar(2)  DEFAULT NULL COMMENT '多次清算序列号',
                                   `multiple_clearing_seq_count` varchar(2)  DEFAULT NULL COMMENT '多次清算序列计数',
                                   `spend_qualified_indicator` varchar(1)  DEFAULT NULL COMMENT '消费合格指示符',
                                   `dispute_financial_reason_code` varchar(2)  DEFAULT NULL COMMENT '争议财务原因代码',
                                   `settlement_flag` varchar(1)  DEFAULT NULL COMMENT '结算标志',
                                   `usage_code` varchar(1)  DEFAULT NULL COMMENT '使用代码',
                                   `trans_identifier` varchar(15)  DEFAULT NULL COMMENT '交易标识符',
                                   `acq_business_id` varchar(8)  DEFAULT NULL COMMENT '收单行商业ID',
                                   `original_trans_amt` varchar(12)  DEFAULT NULL COMMENT '原始交易金额',
                                   `original_trans_ccy` varchar(3)  DEFAULT NULL COMMENT '原始交易货币代码',
                                   `special_chargeback_flag` varchar(1)  DEFAULT NULL COMMENT '特殊退单指示符',
                                   `settlement_amt` varchar(12)  DEFAULT NULL COMMENT '结算金额',
                                   `settlement_ccy` varchar(3)  DEFAULT NULL COMMENT '结算货币代码',
                                   `source_settlement_amt_sign` varchar(1)  DEFAULT NULL COMMENT '源结算金额符号',
                                   `message_reason_code` varchar(4)  DEFAULT NULL COMMENT '消息原因代码',
                                   `surcharge_amt` varchar(12)  DEFAULT NULL COMMENT '附加费金额',
                                   `surcharge_sign` varchar(1)  DEFAULT NULL COMMENT '附加费符号',
                                   `file_record_id` varchar(10)  DEFAULT NULL COMMENT '文件记录ID',
                                   `clear_id` varchar(20)  DEFAULT NULL COMMENT '清算ID',
                                   `arn_match_flag` varchar(1)  DEFAULT NULL COMMENT 'ARN匹配标志',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
)  COMMENT='VRQL争议数据表';


CREATE TABLE `kc_clearing_file_error_log` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '清算文件异常主键id',
                                              `clearing_file_id` bigint DEFAULT NULL COMMENT '清算文件主键id',
                                              `clearing_date` date DEFAULT NULL COMMENT '清算日期',
                                              `file_name` varchar(255)  DEFAULT NULL COMMENT '文件名称',
                                              `trans_id` varchar(100)  DEFAULT NULL COMMENT '参考号（交易关联ID_YW生成）',
                                              `channel_source` varchar(16)  DEFAULT NULL COMMENT '通道来源;YW:Yeewallex；',
                                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                              `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE
)  COMMENT='清算文件读取异常记录表';


-- 插入 EXCEPTION_OPERATION_TYPE 类型的数据
INSERT INTO vcc_data_dict (dict_type, dict_value, cn_desc, en_desc, valid_flag, dict_index,create_time)
VALUES
    ('EXCEPTION_OPERATION_TYPE', 'RECONCILIATION', '补记账', 'Reconciliation', 1, 1,now()),
    ('EXCEPTION_OPERATION_TYPE', 'NO_ACTION_REQUIRED', '无需处理', 'No Action Required', 1, 2,now());

-- 插入 CLEARING_PROCESSING_STATUS 类型的数据
INSERT INTO vcc_data_dict (dict_type, dict_value, cn_desc, en_desc, valid_flag, dict_index,create_time)
VALUES
    ('CLEARING_PROCESSING_STATUS', 'SUCCESS', '完成', 'Success', 1, 1,now()),
    ('CLEARING_PROCESSING_STATUS', 'FAIL', '失败', 'Fail', 1, 2,now()),
    ('CLEARING_PROCESSING_STATUS', 'PENDING', '待处理', 'Pending', 1, 3,now());


INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-clearing-info' as code,
             'KL清分前置查询'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >= 1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-clearing-exception-info' as code,
             'KL清分前置异常查询'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >= 1;

INSERT INTO vcc_permission (code, name, id, create_date, update_date)
select code, name, id, create_date, update_date
from (select 'boss-kl-visa-report' as code,
             'KL VISA 报表下载'      as name,
             (max(id) + 1)             as id,
             now()                     as create_date,
             null                      as update_date
      from vcc_permission) temp where id >= 1;

CREATE TABLE `kl_visa_file` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件唯一标识',
                                `channel_source` varchar(255) DEFAULT NULL COMMENT '通道来源;PBC-GW',
                                `original_filename` varchar(255) DEFAULT NULL COMMENT '原文件名',
                                `file_name` varchar(255)  DEFAULT NULL COMMENT '原始文件名',
                                `file_type` varchar(50)  DEFAULT NULL COMMENT '文件类型（如PDF、JPG）',
                                `storage_path` varchar(255)  DEFAULT NULL COMMENT '文件存储路径',
                                `file_time` date DEFAULT NULL COMMENT '文件日期',
                                `download_time` datetime DEFAULT NULL COMMENT '下载日期',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE KEY `uniq_ch_file` (`channel_source`,`file_time`)
) COMMENT='visa文件报表下载';

CREATE TABLE `kc_peripheral_file_log` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                          `download_date` varchar(8)  DEFAULT NULL COMMENT '文件下载日期;yyyymmdd',
                                          `file_type` varchar(32)  DEFAULT NULL COMMENT '文件类型',
                                          `file_desc` varchar(255)  DEFAULT NULL COMMENT '文件描述',
                                          `local_file_path` varchar(255)  DEFAULT NULL COMMENT '本地文件路径',
                                          `local_file_name` varchar(255)  DEFAULT NULL COMMENT '本地文件名称',
                                          `remote_file_path` varchar(255)  DEFAULT NULL COMMENT '外部文件路径',
                                          `error_rows` varchar(255)  DEFAULT NULL COMMENT '异常行号',
                                          `file_status` varchar(8)  DEFAULT NULL COMMENT '文件状态；成功;失败;处理中',
                                          `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime NULL DEFAULT NULL COMMENT '跟新时间',
                                          PRIMARY KEY (`id`) USING BTREE
)  COMMENT='外部下载文件记录';