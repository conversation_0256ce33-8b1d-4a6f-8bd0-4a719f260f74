# TransactionFeeListener 告警去重优化方案

## 问题分析

### 重复告警的根本原因

通过分析 `TransactionFeeListener` 类的代码，发现了导致重复发送 Lark 告警通知的根本原因：

1. **双重告警触发点**：
   - **第一个触发点**：在 `onMessage` 方法中，当 `authTransactionFeeService.processTransactionFee()` 返回失败结果且达到最大重试次数时（第105-107行）
   - **第二个触发点**：在 `handleRetry` 方法中，当处理过程中抛出异常且达到最大重试次数时（第181-183行）

2. **触发场景**：
   - 当业务处理失败时，会在第一个位置发送告警
   - 如果在处理过程中同时抛出异常，会在 `catch` 块中调用 `handleRetry`，可能在第二个位置再次发送告警
   - 这导致同一个交易ID可能收到多次告警通知

## 优化方案

### 核心思路

实现基于 Redis 的告警去重机制，确保对于同一个交易ID在达到最大重试次数后只发送一次告警通知。

### 技术实现

#### 1. 引入依赖
- 添加 `RedissonCacheUtil` 依赖，用于 Redis 操作
- 添加 `TimeUnit` 用于时间单位控制

#### 2. 新增常量配置
```java
// 告警去重相关常量
private static final String ALARM_DEDUP_KEY_PREFIX = "transaction_fee_alarm_dedup";
private static final long ALARM_DEDUP_EXPIRE_HOURS = 24; // 告警去重键过期时间：24小时
```

#### 3. 核心去重方法
实现 `sendAlarmWithDeduplication` 方法，包含以下逻辑：

**去重检查**：
- 使用交易ID构建唯一的Redis键：`transaction_fee_alarm_dedup:transactionId`
- 检查Redis中是否已存在该键

**告警发送**：
- 如果键不存在（首次告警）：设置Redis键并发送告警
- 如果键已存在（重复告警）：跳过发送，记录日志

**异常处理**：
- 如果Redis操作异常，为保证告警可靠性，降级发送告警
- 记录详细的异常日志

#### 4. 键构建方法
```java
private String buildAlarmDedupKey(String transactionId) {
    return ALARM_DEDUP_KEY_PREFIX + ":" + transactionId;
}
```

### 优化特性

#### 1. 可靠性保障
- **降级机制**：当Redis操作异常时，仍然发送告警，确保重要告警不丢失
- **异常处理**：完善的异常捕获和日志记录

#### 2. 性能优化
- **过期机制**：Redis键设置24小时过期时间，自动清理历史数据
- **轻量级操作**：使用简单的存在性检查，性能开销最小

#### 3. 可观测性
- **详细日志**：记录告警发送、跳过、异常等所有关键操作
- **状态追踪**：可通过日志追踪每个交易的告警处理状态

## 代码修改详情

### 修改的文件
- `kun-linkage-auth-service/src/main/java/com/kun/linkage/auth/mq/TransactionFeeListener.java`

### 主要变更

#### 1. 导入新增依赖
```java
import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import java.util.concurrent.TimeUnit;
```

#### 2. 注入Redis工具类
```java
@Autowired
private RedissonCacheUtil redissonCacheUtil;
```

#### 3. 替换告警调用
- 原来：`authLarkAlarmUtil.sendTransactionFeeAlarm(...)`
- 现在：`sendAlarmWithDeduplication(...)`

#### 4. 新增方法
- `sendAlarmWithDeduplication()` - 带去重机制的告警发送方法
- `buildAlarmDedupKey()` - 构建去重键的工具方法

## 测试验证

### 单元测试覆盖场景
1. **首次告警**：验证正常发送告警并设置去重键
2. **重复告警**：验证跳过发送重复告警
3. **异常处理**：验证Redis异常时的降级处理
4. **成功处理**：验证成功场景不发送告警
5. **重试逻辑**：验证未达到最大重试次数时不发送告警

### 测试文件
- `kun-linkage-auth-service/src/test/java/com/kun/linkage/auth/mq/TransactionFeeListenerTest.java`

## 部署注意事项

### 1. Redis依赖
确保项目中已正确配置Redis连接和RedissonCacheUtil Bean

### 2. 监控建议
- 监控Redis键的创建和过期情况
- 关注告警去重的日志输出
- 监控降级告警的频率

### 3. 配置调优
- 可根据业务需要调整去重键的过期时间
- 可根据实际情况调整去重键的命名规则

## 优化效果

### 1. 解决重复告警问题
- 彻底解决同一交易ID的重复告警问题
- 保持告警的及时性和准确性

### 2. 提升系统稳定性
- 减少无效告警对运维人员的干扰
- 提高告警系统的可信度

### 3. 保持原有功能
- 完全保留原有的重试机制和延迟策略
- 保持核心业务逻辑不变
- 保持日志记录的完整性

## 总结

本次优化通过引入Redis去重机制，在保持原有业务逻辑不变的前提下，有效解决了TransactionFeeListener中重复发送Lark告警的问题。优化方案具有高可靠性、良好的性能表现和完善的异常处理机制，确保了告警系统的稳定性和准确性。
