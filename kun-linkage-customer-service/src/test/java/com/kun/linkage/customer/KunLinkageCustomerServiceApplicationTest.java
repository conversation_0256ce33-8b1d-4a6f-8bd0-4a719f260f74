package com.kun.linkage.customer;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = KunLinkageCustomerServiceApplication.class)
public class KunLinkageCustomerServiceApplicationTest {

    @Test
    public void contextLoads() {
        // This test will simply check if the application context loads successfully
        // No additional assertions are needed as a failure to load the context will throw an exception
    }
}
