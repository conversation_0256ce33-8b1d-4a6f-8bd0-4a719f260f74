server:
  port: 8080

spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ***********:8848
        # 命名空间
        namespace: 724e1c9e-8c3b-46ef-8170-fa656b1ef3a5
        # 服务分组
        group: dev
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 100MB
  shardingsphere:
    enabled: true
    props:
      #是否输出sql
      sql-show: true
      default-data-source-name: ds0
    datasource:
      names: ds0
      ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ********************************************************************************
        username: kcard_dev
        # 原文密码加密的内容
        password: kcard_devABC123!
        #特别提示:配置数据库加密 config这个不能忘掉
        filters: stat,wall,config
        use-global-data-source-stat: true
        # 开启解密config.decrypt=true; 公钥:config.decrypt.key
        #connectionProperties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ==
        # 连接池的配置信息
        # 初始化大小，最小空闲连接数，最大活跃数
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
    # 配置分片分表策略
    rules:
      sharding:
        tables:
          kl_organization_customer_card_operation_record:
            actual-data-nodes: ds0.kl_organization_customer_card_operation_record_$->{0..7}
            table-strategy:
              standard:
                sharding-column: organization_no
                sharding-algorithm-name: kl-organization-customer-card-operation-record-table-inline
          kl_organization_customer_card_info:
            actual-data-nodes: ds0.kl_organization_customer_card_info_$->{0..7}
            table-strategy:
              standard:
                sharding-column: organization_no
                sharding-algorithm-name: kl-organization-customer-card-info-table-inline
          kl_organization_customer_account_info:
            actual-data-nodes: ds0.kl_organization_customer_account_info_$->{0..7}
            table-strategy:
              standard:
                sharding-column: organization_no
                sharding-algorithm-name: kl-organization-customer-account-info-table-inline
          kl_organization_customer_wallet_operation_record:
            actual-data-nodes: ds0.kl_organization_customer_wallet_operation_record_$->{0..7}
            table-strategy:
              standard:
                sharding-column: organization_no
                sharding-algorithm-name: kl-organization-customer-wallet-operation-record-table-inline
          kl_organization_customer_wallet_info:
            actual-data-nodes: ds0.kl_organization_customer_wallet_info_$->{0..7}
            table-strategy:
              standard:
                sharding-column: organization_no
                sharding-algorithm-name: kl-organization-customer-wallet-info-table-inline
          kl_mpc_wallet_webhook_record:
            actual-data-nodes: ds0.kl_mpc_wallet_webhook_record_$->{0..7}
            table-strategy:
              standard:
                sharding-column: organization_no
                sharding-algorithm-name: kl-mpc-wallet-webhook-record-table-inline
          kl_wallet_transaction_detail:
            actual-data-nodes: ds0.kl_wallet_transaction_detail_$->{202505..203512}
            table-strategy:
              standard:
                sharding-column: transaction_datetime
                sharding-algorithm-name: local-date-time-dynamic-year-month
          kl_card_recharge_detail:
            actual-data-nodes: ds0.kl_card_recharge_detail_$->{2025..2035}Q$->{1..4}
            table-strategy:
              standard:
                sharding-column: recharge_datetime
                sharding-algorithm-name: local-date-time-dynamic-year-quarter
          kl_account_info:
            actual-data-nodes: ds0.kl_account_info_$->{0..7}
            table-strategy:
              standard:
                sharding-column: business_organization_no
                sharding-algorithm-name: kl-account-info-table-inline
          kl_organization_fee_detail:
            actual-data-nodes: ds0.kl_organization_fee_detail_$->{202505..203512}
            table-strategy:
              standard:
                sharding-column: transaction_datetime
                sharding-algorithm-name: local-date-time-dynamic-year-month
        sharding-algorithms:
          kl-organization-customer-card-operation-record-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_organization_customer_card_operation_record_${Long.parseLong(organization_no.replaceAll("[^0-9]", "")) % 8}
          kl-organization-customer-card-info-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_organization_customer_card_info_${Long.parseLong(organization_no.replaceAll("[^0-9]", "")) % 8}
          kl-organization-customer-account-info-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_organization_customer_account_info_${Long.parseLong(organization_no.replaceAll("[^0-9]", "")) % 8}
          kl-organization-customer-wallet-operation-record-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_organization_customer_wallet_operation_record_${Long.parseLong(organization_no.replaceAll("[^0-9]", "")) % 8}
          kl-organization-customer-wallet-info-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_organization_customer_wallet_info_${Long.parseLong(organization_no.replaceAll("[^0-9]", "")) % 8}
          kl-mpc-wallet-webhook-record-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_mpc_wallet_webhook_record_${Long.parseLong(organization_no.replaceAll("[^0-9]", "")) % 8}
          local-date-time-dynamic-year-month:
            type: CLASS_BASED
            props:
              strategy: standard
              algorithmClassName: com.kun.linkage.common.db.sharding.algorithm.LocalDateTimeDynamicYearMonthShardingAlgorithm
              sharding-suffix-pattern: yyyyMM
          local-date-time-dynamic-year-quarter:
            type: CLASS_BASED
            props:
              strategy: standard
              algorithmClassName: com.kun.linkage.common.db.sharding.algorithm.LocalDateTimeDynamicYearQuarterShardingAlgorithm
          kl-account-info-table-inline:
            type: INLINE
            props:
              algorithm-expression: kl_account_info_${Long.parseLong(business_organization_no.replaceAll("[^0-9]", "")) % 8}

  redis:
    host: redis.qa.kun
    port: 6379
    #    password:
    database: 1
    timeout: 10000
    # 连接池
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大阻塞等待时间(负数表示没限制)
        max-wait: -1
        # 最小空闲
        min-idle: 0
        # 最大空闲
        max-idle: 8

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.kun.linkage.common.db.entity

rocketmq:
  name-server: mq.dev.kun:9876
  producer:
    group: KL_CUSTOMER_GROUP
#     # 发送消息超时时间，默认 3000
#     send-message-timeout: 3000
#     # 发送消息失败重试次数，默认2
#     retryTimesWhenSendFailed: 2
#     # 发送异步消息失败重试次数，默认2
#     retryTimesWhenSendAsyncFailed: 2

xxl:
  job:
    admin:
      addresses: http://dev.kun.global/xxl-job-admin/
    accessToken: default_token
    executor:
      appname: kun-linkage-customer-executor
      address:
      ip:
      port: 16661
      logpath: /apps/xxl/jobhandler
      logretentiondays: 10

vcc:
  boss:
    interceptor:
      enabled: false
    account:
      jwtExpiresSecond: 3600000 # 1hour
      jwtSecretString: vcc-boss
      #是否启动Filter过滤
      StartFilter: true
      #是否启动记录Filter所有日志
      StartFilterAllLog: true
springdoc:
  api-docs:
    # 开启api-docs
    enabled: true
# 秘钥管理
kl:
  key-management:
    db-sensitive-info-aes-key: 6DBqQZV1MeT5qIVon+9JnA3Q70v7c6l/3cBWCnZlCGFISP6b1IqsZG0vq9vtmoOs
  mpcWalletMock: true
  # MPC充值机构记账开关,true开启,false关闭
  mpcOrganizationBookkeepSwitch: true

lark:
  config:
    enabled: true
    webhook: https://open.larksuite.com/open-apis/bot/v2/hook/11bfca2c-d692-4ede-b555-0d9012153245
    secret: 00GUV0yrJwy75QsdnyfDU

kun:
  aws:
    s3:
      open: true
      needSk: true
      accessKey: ********************
      secretKey: 4w5hC8ywH+6JJbQhtd98WGzZeMzetV5s0rXqxHkf
      region: ap-east-1
      bucket: qa-aws-static-s3
      endpoint: https://qa-aws-static-s3.s3.ap-east-1.amazonaws.com
      fileFolder: /kl-static-file