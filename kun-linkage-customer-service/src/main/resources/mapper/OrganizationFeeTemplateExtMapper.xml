<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationFeeTemplateExtMapper">

    <select id="listOrganizationFeeTemplateByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateVO">
        select *
        from kl_organization_fee_template
        <where>
            <if test="templateNo != null and templateNo != ''">
                and template_no = #{templateNo}
            </if>
        </where>
        order by last_modify_time desc
    </select>

    <select id="listOrganizationFeeTemplateReviewRecordByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateReviewRecordVO">
        select *
        from kl_organization_fee_template_review_record
        <where>
            <if test="templateNo != null and templateNo != ''">
                and template_no = #{templateNo}
            </if>
            <if test="reviewId != null">
                and review_id = #{reviewId}
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and review_status = #{reviewStatus}
            </if>
            <if test="submitStartTime != null">
                and submit_time &gt;= #{submitStartTime}
            </if>
            <if test="submitEndTime != null">
                and submit_time &lt;= #{submitEndTime}
            </if>
            <if test="reviewStartTime != null">
                and review_time &gt;= #{reviewStartTime}
            </if>
            <if test="reviewEndTime != null">
                and review_time &lt;= #{reviewEndTime}
            </if>
            <if test="operatorType != null and operatorType != ''">
                and operator_type = #{operatorType}
            </if>
        </where>
        order by submit_time desc
    </select>
</mapper>