<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationCustomerAccountInfoExtMapper">

    <select id="listOrganizationCustomerAccountInfoByWhere" resultType="com.kun.linkage.customer.facade.vo.OrganizationCustomerAccountInfoVO">
        select ocai.*, obi.organization_name,kai.available_amount,kai.currency_precision
        ,kai.total_balance_amount,kai.frozen_amount,kai.minimum_amount
        from kl_organization_customer_account_info ocai
                 inner join kl_account_info kai on  kai.business_organization_no = ocai.organization_no and ocai.account_no = kai.account_no
                 left join kl_organization_basic_info obi on ocai.organization_no = obi.organization_no
         where  ocai.organization_no = #{organizationNo}
            <if test="accountNo != null and accountNo != ''">
                and ocai.account_no = #{accountNo}
            </if>
            <if test="customerId != null and customerId != ''">
                and ocai.customer_id = #{customerId}
            </if>
            <if test="accountType != null and accountType != ''">
                and ocai.account_type = #{accountType}
            </if>
            <if test="status != null and status != ''">
                and ocai.status = #{status}
            </if>
            <if test="maxAvailableAmount != null">
                and kai.available_amount &lt; #{maxAvailableAmount}
            </if>
            <if test="mixAvailableAmount != null">
                and kai.available_amount &gt;= #{mixAvailableAmount}
            </if>
        order by ocai.last_modify_time desc
    </select>

    <select id="getOrganizationCustomerAccountInfoByOrganizationNoAndId" resultType="com.kun.linkage.customer.facade.vo.OrganizationCustomerAccountDetailVO">
        select ocai.*, obi.organization_name
        from kl_organization_customer_account_info ocai
                 left join kl_organization_basic_info obi on ocai.organization_no = obi.organization_no
        where ocai.id = #{id}
        and ocai.organization_no = #{organizationNo}
    </select>
</mapper>