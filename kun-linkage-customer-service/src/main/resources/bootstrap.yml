# Spring
spring:
  application:
    # 应用名称
    name: kun-linkage-customer
  profiles:
    # 环境配置
    active: local
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    servlet:
      path: /linkage-customer
  sleuth:
    # 采样率配置
    sampler:
      probability: 1.0
    # 是否启用web
    web:
      enabled: true
    # 是否启用消息
    messaging:
      enabled: true
    # 是否启用rpc
    rpc:
      enabled: true
    # 是否启用redis
    redis:
      enabled: true
    # 是否启用jdbc
    jdbc:
      enabled: true
# 配置 Spring Boot Actuator 管理相关的设置
management:
  # 配置 Actuator 的端点
  endpoints:
    # 配置 Web 端点的相关设置
    web:
      # 配置 Web 端点的暴露规则
      exposure:
        # 包含所有的端点，即所有端点都可以通过 Web 访问
        include: '*'
  # 配置单个端点的特定设置
  endpoint:
    # 配置健康检查端点
    health:
      # 始终显示健康检查的详细信息
      show-details: always