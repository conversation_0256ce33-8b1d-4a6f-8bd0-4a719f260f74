package com.kun.linkage.customer.service.kyc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.base.utils.SensitiveInfoUtil;
import com.kun.linkage.common.db.entity.CardProductInfo;
import com.kun.linkage.common.db.entity.CustomerKycAuditRecord;
import com.kun.linkage.common.db.entity.CustomerKycLevel1Info;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.mapper.CardProductInfoMapper;
import com.kun.linkage.customer.constant.CustomerServiceApplicationConstant;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycLevel1Req;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycLevel2Req;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycLevel3Req;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycSubmissionReq;
import com.kun.linkage.customer.facade.api.bean.res.kyc.CustomerKycSubmissionRes;
import com.kun.linkage.customer.facade.api.bean.res.kyc.KycNotifyRes;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.CaseStatusEnum;
import com.kun.linkage.customer.facade.enums.KycCaseTypeEnum;
import com.kun.linkage.customer.facade.enums.KycLevelEnum;
import com.kun.linkage.customer.service.OrganizationCustomerBasicInfoBizService;
import com.kun.linkage.customer.service.organization.OrganizationBasicInfoBizService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;


@Slf4j
@Service
public class CustomerKycSubmissionService {

    @Resource
    private OrganizationBasicInfoBizService organizationBasicInfoBizService;
    @Resource
    private CustomerKycAuditService customerKycAuditService;
    @Resource
    private CustomerKycLevel1Service customerKycLevel1Service;
    @Resource
    private CustomerKycLevel2Service customerKycLevel2Service;
    @Resource
    private CustomerKycLevel3Service customerKycLevel3Service;
    @Resource
    private OrganizationCustomerBasicInfoBizService organizationCustomerBasicInfoBizService;
    @Resource
    private CardProductInfoMapper cardProductInfoMapper;


    /**
     * kyc信息报送
     * @param customerKycSubmissionReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<CustomerKycSubmissionRes> kycSubmission(CustomerKycSubmissionReq customerKycSubmissionReq) {

        String requestNo = customerKycSubmissionReq.getRequestNo();
        String customerId = customerKycSubmissionReq.getCustomerId();
        String organizationNo = customerKycSubmissionReq.getOrganizationNo();

        CustomerKycLevel1Req customerKycLevel1 = customerKycSubmissionReq.getCustomerKycLevel1();
        if(null == customerKycLevel1) {
            log.error("KYC信息报送一级信息不能为空");
            return Result.fail(CommonTipConstant.REQUEST_PARAM_MISSING);
        }

        String kycLevel = customerKycSubmissionReq.getKycLevel();
        if(KycLevelEnum.LEVEL_0.getValue().equals(kycLevel)) {
            log.error("KYC信息报送等级不能是0级;customerId:{},kycLevel:{}", customerId, kycLevel);
            return Result.fail(CommonTipConstant.REQUEST_PARAM_ERROR);
        }


        KycLevelEnum kycLevelEnum = KycLevelEnum.fromValue(kycLevel);
        if(null == kycLevelEnum){
            log.error("风险等级入参错误;customerId:{},kycLevel:{}", customerId, kycLevel);
            return Result.fail(CommonTipConstant.REQUEST_PARAM_ERROR);
        }

        //校验机构是否存在
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoBizService.selectOrganizationBasicInfoByOrganization(organizationNo);
        if(null == organizationBasicInfo){
            log.error("机构不存在organizationNo:{}", organizationNo);
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        //不需要报送的直接报错
        Integer isKycReported = organizationBasicInfo.getIsKycReported();
        if(YesFlagEnum.NO.getNumValue().equals(isKycReported)){
            log.error("机构不需要报送信息;organizationNo:{},customerId:{}", organizationNo, customerId);
            return Result.fail(CustomerTipConstant.ORGANIZATION_KYC_NO_REPORTED);
        }

        CustomerKycAuditRecord selectCustomerKycAuditRecord = customerKycAuditService.selectCustomerKycAuditRecordByCustomerId(requestNo, customerId, organizationNo);
        if(null != selectCustomerKycAuditRecord){
            log.info("机构客户已经报送过数据");
            CustomerKycSubmissionRes customerKycSubmissionRes = new CustomerKycSubmissionRes();
            customerKycSubmissionRes.setCaseNo(selectCustomerKycAuditRecord.getCaseNo());
            customerKycSubmissionRes.setIsKycVerified(organizationBasicInfo.getIsKycVerified());
            return Result.success(customerKycSubmissionRes);
        }

        //卡产品查询
        String cardProductNo = customerKycSubmissionReq.getCardProductNo();
        String cardProductName = null;
        if(StringUtils.isNotBlank(cardProductNo)){
            LambdaQueryWrapper<CardProductInfo> queryWrapper = Wrappers.<CardProductInfo>lambdaQuery();
            queryWrapper.eq(CardProductInfo::getCardProductCode, cardProductNo)
                    .eq(CardProductInfo::getStatus, ValidStatusEnum.VALID.getValue());
            CardProductInfo cardProductInfo = cardProductInfoMapper.selectOne(queryWrapper);

            if(null == cardProductInfo){
                log.error("无效的卡产品编号,cardProductNo:{}", cardProductNo);
                return Result.fail(CustomerTipConstant.CARD_PRODUCT_NOT_FOUND);
            }

            cardProductName = cardProductInfo.getCardProductName();
        }

        //查看是否有审核中的数据,有审核中的数据提示
        Boolean b = customerKycAuditService.countCustomerKycAuditRecordByCustomerId(customerId, organizationNo, CaseStatusEnum.PENDING);
        if(b){
            log.error("存在审核中的数据;customerId:{},organizationNo:{}", customerId, organizationNo);
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }
        //获取一级信息
        // 同一个商户下证件类型+证件号不可重复，必须唯一
        String idType = customerKycLevel1.getIdType();
        //外部传入的加密的证件号
        String idNo = customerKycLevel1.getIdNo();
        //String maskedIdNo = customerKycLevel1.getMaskedIdNo();
        //解密证件号
        String decryptIdNo = SensitiveInfoUtil.decryptCBC(idNo, organizationBasicInfo.getSensitiveKey());
        String maskedIdNo = SensitiveInfoUtil.mask(decryptIdNo, 1, 4, '*');
        //内部使用的加密证件号
        String innerEncryptIdNo = SensitiveInfoUtil.innerEncrypt(decryptIdNo);
        Long totalUserId = customerKycAuditService.countByIdTypeAndIdNo(organizationNo, idType, innerEncryptIdNo, customerId,null,null,null,cardProductNo);
        if(totalUserId > 0){
            log.error("用户证件类型+证件号已经存在;organizationNo:{},idType:{},customerId:{},maskedIdNo:{},cardProductNo:{}", organizationNo, idType, customerId,maskedIdNo,cardProductNo);
            return Result.fail(CustomerTipConstant.CERTIFICATE_INFORMATION_EXISTS);
        }
        // 同一个商户下邮箱不可重复，必须唯一
        String email = customerKycLevel1.getEmail();
        Long totalEmail = customerKycAuditService.countByIdTypeAndIdNo(organizationNo, null, null, customerId,email,null,null,null);
        if(totalEmail > 0){
            log.error("用户邮箱已经存在;organizationNo:{},customerId:{},email:{}", organizationNo, customerId,email);
            return Result.fail(CustomerTipConstant.EMAIL_EXISTS);
        }
        //同一个商户下手机区号+手机号必须唯一
        String phoneArea = customerKycLevel1.getPhoneArea();
        String mobileNo = customerKycLevel1.getMobileNo();
        if(StringUtils.isNotBlank(phoneArea) && StringUtils.isNotBlank(mobileNo)){
            phoneArea = phoneArea.replace("+", "");
            Long totalMobile = customerKycAuditService.countByIdTypeAndIdNo(organizationNo, null, null, customerId,null,phoneArea,mobileNo,null);
            if(totalMobile > 0){
                log.error("用户手机号已经存在;organizationNo:{},customerId:{},phoneArea:{},mobileNo:{}", organizationNo, customerId,phoneArea,mobileNo);
                return Result.fail(CustomerTipConstant.MOBILE_EXISTS);
            }
        }

        String caseType = customerKycSubmissionReq.getCaseType();

        //如果案件类型是申卡前置报送,KYC报送需要查询是否有报送通过记录,如果有的话提示已经报送请通过修改报送
        if(KycCaseTypeEnum.KYC_REPORT.getValue().equals(caseType) || KycCaseTypeEnum.PRE_CARD_APPLICATION.getValue().equals(caseType)){
            CustomerKycLevel1Info customerKycLevel1Info = customerKycLevel1Service.selectCustomerKycLevel1InfoByOrgNoAndCustomerId(organizationNo, customerId);
            if(null!= customerKycLevel1Info && StringUtils.isNotBlank(customerKycLevel1Info.getCaseNo())){
                log.error("不能重复报送;organizationNo:{},customerId:{},caseType:{}", organizationNo, customerId,caseType);
                return Result.fail(CustomerTipConstant.CERTIFICATE_INFORMATION_EXISTS);
            }

        }

        String lastName = customerKycLevel1.getLastName();
        String firstName = customerKycLevel1.getFirstName();
        String middleName = customerKycLevel1.getMiddleName();
        String cardholderName = lastName + middleName + firstName;
        String caseNo = String.valueOf(IdWorker.getId());

        CustomerKycAuditRecord customerKycAuditRecord = new CustomerKycAuditRecord();
        customerKycAuditRecord.setRequestNo(requestNo);
        customerKycAuditRecord.setCustomerId(customerId);
        customerKycAuditRecord.setOrganizationNo(organizationNo);
        customerKycAuditRecord.setCaseNo(caseNo);
        customerKycAuditRecord.setCaseType(caseType);
        customerKycAuditRecord.setKycLevel(kycLevel);
        customerKycAuditRecord.setCardholderName(cardholderName);
        customerKycAuditRecord.setIdType(idType);
        customerKycAuditRecord.setIdNo(innerEncryptIdNo);
        customerKycAuditRecord.setCardProductNo(customerKycSubmissionReq.getCardProductNo());
        customerKycAuditRecord.setCardProductName(cardProductName);
        customerKycAuditRecord.setEmail(email);
        customerKycAuditRecord.setPhoneArea(phoneArea);
        customerKycAuditRecord.setMobileNo(mobileNo);
        Date date = new Date();
        //是否个人客户kyc校验;0否;1:是；如果是否的话直接进入正式表
        Integer isKycVerified = organizationBasicInfo.getIsKycVerified();
        //保存审核数据数据
        customerKycAuditService.saveCustomerKycAuditRecord(customerKycAuditRecord,isKycVerified,date);
        //保存一级信息
        customerKycLevel1Service.saveCustomerKycLevel1Record(customerKycLevel1,caseNo,organizationNo,customerId,innerEncryptIdNo,date);

        //保存二级信息
        boolean updateLevel2 = false;
        CustomerKycLevel2Req customerKycLevel2 = customerKycSubmissionReq.getCustomerKycLevel2();
        if(null != customerKycLevel2){
            customerKycLevel2Service.saveCustomerKycLevel2Record(customerKycLevel2,caseNo,organizationNo,customerId,date);
            updateLevel2 = true;
        }
        //保存三级信息
        boolean updateLevel3 = false;
        CustomerKycLevel3Req customerKycLevel3 = customerKycSubmissionReq.getCustomerKycLevel3();
        if(null != customerKycLevel3){
            customerKycLevel3Service.saveCustomerKycLevel3Record(customerKycLevel3,caseNo,organizationNo,customerId,date);
            updateLevel3 = true;
        }
        //默认是成功直接把数据存到正式表
        if(YesFlagEnum.NO.getNumValue().equals(isKycVerified)){
            //1.0 更新正式表数据
            customerKycLevel1Service.saveOrUpdateCustomerKycLevel1Info(caseNo,organizationNo,customerId);
            if(updateLevel2){
                customerKycLevel2Service.saveOrUpdateCustomerKycLevel2Info(caseNo,organizationNo,customerId);
            }
            if(updateLevel3){
                customerKycLevel3Service.saveOrUpdateCustomerKycLevel3Info(caseNo,organizationNo,customerId);
            }

            //2.0 新增或者更新机构用户基础数据
            Integer level = kycLevelEnum.getLevel();
            if(null != level){
                organizationCustomerBasicInfoBizService.insetOrUpdateOrganizationCustomerBasicInfo(organizationNo,caseNo,level);
            }

        }

        CustomerKycSubmissionRes customerKycSubmissionRes = new CustomerKycSubmissionRes();
        customerKycSubmissionRes.setCaseNo(caseNo);
        customerKycSubmissionRes.setIsKycVerified(isKycVerified);
        return Result.success(customerKycSubmissionRes);
    }


    /**
     * 客户kyc信息申报查询
     * @param caseNo 案件号
     * @param customerId 客户id
     * @param organizationNo 机构号
     * @return
     */
    @Cacheable(value = CustomerServiceApplicationConstant.APPLICATION_NAME,key = "'CustomerKycSubmissionSelect:' + #caseNo +':' + #customerId + ':'+ #organizationNo")
    public Result<KycNotifyRes> selectCaseResult(String caseNo, String customerId, String organizationNo) {
        //查询缓存数据
        KycNotifyRes kycNotifyRes = new KycNotifyRes();
        CustomerKycAuditRecord customerKycAuditRecord = customerKycAuditService.selectByCaseNoAndCustomerIdAndOrg(caseNo, customerId, organizationNo);

        if(null == customerKycAuditRecord){
            log.error("暂无报送数据:caseNo:{},customerId:{},organizationNo:{}", caseNo, customerId, organizationNo);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }

        kycNotifyRes.setOrganizationNo(organizationNo);
        kycNotifyRes.setCustomerId(customerId);
        kycNotifyRes.setCaseNo(caseNo);
        String caseStatus = customerKycAuditRecord.getCaseStatus();
        if(StringUtils.isNotBlank(caseStatus)){
            if(CaseStatusEnum.NONE.getCode().equals(caseStatus)){
                caseStatus = CaseStatusEnum.APPROVED.getCode();
            }
        }
        kycNotifyRes.setCaseResult(caseStatus);
        kycNotifyRes.setAuditResultDescription(customerKycAuditRecord.getAuditDescription());

        return Result.success(kycNotifyRes);

    }
}
