package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeDetailPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeDetailVO;
import com.kun.linkage.customer.service.organization.OrganizationFeeDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 手续费明细相关接口
 * </p>
 */
@Tag(name = "手续费明细相关接口", description = "手续费明细相关接口")
@RestController
@RequestMapping("/boss/organizationFeeDetail")
public class BossOrganizationFeeDetailController {

    @Resource
    private OrganizationFeeDetailService organizationFeeDetailService;

    /**
     * 分页查询机构手续费明细信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(description = "分页查询机构手续费明细信息", summary = "分页查询机构手续费明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFeeDetail"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationFeeDetailVO>> pageList(@Validated OrganizationFeeDetailPageQueryDTO req) {
        PageResult<OrganizationFeeDetailVO> list = organizationFeeDetailService.pageListByBOSS(req);
        return Result.success(list);
    }
}
