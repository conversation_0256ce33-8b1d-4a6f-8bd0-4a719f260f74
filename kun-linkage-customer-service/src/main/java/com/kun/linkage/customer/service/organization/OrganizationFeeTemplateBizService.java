package com.kun.linkage.customer.service.organization;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.boss.support.vo.VccBossUserVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.enums.OperationTypeEnum;
import com.kun.linkage.common.base.enums.ReviewStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.OrganizationFeeTemplate;
import com.kun.linkage.common.db.entity.OrganizationFeeTemplateDetail;
import com.kun.linkage.common.db.entity.OrganizationFeeTemplateDetailReviewRecord;
import com.kun.linkage.common.db.entity.OrganizationFeeTemplateReviewRecord;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateDetailMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateDetailReviewRecordMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateReviewRecordMapper;
import com.kun.linkage.customer.ext.mapper.OrganizationFeeTemplateExtMapper;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplatePageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.fee.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构费率模版服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class OrganizationFeeTemplateBizService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationFeeTemplateBizService.class);
    @Resource
    private OrganizationFeeTemplateMapper organizationFeeTemplateMapper;
    @Resource
    private OrganizationFeeTemplateExtMapper organizationFeeTemplateExtMapper;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeTemplateReviewRecordMapper organizationFeeTemplateReviewRecordMapper;
    @Resource
    private OrganizationFeeTemplateDetailReviewRecordMapper organizationFeeTemplateDetailReviewRecordMapper;

    /**
     * 分页查询机构费率模版列表
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationFeeTemplateVO> pageList(OrganizationFeeTemplatePageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationFeeTemplateExtMapper.listOrganizationFeeTemplateByWhere(dto));
    }

    /**
     * 查询所有有效机构费率模版列表
     *
     * @return
     */
    public List<OrganizationFeeTemplateVO> allValidList() {
        List<OrganizationFeeTemplate> organizationFeeTemplateList = organizationFeeTemplateMapper
                .selectList(Wrappers.<OrganizationFeeTemplate>lambdaQuery().eq(OrganizationFeeTemplate::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationFeeTemplateList != null && !organizationFeeTemplateList.isEmpty()) {
            return BeanUtil.copyToList(organizationFeeTemplateList, OrganizationFeeTemplateVO.class);
        }
        return Collections.emptyList();
    }

    /**
     * 查询机构费率模版明细信息
     *
     * @param templateNo
     * @return
     */
    public Result<OrganizationFeeTemplateAndDetailVO> detail(String templateNo) {
        OrganizationFeeTemplateAndDetailVO organizationFeeTemplateAndDetailVO = new OrganizationFeeTemplateAndDetailVO();
        OrganizationFeeTemplate organizationFeeTemplate = organizationFeeTemplateMapper.selectById(templateNo);
        if (organizationFeeTemplate == null) {
            log.error("机构费率模版不存在, 模版号:{}", templateNo);
            return Result.fail(CustomerTipConstant.FEE_TEMPLATE_NOT_FOUND);
        }
        BeanUtil.copyProperties(organizationFeeTemplate, organizationFeeTemplateAndDetailVO);
        List<OrganizationFeeTemplateDetailVO> organizationFeeTemplateDetailVOList = new ArrayList<>();
        List<OrganizationFeeTemplateDetail> organizationFeeTemplateDetailList =
                organizationFeeTemplateDetailMapper.selectList(Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery().eq(OrganizationFeeTemplateDetail::getTemplateNo, templateNo));
        if (organizationFeeTemplateDetailList != null && !organizationFeeTemplateDetailList.isEmpty()) {
            // 进行分组
            Function<OrganizationFeeTemplateDetail, List<String>> compositeKey = item ->
                    Arrays.asList(item.getFeeType(), item.getBillingDimension(), item.getCollectionMethod(), item.getCurrencyCode());
            Map<List<String>, List<OrganizationFeeTemplateDetail>> collect =
                    organizationFeeTemplateDetailList.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));
            collect.forEach((item, organizationFeeTemplateDetails) -> {
                OrganizationFeeTemplateDetailVO organizationFeeTemplateDetailVO = new OrganizationFeeTemplateDetailVO();
                organizationFeeTemplateDetailVO.setFeeType(item.get(0));
                organizationFeeTemplateDetailVO.setBillingDimension(item.get(1));
                organizationFeeTemplateDetailVO.setCollectionMethod(item.get(2));
                organizationFeeTemplateDetailVO.setCurrencyCode(item.get(3));
                List<OrganizationFeeTemplateDetailValueVO> organizationFeeTemplateDetailValueList =
                        BeanUtil.copyToList(organizationFeeTemplateDetails, OrganizationFeeTemplateDetailValueVO.class);
                organizationFeeTemplateDetailVO.setOrganizationFeeTemplateDetailValueList(organizationFeeTemplateDetailValueList);
                organizationFeeTemplateDetailVOList.add(organizationFeeTemplateDetailVO);
            });
            // 按照费率类型进行排序
            organizationFeeTemplateDetailVOList.sort((o1, o2) -> {
                int value1 = Integer.parseInt(o1.getFeeType());
                int value2 = Integer.parseInt(o2.getFeeType());
                return Integer.compare(value1, value2);
            });
        }
        organizationFeeTemplateAndDetailVO.setOrganizationFeeTemplateDetailList(organizationFeeTemplateDetailVOList);
        return Result.success(organizationFeeTemplateAndDetailVO);
    }

    /**
     * 提交新增机构费率模版
     *
     * @param dto
     * @return
     */
    @Transactional
    public Result<Void> addSubmit(OrganizationFeeTemplateAddSubmitDTO dto, VccBossUserVO bossOperator) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeTemplateReviewRecord organizationFeeTemplateReviewRecord = new OrganizationFeeTemplateReviewRecord();
        BeanUtil.copyProperties(dto, organizationFeeTemplateReviewRecord);
        organizationFeeTemplateReviewRecord.setOperatorType(OperationTypeEnum.ADD.getValue());
        organizationFeeTemplateReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationFeeTemplateReviewRecord.setSubmitTime(now);
        organizationFeeTemplateReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationFeeTemplateReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationFeeTemplateReviewRecordMapper.insert(organizationFeeTemplateReviewRecord);
        // 将明细存入明细审核记录中
        dto.getOrganizationFeeTemplateDetailList().forEach(organizationFeeTemplateDetail -> {
            organizationFeeTemplateDetail.getOrganizationFeeTemplateDetailValueList().forEach(organizationFeeTemplateDetailValue -> {
                OrganizationFeeTemplateDetailReviewRecord organizationFeeTemplateDetailReviewRecord = new OrganizationFeeTemplateDetailReviewRecord();
                BeanUtil.copyProperties(organizationFeeTemplateDetailValue, organizationFeeTemplateDetailReviewRecord);
                organizationFeeTemplateDetailReviewRecord.setFeeType(organizationFeeTemplateDetail.getFeeType());
                organizationFeeTemplateDetailReviewRecord.setBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
                organizationFeeTemplateDetailReviewRecord.setCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());
                organizationFeeTemplateDetailReviewRecord.setCurrencyCode(organizationFeeTemplateDetail.getCurrencyCode());
                organizationFeeTemplateDetailReviewRecord.setOperatorType(OperationTypeEnum.ADD.getValue());
                organizationFeeTemplateDetailReviewRecord.setReviewId(organizationFeeTemplateReviewRecord.getReviewId());
                organizationFeeTemplateDetailReviewRecord.setCreateTime(now);
                organizationFeeTemplateDetailReviewRecordMapper.insert(organizationFeeTemplateDetailReviewRecord);
            });
        });
        return Result.success();
    }

    /**
     * 提交修改机构费率模版
     *
     * @param dto
     * @return
     */
    public Result<Void> modifySubmit(OrganizationFeeTemplateModifySubmitDTO dto, VccBossUserVO bossOperator) {
        // 校验待修改的数据是否存在
        OrganizationFeeTemplate organizationFeeTemplate = organizationFeeTemplateMapper.selectById(dto.getTemplateNo());
        if (organizationFeeTemplate == null) {
            log.error("机构费率模版不存在, 模版号:{}", dto.getTemplateNo());
            return Result.fail(CustomerTipConstant.FEE_TEMPLATE_NOT_FOUND);
        }
        // 校验是否存在待审核数据
        Long num = organizationFeeTemplateReviewRecordMapper.selectCount(Wrappers.<OrganizationFeeTemplateReviewRecord>lambdaQuery()
                .eq(OrganizationFeeTemplateReviewRecord::getTemplateNo, dto.getTemplateNo())
                .eq(OrganizationFeeTemplateReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (num != null && num > 0) {
            log.error("已存在待审核的数据, 模版号:{}", dto.getTemplateNo());
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeTemplateReviewRecord organizationFeeTemplateReviewRecord = new OrganizationFeeTemplateReviewRecord();
        BeanUtil.copyProperties(dto, organizationFeeTemplateReviewRecord);
        organizationFeeTemplateReviewRecord.setTemplateNo(organizationFeeTemplate.getTemplateNo());
        organizationFeeTemplateReviewRecord.setTemplateName(organizationFeeTemplate.getTemplateName());
        organizationFeeTemplateReviewRecord.setStatus(organizationFeeTemplate.getStatus());
        organizationFeeTemplateReviewRecord.setOperatorType(OperationTypeEnum.MODIFY.getValue());
        organizationFeeTemplateReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationFeeTemplateReviewRecord.setSubmitTime(now);
        organizationFeeTemplateReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationFeeTemplateReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationFeeTemplateReviewRecordMapper.insert(organizationFeeTemplateReviewRecord);
        // 将明细存入明细审核记录中
        dto.getOrganizationFeeTemplateDetailList().forEach(organizationFeeTemplateDetail -> {
            organizationFeeTemplateDetail.getOrganizationFeeTemplateDetailValueList().forEach(organizationFeeTemplateDetailValue -> {
                OrganizationFeeTemplateDetailReviewRecord organizationFeeTemplateDetailReviewRecord = new OrganizationFeeTemplateDetailReviewRecord();
                BeanUtil.copyProperties(organizationFeeTemplateDetailValue, organizationFeeTemplateDetailReviewRecord);
                organizationFeeTemplateDetailReviewRecord.setFeeType(organizationFeeTemplateDetail.getFeeType());
                organizationFeeTemplateDetailReviewRecord.setBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
                organizationFeeTemplateDetailReviewRecord.setCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());
                organizationFeeTemplateDetailReviewRecord.setCurrencyCode(organizationFeeTemplateDetail.getCurrencyCode());
                organizationFeeTemplateDetailReviewRecord.setOperatorType(OperationTypeEnum.MODIFY.getValue());
                organizationFeeTemplateDetailReviewRecord.setTemplateNo(dto.getTemplateNo());
                organizationFeeTemplateDetailReviewRecord.setReviewId(organizationFeeTemplateReviewRecord.getReviewId());
                organizationFeeTemplateDetailReviewRecord.setCreateTime(now);
                organizationFeeTemplateDetailReviewRecordMapper.insert(organizationFeeTemplateDetailReviewRecord);
            });
        });
        return Result.success();
    }

    /**
     * 审核机构费率模版
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> review(ReviewDTO dto, VccBossUserVO bossOperator) {
        OrganizationFeeTemplateReviewRecord organizationFeeTemplateReviewRecord =
                organizationFeeTemplateReviewRecordMapper.selectOne(
                        Wrappers.<OrganizationFeeTemplateReviewRecord>lambdaQuery()
                                .eq(OrganizationFeeTemplateReviewRecord::getReviewId, dto.getReviewId())
                                .eq(OrganizationFeeTemplateReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (organizationFeeTemplateReviewRecord == null) {
            log.error("机构费率模版待审核记录不存在, 审核记录Id:{}", dto.getReviewId());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeTemplateReviewRecord modifyRecord = new OrganizationFeeTemplateReviewRecord();
        modifyRecord.setReviewId(dto.getReviewId());
        modifyRecord.setReviewStatus(dto.getReviewStatus());
        if (StringUtils.isNotBlank(dto.getReviewReason())) {
            modifyRecord.setReviewReason(dto.getReviewReason());
        }
        modifyRecord.setReviewTime(now);
        modifyRecord.setReviewUserId(String.valueOf(bossOperator.getId()));
        modifyRecord.setReviewUserName(bossOperator.getUsername());
        if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.REJECT.getValue())) {
            organizationFeeTemplateReviewRecordMapper.updateById(modifyRecord);
        } else if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.PASS.getValue())) {
            if (StringUtils.equals(OperationTypeEnum.ADD.getValue(), organizationFeeTemplateReviewRecord.getOperatorType())) {
                // 费用基本信息
                OrganizationFeeTemplate organizationFeeTemplate = new OrganizationFeeTemplate();
                BeanUtil.copyProperties(organizationFeeTemplateReviewRecord, organizationFeeTemplate);
                organizationFeeTemplate.setCreateTime(now);
                organizationFeeTemplate.setCreateUserId(String.valueOf(bossOperator.getId()));
                organizationFeeTemplate.setCreateUserName(bossOperator.getUsername());
                organizationFeeTemplate.setLastModifyTime(now);
                organizationFeeTemplate.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationFeeTemplate.setLastModifyUserName(bossOperator.getUsername());
                organizationFeeTemplateMapper.insert(organizationFeeTemplate);
                // 费用明细信息
                List<OrganizationFeeTemplateDetailReviewRecord> organizationFeeTemplateDetailReviewRecordList = organizationFeeTemplateDetailReviewRecordMapper.selectList(
                        Wrappers.<OrganizationFeeTemplateDetailReviewRecord>lambdaQuery().eq(OrganizationFeeTemplateDetailReviewRecord::getReviewId, dto.getReviewId()));
                if (organizationFeeTemplateDetailReviewRecordList != null && !organizationFeeTemplateDetailReviewRecordList.isEmpty()) {
                    organizationFeeTemplateDetailReviewRecordList.forEach(organizationFeeTemplateDetailReviewRecord -> {
                        OrganizationFeeTemplateDetail organizationFeeTemplateDetail = new OrganizationFeeTemplateDetail();
                        BeanUtil.copyProperties(organizationFeeTemplateDetailReviewRecord, organizationFeeTemplateDetail);
                        organizationFeeTemplateDetail.setTemplateNo(organizationFeeTemplate.getTemplateNo());
                        organizationFeeTemplateDetail.setCreateTime(now);
                        organizationFeeTemplateDetail.setCreateUserId(String.valueOf(bossOperator.getId()));
                        organizationFeeTemplateDetail.setCreateUserName(bossOperator.getUsername());
                        organizationFeeTemplateDetail.setLastModifyTime(now);
                        organizationFeeTemplateDetail.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                        organizationFeeTemplateDetail.setLastModifyUserName(bossOperator.getUsername());
                        organizationFeeTemplateDetailMapper.insert(organizationFeeTemplateDetail);
                    });
                }
            } else {
                // 修改
                OrganizationFeeTemplate organizationFeeTemplate = new OrganizationFeeTemplate();
                BeanUtil.copyProperties(organizationFeeTemplateReviewRecord, organizationFeeTemplate);
                organizationFeeTemplate.setLastModifyTime(now);
                organizationFeeTemplate.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationFeeTemplate.setLastModifyUserName(bossOperator.getUsername());
                organizationFeeTemplateMapper.updateById(organizationFeeTemplate);
                // 费用明细信息
                List<OrganizationFeeTemplateDetailReviewRecord> organizationFeeTemplateDetailReviewRecordList = organizationFeeTemplateDetailReviewRecordMapper.selectList(
                        Wrappers.<OrganizationFeeTemplateDetailReviewRecord>lambdaQuery().eq(OrganizationFeeTemplateDetailReviewRecord::getReviewId, dto.getReviewId()));
                if (organizationFeeTemplateDetailReviewRecordList != null && !organizationFeeTemplateDetailReviewRecordList.isEmpty()) {
                    // 先删正式表的数据后再新增
                    organizationFeeTemplateDetailMapper.delete(Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                            .eq(OrganizationFeeTemplateDetail::getTemplateNo, organizationFeeTemplate.getTemplateNo()));
                    organizationFeeTemplateDetailReviewRecordList.forEach(organizationFeeTemplateDetailReviewRecord -> {
                        OrganizationFeeTemplateDetail organizationFeeTemplateDetail = new OrganizationFeeTemplateDetail();
                        BeanUtil.copyProperties(organizationFeeTemplateDetailReviewRecord, organizationFeeTemplateDetail);
                        organizationFeeTemplateDetail.setTemplateNo(organizationFeeTemplate.getTemplateNo());
                        organizationFeeTemplateDetail.setCreateTime(now);
                        organizationFeeTemplateDetail.setCreateUserId(String.valueOf(bossOperator.getId()));
                        organizationFeeTemplateDetail.setCreateUserName(bossOperator.getUsername());
                        organizationFeeTemplateDetail.setLastModifyTime(now);
                        organizationFeeTemplateDetail.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                        organizationFeeTemplateDetail.setLastModifyUserName(bossOperator.getUsername());
                        organizationFeeTemplateDetailMapper.insert(organizationFeeTemplateDetail);
                    });
                }
            }
            organizationFeeTemplateReviewRecordMapper.updateById(modifyRecord);
        } else {
            log.error("非法审核状态, 审核状态:{}", dto.getReviewStatus());
            return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
        }
        return Result.success();
    }


    /**
     * 分页查询机构费率模版审核信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationFeeTemplateReviewRecordVO> reviewPageList(OrganizationFeeTemplateReviewRecordPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationFeeTemplateExtMapper.listOrganizationFeeTemplateReviewRecordByWhere(dto));
    }

    /**
     * 查询机构费率模版明细列表
     *
     * @param reviewId
     * @return
     */
    public Result<OrganizationFeeTemplateAndDetailReviewRecordVO> reviewDetail(String reviewId) {
        OrganizationFeeTemplateAndDetailReviewRecordVO organizationFeeTemplateAndDetailReviewRecordVO = new OrganizationFeeTemplateAndDetailReviewRecordVO();
        OrganizationFeeTemplateReviewRecord organizationFeeTemplateReviewRecord = organizationFeeTemplateReviewRecordMapper.selectById(reviewId);
        if (organizationFeeTemplateReviewRecord == null) {
            log.error("审核信息不存在, reviewId:{}", reviewId);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        BeanUtil.copyProperties(organizationFeeTemplateReviewRecord, organizationFeeTemplateAndDetailReviewRecordVO);
        List<OrganizationFeeTemplateDetailVO> organizationFeeTemplateDetailReviewRecordVOList = new ArrayList<>();
        List<OrganizationFeeTemplateDetailReviewRecord> organizationFeeTemplateDetailReviewRecordList =
                organizationFeeTemplateDetailReviewRecordMapper.selectList(Wrappers.<OrganizationFeeTemplateDetailReviewRecord>lambdaQuery()
                        .eq(OrganizationFeeTemplateDetailReviewRecord::getReviewId, reviewId));
        if (organizationFeeTemplateDetailReviewRecordList != null && !organizationFeeTemplateDetailReviewRecordList.isEmpty()) {
            // 进行分组
            Function<OrganizationFeeTemplateDetailReviewRecord, List<String>> compositeKey = item ->
                    Arrays.asList(item.getFeeType(), item.getBillingDimension(), item.getCollectionMethod(), item.getCurrencyCode());
            Map<List<String>, List<OrganizationFeeTemplateDetailReviewRecord>> collect =
                    organizationFeeTemplateDetailReviewRecordList.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));
            collect.forEach((item, organizationFeeTemplateDetails) -> {
                OrganizationFeeTemplateDetailVO organizationFeeTemplateDetailVO = new OrganizationFeeTemplateDetailVO();
                organizationFeeTemplateDetailVO.setFeeType(item.get(0));
                organizationFeeTemplateDetailVO.setBillingDimension(item.get(1));
                organizationFeeTemplateDetailVO.setCollectionMethod(item.get(2));
                organizationFeeTemplateDetailVO.setCurrencyCode(item.get(3));
                List<OrganizationFeeTemplateDetailValueVO> organizationFeeTemplateDetailValueList =
                        BeanUtil.copyToList(organizationFeeTemplateDetails, OrganizationFeeTemplateDetailValueVO.class);
                organizationFeeTemplateDetailVO.setOrganizationFeeTemplateDetailValueList(organizationFeeTemplateDetailValueList);
                organizationFeeTemplateDetailReviewRecordVOList.add(organizationFeeTemplateDetailVO);
            });
            // 按照费率类型进行排序
            organizationFeeTemplateDetailReviewRecordVOList.sort((o1, o2) -> {
                int value1 = Integer.parseInt(o1.getFeeType());
                int value2 = Integer.parseInt(o2.getFeeType());
                return Integer.compare(value1, value2);
            });
        }
        organizationFeeTemplateAndDetailReviewRecordVO.setOrganizationFeeTemplateDetailReviewRecordList(organizationFeeTemplateDetailReviewRecordVOList);
        return Result.success(organizationFeeTemplateAndDetailReviewRecordVO);
    }
}
