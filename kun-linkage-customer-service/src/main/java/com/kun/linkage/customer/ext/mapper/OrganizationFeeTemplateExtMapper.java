package com.kun.linkage.customer.ext.mapper;


import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplatePageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateVO;

import java.util.List;

/**
 * <p>
 * 机构费用模版信息表 Mapper 扩展接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface OrganizationFeeTemplateExtMapper {
    /**
     * 根据条件查询机构费用模版信息列表
     *
     * @param pageQueryDTO
     * @return
     */
    List<OrganizationFeeTemplateVO> listOrganizationFeeTemplateByWhere(OrganizationFeeTemplatePageQueryDTO pageQueryDTO);

    /**
     * 根据条件查询机构费用模版信息审核记录列表
     *
     * @param pageQueryDTO
     * @return
     */
    List<OrganizationFeeTemplateReviewRecordVO> listOrganizationFeeTemplateReviewRecordByWhere(OrganizationFeeTemplateReviewRecordPageQueryDTO pageQueryDTO);
}
