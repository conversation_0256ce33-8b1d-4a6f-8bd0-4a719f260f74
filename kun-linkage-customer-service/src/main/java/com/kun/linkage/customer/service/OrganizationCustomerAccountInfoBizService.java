package com.kun.linkage.customer.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.account.facade.api.AccountInfoManagementFacade;
import com.kun.linkage.account.facade.api.AccountTransactionFacade;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.req.QueryAccountInfoReq;
import com.kun.linkage.account.facade.api.bean.req.QueryListAccountInfoReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.api.bean.res.QueryAccountInfoRes;
import com.kun.linkage.account.facade.constants.AccountTipConstant;
import com.kun.linkage.account.facade.enums.AccountingActionEnum;
import com.kun.linkage.account.facade.enums.BusinessActionEnum;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.account.facade.enums.BusinessTypeEnum;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonConstant;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.*;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.customer.ext.mapper.OrganizationCustomerAccountInfoExtMapper;
import com.kun.linkage.customer.facade.api.bean.req.QueryAccountBalanceReq;
import com.kun.linkage.customer.facade.api.bean.req.QueryBalanceByCardIdReq;
import com.kun.linkage.customer.facade.api.bean.res.QueryAccountBalanceRes;
import com.kun.linkage.customer.facade.api.bean.res.QueryBalanceByCardIdRsp;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.dto.OrgCustomerAccountProcessBuckChargeDTO;
import com.kun.linkage.customer.facade.dto.OrganizationCustomerAccountInfoPageQueryDTO;
import com.kun.linkage.customer.facade.enums.*;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerAccountDetailVO;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerAccountInfoVO;
import com.kun.linkage.customer.facade.vo.mq.CardRechargeBookkeepReversalEventVO;
import com.kun.linkage.customer.facade.vo.mq.OrganizationFeeDeductionEventVO;
import com.kun.linkage.customer.service.organization.OrganizationBasicInfoBizService;
import com.kun.linkage.customer.service.organization.OrganizationFeeConfigBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构客户账户信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class OrganizationCustomerAccountInfoBizService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationCustomerAccountInfoBizService.class);
    @Resource
    private OrganizationCustomerAccountInfoExtMapper organizationCustomerAccountInfoExtMapper;
    @Resource
    private OrganizationCustomerAccountInfoMapper organizationCustomerAccountInfoMapper;
    @Resource
    private AccountInfoManagementFacade accountInfoManagementFacade;
    @Resource
    private OrganizationCustomerCardInfoMapper organizationCustomerCardInfoMapper;
    @Resource
    private AccountInfoMapper accountInfoMapper;
    @Resource
    private OrganizationBasicInfoBizService organizationBasicInfoBizService;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private OrganizationCustomerCardInfoService organizationCustomerCardInfoService;
    @Resource
    private ExchangeRateBizService exchangeRateBizService;
    @Resource
    private OrganizationFeeConfigBizService organizationFeeConfigBizService;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private AccountTransactionFacade accountTransactionFacade;
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private CardRechargeDetailMapper cardRechargeDetailMapper;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;

    /**
     * 分页查询机构客户账户信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationCustomerAccountInfoVO> pageList(OrganizationCustomerAccountInfoPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationCustomerAccountInfoExtMapper.listOrganizationCustomerAccountInfoByWhere(dto));
    }

    /**
     * 查询机构客户账户明细信息
     *
     * @param organizationNo
     * @param organizationCustomerAccountInfoId
     * @return
     */
    public Result<OrganizationCustomerAccountDetailVO> detail(String organizationNo, String organizationCustomerAccountInfoId) {
        OrganizationCustomerAccountDetailVO organizationCustomerAccountDetailVO = organizationCustomerAccountInfoExtMapper
                .getOrganizationCustomerAccountInfoByOrganizationNoAndId(organizationNo, Long.valueOf(organizationCustomerAccountInfoId));
        if (organizationCustomerAccountDetailVO != null) {
            // 调用账户服务开户
            QueryAccountInfoReq queryAccountInfoReq = new QueryAccountInfoReq();
            queryAccountInfoReq.setBusinessSystem(CommonConstant.KL);
            queryAccountInfoReq.setBusinessOrganizationNo(organizationNo);
            queryAccountInfoReq.setAccountNo(organizationCustomerAccountDetailVO.getAccountNo());
            log.info("调用账户服务进行查询账户信息,请求参数:{}", JSON.toJSONString(queryAccountInfoReq));
            Result<QueryAccountInfoRes> queryAccountInfoRes = accountInfoManagementFacade.queryAccountInfo(queryAccountInfoReq);
            log.info("调用账户服务进行查询账户信息,响应参数:{}", JSON.toJSONString(queryAccountInfoRes));
            if (Result.isSuccess(queryAccountInfoRes) && queryAccountInfoRes.getData() != null) {
                log.info("调用账户服务查询账户信息成功");
                organizationCustomerAccountDetailVO.setTotalBalance(queryAccountInfoRes.getData().getTotalBalanceAmount());
                organizationCustomerAccountDetailVO.setFrozenAmount(queryAccountInfoRes.getData().getFrozenAmount());
                organizationCustomerAccountDetailVO.setAvailableAmount(queryAccountInfoRes.getData().getAvailableAmount());
            } else {
                log.error("调用账户服务查询账户信息失败");
                throw new BusinessException(CommonTipConstant.SYSTEM_INSIDE_ERROR);
            }
            return Result.success(organizationCustomerAccountDetailVO);
        } else {
            log.error("机构客户账户信息不存在, 机构账户信息表ID:{}", organizationCustomerAccountInfoId);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
    }

    /**
     * 查询账户余额
     *
     * @param queryAccountBalanceReq
     * @return
     */
    public Result<List<QueryAccountBalanceRes>> queryAccountBalance(QueryAccountBalanceReq queryAccountBalanceReq) {
        List<OrganizationCustomerAccountInfo> organizationCustomerAccountInfoList =
                organizationCustomerAccountInfoMapper.selectList(Wrappers.<OrganizationCustomerAccountInfo>lambdaQuery()
                        .eq(OrganizationCustomerAccountInfo::getOrganizationNo, queryAccountBalanceReq.getOrganizationNo())
                        .eq(OrganizationCustomerAccountInfo::getCustomerId, queryAccountBalanceReq.getCustomerId())
                        .eq(OrganizationCustomerAccountInfo::getStatus, ValidStatusEnum.VALID.getValue())
                        .in(OrganizationCustomerAccountInfo::getCurrencyCode, queryAccountBalanceReq.getCurrencyCodeList()));
        if (organizationCustomerAccountInfoList == null || organizationCustomerAccountInfoList.size() != queryAccountBalanceReq.getCurrencyCodeList().size()) {
            log.error("机构客户账户信息不存在, 机构号:{}, 客户号:{}, 币种:{}",
                    queryAccountBalanceReq.getOrganizationNo(), queryAccountBalanceReq.getCustomerId(), queryAccountBalanceReq.getCurrencyCodeList());
            return Result.fail(AccountTipConstant.ACCOUNT_NOT_EXIST);
        }
        QueryListAccountInfoReq queryListAccountInfoReq = new QueryListAccountInfoReq();
        queryListAccountInfoReq.setBusinessSystem(CommonConstant.KL);
        queryListAccountInfoReq.setBusinessOrganizationNo(queryAccountBalanceReq.getOrganizationNo());
        queryListAccountInfoReq.setAccountNoList(organizationCustomerAccountInfoList.stream().map(OrganizationCustomerAccountInfo::getAccountNo).collect(Collectors.toList()));
        log.info("调用账户服务进行查询账户信息列表,请求参数:{}", JSON.toJSONString(queryListAccountInfoReq));
        Result<List<QueryAccountInfoRes>> result = accountInfoManagementFacade.listAccountInfo(queryListAccountInfoReq);
        log.info("调用账户服务进行查询账户信息列表,响应参数:{}", JSON.toJSONString(result));
        List<QueryAccountBalanceRes> queryAccountBalanceResList = new ArrayList<>();
        if (Result.isSuccess(result) && result.getData() != null
                && result.getData().size() == queryAccountBalanceReq.getCurrencyCodeList().size()) {
            log.info("调用账户服务查询账户信息列表成功");
            result.getData().forEach(queryAccountInfoRes -> {
                QueryAccountBalanceRes queryAccountBalanceRes = new QueryAccountBalanceRes();
                BeanUtil.copyProperties(queryAccountInfoRes, queryAccountBalanceRes);
                queryAccountBalanceResList.add(queryAccountBalanceRes);
            });
        } else {
            log.error("调用账户服务查询账户信息列表失败");
            throw new BusinessException(AccountTipConstant.ACCOUNT_NOT_EXIST);
        }
        return Result.success(queryAccountBalanceResList);
    }

    /**
     * 根据cardId查询账户余额
     *
     * @param queryBalanceByCardIdReq
     * @return
     */
    public Result<QueryBalanceByCardIdRsp> queryBalanceByCardId(QueryBalanceByCardIdReq queryBalanceByCardIdReq) {
        //1.根据cardId查询机构用户卡信息表 kl_organization_customer_card_info
        QueryWrapper<OrganizationCustomerCardInfo> organizationCustomerCardInfoQueryWrapper = new QueryWrapper<>();
        organizationCustomerCardInfoQueryWrapper.lambda().eq(OrganizationCustomerCardInfo::getCardId, queryBalanceByCardIdReq.getCardId())
                .eq(OrganizationCustomerCardInfo::getCustomerId, queryBalanceByCardIdReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, queryBalanceByCardIdReq.getOrganizationNo());
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(organizationCustomerCardInfoQueryWrapper);
        if (ObjectUtil.isNull(organizationCustomerCardInfo)) {
            log.error("客户卡信息不存在,机构号:{},客户号:{},卡id:{}", queryBalanceByCardIdReq.getOrganizationNo(), queryBalanceByCardIdReq.getCustomerId(), queryBalanceByCardIdReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        //2.机构+用户+卡的币种去kl_organization_customer_account_info(分表规则一样)这里面拿到账户号
        QueryWrapper<OrganizationCustomerAccountInfo> organizationCustomerAccountInfoQueryWrapper = new QueryWrapper<>();
        organizationCustomerAccountInfoQueryWrapper.lambda()
                .eq(OrganizationCustomerAccountInfo::getOrganizationNo, queryBalanceByCardIdReq.getOrganizationNo())
                .eq(OrganizationCustomerAccountInfo::getCustomerId, queryBalanceByCardIdReq.getCustomerId())
                .eq(OrganizationCustomerAccountInfo::getCurrencyCode, organizationCustomerCardInfo.getCurrencyCode());
        OrganizationCustomerAccountInfo organizationCustomerAccountInfo = organizationCustomerAccountInfoMapper.selectOne(organizationCustomerAccountInfoQueryWrapper);
        if (ObjectUtil.isNull(organizationCustomerAccountInfo)) {
            log.info("账号信息不存在:{}", queryBalanceByCardIdReq);
            return Result.fail(AccountTipConstant.ACCOUNT_NOT_EXIST);
        }

        QueryAccountInfoReq queryAccountInfoReq = new QueryAccountInfoReq();
        queryAccountInfoReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        queryAccountInfoReq.setAccountNo(organizationCustomerAccountInfo.getAccountNo());
        queryAccountInfoReq.setBusinessOrganizationNo(organizationCustomerAccountInfo.getOrganizationNo());
        Result<QueryAccountInfoRes> queryAccountInfoResResult = accountInfoManagementFacade.queryAccountInfo(queryAccountInfoReq);
        if (Result.isSuccess(queryAccountInfoResResult) && queryAccountInfoResResult.getData() != null) {
            QueryAccountInfoRes queryAccountInfoRes = queryAccountInfoResResult.getData();
            if (queryAccountInfoRes != null) {
                QueryBalanceByCardIdRsp queryBalanceByCardIdRsp = BeanUtil.copyProperties(queryAccountInfoRes, QueryBalanceByCardIdRsp.class);
                queryBalanceByCardIdRsp.setCardId(queryBalanceByCardIdReq.getCardId());
                return Result.success(queryBalanceByCardIdRsp);
            } else {
                return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
            }
        } else {
            log.error("调用账户服务查询账户信息失败");
            return Result.fail(queryAccountInfoResResult.getCode(), queryAccountInfoResResult.getMessage());
        }
    }

    /**
     * 补扣账
     * @param orgCustomerAccountProcessBuckChargeDTO
     * @return
     */
    public Result<Boolean> processBackCharge(OrgCustomerAccountProcessBuckChargeDTO orgCustomerAccountProcessBuckChargeDTO) {
        String organizationCustomerAccountInfoId = orgCustomerAccountProcessBuckChargeDTO.getOrganizationCustomerAccountInfoId();
        String organizationNo = orgCustomerAccountProcessBuckChargeDTO.getOrganizationNo();

        LambdaQueryWrapper<OrganizationCustomerAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrganizationCustomerAccountInfo::getOrganizationNo, organizationNo)
                .eq(OrganizationCustomerAccountInfo::getId, Long.valueOf(organizationCustomerAccountInfoId));
        OrganizationCustomerAccountInfo organizationCustomerAccountInfo = organizationCustomerAccountInfoMapper.selectOne(queryWrapper);

        if(null == organizationCustomerAccountInfo){
            log.error("暂无机构客户账号数据,id:{},organizationNo:{}", organizationCustomerAccountInfoId, organizationNo);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }

        String accountNo = organizationCustomerAccountInfo.getAccountNo();
        //查基础账户信息表
        LambdaQueryWrapper<AccountInfo> accountInfoQuery = new LambdaQueryWrapper<>();
        accountInfoQuery.eq(AccountInfo::getAccountNo, accountNo)
                .eq(AccountInfo::getBusinessOrganizationNo, organizationNo);

        AccountInfo accountInfo = accountInfoMapper.selectOne(accountInfoQuery);
        if(null == accountInfo){
            log.error("账户基础数据不存在,accountNo:{},organizationNo:{}", accountNo, organizationNo);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }

        //判断可用余额是否小于0;如果小于0才补扣账
        BigDecimal availableAmount = accountInfo.getAvailableAmount();
        if(null == availableAmount || availableAmount.compareTo(BigDecimal.ZERO) >= 0){
            log.error("客户账户金额大于0，无需补扣账,accountNo:{},organizationNo:{},availableAmount:{}", accountNo, organizationNo, availableAmount);
            return Result.fail(CustomerTipConstant.ACCOUNT_AVAILABLE_AMOUNT_GE_ZERO);
        }

        //查询机构基础信息拿到 资金池币种
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoBizService.selectOrganizationBasicInfoByOrganization(organizationNo);
        if(null == organizationBasicInfo || StringUtils.isBlank(organizationBasicInfo.getPoolCurrencyCode())){
            log.error("查询机构数据，机构或机构资金池币种为空,organizationNo:{}", organizationNo);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        //资金池币种
        String poolCurrencyCode = organizationBasicInfo.getPoolCurrencyCode();
        //机构账户币种
        String currencyCode = accountInfo.getCurrencyCode();
        //需要补扣的金额
        BigDecimal absoluteAmount = availableAmount.abs();

        //拿账户查询有效卡片信息,如果卡片已经注销直接返回错误提示
        String businessCustomerId = accountInfo.getBusinessCustomerId();
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoService.selectByOrgNoAndCustomerIdCurrencyCode(organizationNo, businessCustomerId, currencyCode);
        if(null == organizationCustomerCardInfo){
            log.error("该账户没有可用的卡信息,organizationNo:{},businessCustomerId:{},currencyCode:{}", organizationNo, businessCustomerId,currencyCode);
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        String cardProductCode = organizationCustomerCardInfo.getCardProductCode();
        DigitalCurrencyEnum enumByValue = DigitalCurrencyEnum.getEnumByValue(poolCurrencyCode);

        Integer currencyPrecision = accountInfo.getCurrencyPrecision();
        // 初始化充值记录
        CardRechargeDetail cardRechargeDetail = this.initCardRechargeDetail(poolCurrencyCode,organizationCustomerCardInfo,absoluteAmount,currencyPrecision);
        String cardRechargeDetailId = cardRechargeDetail.getId();
        try {
            //数币账户
            if(null != enumByValue){
                //如果是USDT 且账号币种是USD 不需要换汇且不收取markup费，只有承兑费
                String templateNo = null;
                cardRechargeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
                //查询商户机构模板
                OrganizationFeeConfig organizationFeeConfig = organizationFeeConfigBizService.getValidOrganizationFeeConfigByWhere(organizationNo, cardProductCode);
                if(null == organizationFeeConfig || null == organizationFeeConfig.getTemplateNo()){
                    log.info("当前机构没有配置费用模板,organizationNo:{}", organizationNo);
                }else {
                    templateNo = organizationFeeConfig.getTemplateNo();
                }

                //资金池币种是数币，且是机构商户币种是USD就不需要查汇率和收markup费了
                if(DigitalCurrencyEnum.USDT.equals(enumByValue) && FiatCurrencyEnum.USD.getCurrencyCode().equals(currencyCode)){
                    //USD换USDT 是1:1 所以我们就不调用汇率接口了
                    BigDecimal fxRate = BigDecimal.ONE;
                    //设置充值的汇率
                    cardRechargeDetail.setFxRate(fxRate);
                    //扣除本金金额
                    cardRechargeDetail.setDeductPrincipalAmount(cardRechargeDetail.getRechargeAmount().multiply(fxRate).setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP));

                    //扣kun账,然后充值到机构账户
                    try {
                        this.processingDigitalCurrencyAccountTransaction(cardRechargeDetail,organizationBasicInfo,accountNo);
                    }catch (Exception e) {
                        log.error("[BOSS后台负金额补扣]扣账币种为数币的账户动账失败,发送冲账事件到mq", e);
                        this.sendCardRechargeBookKeepReversalToMq(cardRechargeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        throw new BusinessException(CommonTipConstant.EXTERNAL_ERROR);
                    }

                    //拿到商户的承兑手续费 计算承兑费用；判断月结还是实时
                    if(StringUtils.isNotBlank(templateNo)){
                        OrganizationFeeTemplateDetail acceptanceTempleDetail = selectOrganizationFeeTemplateDetailByParam(templateNo, OrganizationFeeTypeEnum.ACCEPTANCE_FEE, currencyCode, absoluteAmount);
                        //计算手续费并且保存手续费明细
                        OrganizationFeeDetail organizationFeeDetail = this.calculatePaymentFee(acceptanceTempleDetail, absoluteAmount, BigDecimal.ONE, organizationBasicInfo,
                                currencyCode, cardProductCode,templateNo,cardRechargeDetailId,KunAndPayXRemarkEnum.ACCEPTANCE_FEE.getRemark());
                        if(null != organizationFeeDetail && OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue().equals(organizationFeeDetail.getFeeCollectionMethod())){
                            //扣承兑费
                            this.sendOrganizationFeeDeductionToMq(organizationFeeDetail,organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        }
                    }

                   log.info("负数金额补扣成功,organizationCustomerAccountInfoId:{},organizationNo:{}",organizationCustomerAccountInfoId, organizationNo);
                    cardRechargeDetail.setRechargeStatus(OperationStatusEnum.SUCCESS.getStatus());
                    return Result.success();
                }else {

                    //1.0 调用kun 的汇率查询接口,拿到汇率
                    BigDecimal fxRate = exchangeRateBizService.getCurrencyExchangeRate(currencyCode, poolCurrencyCode, absoluteAmount, organizationBasicInfo);
                    //设置充值的汇率
                    cardRechargeDetail.setFxRate(fxRate);
                    //扣除本金金额
                    cardRechargeDetail.setDeductPrincipalAmount(cardRechargeDetail.getRechargeAmount().multiply(fxRate).setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP));
                    //扣kun账,然后充值到机构账户
                    try {
                        this.processingDigitalCurrencyAccountTransaction(cardRechargeDetail,organizationBasicInfo,accountNo);
                    }catch (Exception e) {
                        log.error("[BOSS后台负金额补扣]扣账币种为数币的账户动账失败,发送冲账事件到mq", e);
                        this.sendCardRechargeBookKeepReversalToMq(cardRechargeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        throw new BusinessException(CommonTipConstant.EXTERNAL_ERROR);
                    }

                    //扣手续费
                    if(StringUtils.isNotBlank(templateNo)){

                        // 拿到商户的markUpRate 计算markup 手续费；判断是月结还是实时
                        OrganizationFeeTemplateDetail markupTempleDetail = selectOrganizationFeeTemplateDetailByParam(templateNo, OrganizationFeeTypeEnum.FX_MARKUP_FEE, currencyCode, absoluteAmount);
                        OrganizationFeeDetail markupFeeDetail = this.calculatePaymentFee(markupTempleDetail, absoluteAmount, fxRate, organizationBasicInfo,
                                currencyCode, cardProductCode,templateNo,cardRechargeDetailId,KunAndPayXRemarkEnum.FX_MARKUP_FEE.getRemark());
                        if(null != markupFeeDetail && OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue().equals(markupFeeDetail.getFeeCollectionMethod())){
                            //扣markUp 手续费;如果实时调用mq;MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC
                            this.sendOrganizationFeeDeductionToMq(markupFeeDetail,organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        }

                        //拿到商户的承兑手续费 计算承兑费用；判断月结还是实时
                        OrganizationFeeTemplateDetail acceptanceTempleDetail = selectOrganizationFeeTemplateDetailByParam(templateNo, OrganizationFeeTypeEnum.ACCEPTANCE_FEE, currencyCode, absoluteAmount);
                        //承兑费计算
                        OrganizationFeeDetail acceptanceFeeDetail = this.calculatePaymentFee(acceptanceTempleDetail, absoluteAmount, fxRate, organizationBasicInfo, currencyCode,
                                cardProductCode,templateNo,cardRechargeDetailId,KunAndPayXRemarkEnum.ACCEPTANCE_FEE.getRemark());
                        if(null != acceptanceFeeDetail && OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue().equals(acceptanceFeeDetail.getFeeCollectionMethod())){
                            //扣承兑费;如果实时调用mq;MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC
                            this.sendOrganizationFeeDeductionToMq(acceptanceFeeDetail,organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        }
                    }

                   log.info("负数金额补扣成功,organizationCustomerAccountInfoId:{},organizationNo:{}",organizationCustomerAccountInfoId, organizationNo);
                    cardRechargeDetail.setRechargeStatus(OperationStatusEnum.SUCCESS.getStatus());
                    return Result.success();
                }
            }else {
                //法币账户
                cardRechargeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());
                //判断币种是否一致
                if(!currencyCode.equals(poolCurrencyCode)){
                    //调用pax 汇率查询接口,拿到汇率
                    BigDecimal fxRate = exchangeRateBizService.getCurrencyExchangeRate(currencyCode, poolCurrencyCode, absoluteAmount, organizationBasicInfo);

                    //设置充值的汇率
                    cardRechargeDetail.setFxRate(fxRate);
                    //扣除本金金额
                    cardRechargeDetail.setDeductPrincipalAmount(cardRechargeDetail.getRechargeAmount().multiply(fxRate).setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP));

                    String templateNo = null;
                    //查询商户机构模板
                    OrganizationFeeConfig organizationFeeConfig = organizationFeeConfigBizService.getValidOrganizationFeeConfigByWhere(organizationNo, cardProductCode);
                    if(null == organizationFeeConfig || null == organizationFeeConfig.getTemplateNo()){
                        log.info("当前机构没有配置费用模板,organizationNo:{}", organizationNo);
                    }else {
                        templateNo = organizationFeeConfig.getTemplateNo().toString();
                    }

                    //调用payX扣账接口,充值上账接口
                    try {
                        this.processingFiatCurrencyAccountTransaction(cardRechargeDetail, organizationBasicInfo, accountNo);
                    } catch (Exception e) {
                        log.error("[BOSS后台负金额补扣]扣账币种为法币的账户动账失败,发送冲账事件到mq", e);
                        this.sendCardRechargeBookkeepReversalToMq(cardRechargeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        throw new BusinessException(CommonTipConstant.EXTERNAL_ERROR);
                    }

                    //获取markup rate 计算markup手续费
                    if(StringUtils.isNotBlank(templateNo)){
                        //2.0 拿到商户的markUpRate 计算markup 手续费；判断是月结还是实时
                        OrganizationFeeTemplateDetail markupTempleDetail = selectOrganizationFeeTemplateDetailByParam(templateNo, OrganizationFeeTypeEnum.FX_MARKUP_FEE, currencyCode, absoluteAmount);
                        OrganizationFeeDetail markupFeeDetail = this.calculatePaymentFee(markupTempleDetail, absoluteAmount, fxRate, organizationBasicInfo, currencyCode,
                                cardProductCode,templateNo,cardRechargeDetailId,KunAndPayXRemarkEnum.FX_MARKUP_FEE.getRemark());

                        if(null != markupFeeDetail && OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue().equals(markupFeeDetail.getFeeCollectionMethod())){
                            this.sendOrganizationFeeDeductionToMq(markupFeeDetail,organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        }
                    }

                   log.info("负数金额补扣成功,organizationCustomerAccountInfoId:{},organizationNo:{}",organizationCustomerAccountInfoId, organizationNo);
                    cardRechargeDetail.setRechargeStatus(OperationStatusEnum.SUCCESS.getStatus());
                    return Result.success();
                }else {
                    //币种一致不收取任何费用
                    BigDecimal fxRate = BigDecimal.ONE;
                    //设置充值的汇率
                    cardRechargeDetail.setFxRate(fxRate);
                    //扣除本金金额
                    cardRechargeDetail.setDeductPrincipalAmount(cardRechargeDetail.getRechargeAmount().multiply(fxRate).setScale(cardRechargeDetail.getDeductCurrencyPrecision(), RoundingMode.UP));
                    //扣payX账,然后充值到机构账户
                    try {
                        this.processingFiatCurrencyAccountTransaction(cardRechargeDetail, organizationBasicInfo, accountNo);
                    } catch (Exception e) {
                        log.error("[BOSS后台负金额补扣]扣账币种为法币的账户动账失败,发送冲账事件到mq", e);
                        this.sendCardRechargeBookkeepReversalToMq(cardRechargeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                        throw new BusinessException(CommonTipConstant.EXTERNAL_ERROR);
                    }
                   log.info("负数金额补扣成功,organizationCustomerAccountInfoId:{},organizationNo:{}",organizationCustomerAccountInfoId, organizationNo);
                    cardRechargeDetail.setRechargeStatus(OperationStatusEnum.SUCCESS.getStatus());
                    return Result.success();
                }
            }
        }catch (BusinessException be) {
            log.error("[BOSS后台负金额补扣]卡充值处理失败,异常信息:{}", be.getMessage());
            if (cardRechargeDetail != null) {
                cardRechargeDetail.setRechargeStatus(OperationStatusEnum.FAIL.getStatus());
            }
            return Result.fail(CommonTipConstant.FAIL);
        }catch (Exception e) {
            log.error("[BOSS后台负金额补扣]卡充值处理异常,异常信息:{}", e.getMessage());
            if (cardRechargeDetail != null) {
                cardRechargeDetail.setRechargeStatus(OperationStatusEnum.FAIL.getStatus());
            }
            return Result.fail(CommonTipConstant.FAIL);
        }finally {
            // 更新充值结果
            if (cardRechargeDetail != null) {
                cardRechargeDetailMapper.update(cardRechargeDetail, Wrappers.<CardRechargeDetail>lambdaQuery()
                        .eq(CardRechargeDetail::getId, cardRechargeDetail.getId())
                        .eq(CardRechargeDetail::getRechargeDatetime, cardRechargeDetail.getRechargeDatetime()));
            }
            log.info("[BOSS后台负金额补扣]处理完成");
        }

    }

    /**
     * 发送卡充值账户冲账事件到mq
     *
     * @param cardRechargeDetail
     * @param mpcGroupCode
     */
    public void sendCardRechargeBookkeepReversalToMq(CardRechargeDetail cardRechargeDetail, String mpcToken, String mpcGroupCode) {
        log.info("BOOS后台负金额补扣发送卡充值账户冲账事件到mq");
        CardRechargeBookkeepReversalEventVO cardRechargeBookkeepReversalEventVO = new CardRechargeBookkeepReversalEventVO();
        cardRechargeBookkeepReversalEventVO.setCardRechargeDetailId(cardRechargeDetail.getId());
        cardRechargeBookkeepReversalEventVO.setRechargeDatetime(cardRechargeDetail.getRechargeDatetime());
        cardRechargeBookkeepReversalEventVO.setMpcToken(mpcToken);
        cardRechargeBookkeepReversalEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.CARD_RECHARGE_BOOKKEEP_REVERSAL_EVENT_TOPIC, cardRechargeBookkeepReversalEventVO, 10000, MqTopicConstant.DELAY_LEVEL_30S);
    }


    /**
     * 创建充值操作记录
     *
     */
    private CardRechargeDetail initCardRechargeDetail(String deductCurrencyCode,OrganizationCustomerCardInfo organizationCustomerCardInfo,
                                                      BigDecimal rechargeAmount,Integer currencyPrecision) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        CardRechargeDetail cardRechargeDetail = new CardRechargeDetail();
        cardRechargeDetail.setOrganizationNo(organizationCustomerCardInfo.getOrganizationNo());
        cardRechargeDetail.setBusinessType(CardRechargeBusinessTypeEnum.NEGATIVE_AMOUNT_DEDUCTION.getValue());
        cardRechargeDetail.setCustomerId(organizationCustomerCardInfo.getCustomerId());
        cardRechargeDetail.setRequestNo(organizationCustomerCardInfo.getId().toString());
        cardRechargeDetail.setCardId(organizationCustomerCardInfo.getCardId());
        cardRechargeDetail.setRechargeDatetime(now);
        cardRechargeDetail.setRechargeAmount(rechargeAmount);
        cardRechargeDetail.setRechargeCurrencyCode(organizationCustomerCardInfo.getCurrencyCode());
        cardRechargeDetail.setDeductCurrencyCode(deductCurrencyCode);
        // 充值币种精度
        cardRechargeDetail.setRechargeCurrencyPrecision(currencyPrecision);
        cardRechargeDetail.setDeductCurrencyPrecision(OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(deductCurrencyCode));
        // 记账状态全部初始化为未记账
        cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
        cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
        cardRechargeDetail.setRechargeStatus(OperationStatusEnum.PENDING.getStatus());
        cardRechargeDetail.setBookkeepReversalCount(0);
        cardRechargeDetail.setCreateTime(now);
        cardRechargeDetail.setLastModifyTime(now);
        int row = cardRechargeDetailMapper.insert(cardRechargeDetail);
        log.info("负金额补扣insert {} row CardRechargeDetail", row);
        return cardRechargeDetail;
    }


    /**
     * 发送付金额补扣冲账事件到mq
     *
     * @param cardRechargeDetail
     * @param mpcGroupCode
     */
    public void sendCardRechargeBookKeepReversalToMq(CardRechargeDetail cardRechargeDetail, String mpcToken, String mpcGroupCode) {
        log.info("发送付金额补扣冲账事件到mq");
        CardRechargeBookkeepReversalEventVO cardRechargeBookkeepReversalEventVO = new CardRechargeBookkeepReversalEventVO();
        cardRechargeBookkeepReversalEventVO.setCardRechargeDetailId(cardRechargeDetail.getId());
        cardRechargeBookkeepReversalEventVO.setRechargeDatetime(cardRechargeDetail.getRechargeDatetime());
        cardRechargeBookkeepReversalEventVO.setMpcToken(mpcToken);
        cardRechargeBookkeepReversalEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.CARD_RECHARGE_BOOKKEEP_REVERSAL_EVENT_TOPIC, cardRechargeBookkeepReversalEventVO, 10000, MqTopicConstant.DELAY_LEVEL_30S);
    }

    /**
     * 负金额补扣发送机构费用扣除事件到mq中
     *
     * @param organizationFeeDetail
     * @param mpcToken
     * @param mpcGroupCode
     */
    public void sendOrganizationFeeDeductionToMq(OrganizationFeeDetail organizationFeeDetail, String mpcToken, String mpcGroupCode) {
        log.info("负金额补扣发送机构费用扣除事件到mq中");
        OrganizationFeeDeductionEventVO organizationFeeDeductionEventVO = new OrganizationFeeDeductionEventVO();
        organizationFeeDeductionEventVO.setFeeDetailId(organizationFeeDetail.getId());
        organizationFeeDeductionEventVO.setTransactionDatetime(organizationFeeDetail.getTransactionDatetime());
        organizationFeeDeductionEventVO.setMpcToken(mpcToken);
        organizationFeeDeductionEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC, organizationFeeDeductionEventVO, 10000, MqTopicConstant.DELAY_LEVEL_10S);
    }


    /**
     * 计算手续费并且保存手续费明细
     * @param organizationFeeTemplateDetail 机构费用模板
     * @param amount 交易金额
     * @param fxRate 汇率
     * @param organizationBasicInfo 基础机构数据
     * @param currencyCode 交易币种
     * @param cardProductCode 卡产品号
     * @return
     */
    private OrganizationFeeDetail calculatePaymentFee(OrganizationFeeTemplateDetail organizationFeeTemplateDetail,BigDecimal amount,
                                           BigDecimal fxRate,OrganizationBasicInfo organizationBasicInfo,String currencyCode,String cardProductCode,
                                          String templateNo,String cardRechargeDetailId,String remark) {
        if(null == organizationFeeTemplateDetail){
            log.info("没有机构模板费用详情数据无需记录:机构号{},templateNo:{}",organizationBasicInfo.getOrganizationNo(),templateNo);
            return null;
        }

        //公式 手续费=｜金额｜*汇率*模板中的Rate+固定值 如果大于最大值取最大值,如果小于最小值取最小值
        BigDecimal proportionRate = organizationFeeTemplateDetail.getProportionRate() != null ? organizationFeeTemplateDetail.getProportionRate() : BigDecimal.ZERO;
        //固定值
        BigDecimal fixedAmount = organizationFeeTemplateDetail.getFixedAmount() != null ? organizationFeeTemplateDetail.getFixedAmount() : BigDecimal.ZERO;
        BigDecimal proportionFeeAmount = BigDecimal.ZERO;
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            proportionFeeAmount = amount.multiply(proportionRate);
            if (organizationFeeTemplateDetail.getProportionMinAmount() != null
                    && proportionFeeAmount.compareTo(organizationFeeTemplateDetail.getProportionMinAmount()) < 0) {
                proportionFeeAmount = organizationFeeTemplateDetail.getProportionMinAmount();
            } else if (organizationFeeTemplateDetail.getProportionMaxAmount() != null
                    && proportionFeeAmount.compareTo(organizationFeeTemplateDetail.getProportionMaxAmount()) > 0) {
                proportionFeeAmount = organizationFeeTemplateDetail.getProportionMaxAmount();
            }
        }
        BigDecimal feeAmount = proportionFeeAmount.add(fixedAmount);
        Integer poolCurrencyPrecision = OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(organizationBasicInfo.getPoolCurrencyCode());
        // 此处是开卡和销卡的手续费计算,因为开卡和销卡都没有金额,所以手续费都只会配置固定值
        BigDecimal deductFeeAmount = feeAmount.multiply(fxRate).setScale(poolCurrencyPrecision, RoundingMode.UP);
        if (deductFeeAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 保存费用明细记录
            LocalDateTime now = LocalDateTime.now().withNano(0);
            OrganizationFeeDetail organizationFeeDetail = new OrganizationFeeDetail();
            organizationFeeDetail.setOrganizationNo(organizationBasicInfo.getOrganizationNo());
            organizationFeeDetail.setCardProductCode(cardProductCode);
            organizationFeeDetail.setRelatedTransactionId(cardRechargeDetailId);
            organizationFeeDetail.setCalculateDatetime(now);
            organizationFeeDetail.setTransactionDatetime(now);
            organizationFeeDetail.setFeeType(organizationFeeTemplateDetail.getFeeType());
            organizationFeeDetail.setFeeCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());
            organizationFeeDetail.setTransactionAmount(amount);
            organizationFeeDetail.setTransactionCurrencyCode(currencyCode);
            organizationFeeDetail.setTransactionCurrencyPrecision(2);
            organizationFeeDetail.setFeeAmount(feeAmount);
            organizationFeeDetail.setSnapshotBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
            organizationFeeDetail.setSnapshotMinAmount(organizationFeeTemplateDetail.getMinAmount());
            organizationFeeDetail.setSnapshotMaxAmount(organizationFeeTemplateDetail.getMaxAmount());
            organizationFeeDetail.setSnapshotProportionRate(proportionRate);
            organizationFeeDetail.setSnapshotProportionMinAmount(organizationFeeTemplateDetail.getProportionMinAmount());
            organizationFeeDetail.setSnapshotProportionMaxAmount(organizationFeeTemplateDetail.getProportionMaxAmount());
            organizationFeeDetail.setSnapshotFixedAmount(fixedAmount);
            organizationFeeDetail.setFxRate(fxRate);
            // 此处都先设置为未收,实时收取的后面发送mq消息进行收取
            organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.NOT_COLLECTED.getValue());
            if (DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
                // 扣款币种是数币
                organizationFeeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
            } else {
                // 扣款币种是法币
                organizationFeeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());
            }
            organizationFeeDetail.setDeductFeeAmount(deductFeeAmount);
            organizationFeeDetail.setDeductCurrencyCode(organizationBasicInfo.getPoolCurrencyCode());
            organizationFeeDetail.setDeductCurrencyPrecision(poolCurrencyPrecision);
            organizationFeeDetail.setCallCount(0);
            organizationFeeDetail.setRemark(remark);
            organizationFeeDetail.setCreateTime(now);
            organizationFeeDetail.setLastModifyTime(now);
            organizationFeeDetailMapper.insert(organizationFeeDetail);
            log.info("负金额补扣费率类型:{}, 生成费用明细记录成功,记录id:{}", organizationFeeTemplateDetail.getFeeType(), organizationFeeDetail.getId());
            return organizationFeeDetail;
        }else {
            log.info("负金额补扣费率类型:{}, 费用为0,不保存费用记录明细", organizationFeeTemplateDetail.getFeeType());
            return null;
        }
    }

    /**
     * 查询机构费模板详情
     * @param templateNo 模板编号
     * @param feeTypeEnum 费用类型
     * @param currencyCode 币种
     * @param amount 金额
     * @return
     */
    private OrganizationFeeTemplateDetail selectOrganizationFeeTemplateDetailByParam(String templateNo, OrganizationFeeTypeEnum feeTypeEnum, String currencyCode, BigDecimal amount) {
        LambdaQueryWrapper<OrganizationFeeTemplateDetail> wrapper = Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                .eq(OrganizationFeeTemplateDetail::getTemplateNo, templateNo)
                .eq(OrganizationFeeTemplateDetail::getFeeType, feeTypeEnum.getValue())
                .eq(OrganizationFeeTemplateDetail::getCurrencyCode, currencyCode)
                .in(OrganizationFeeTemplateDetail::getBillingDimension,
                        Arrays.asList(OrganizationFeeBillingDimensionEnum.SINGLE_AMOUNT.getValue(), OrganizationFeeBillingDimensionEnum.TIERED_SINGLE_AMOUNT.getValue()));
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            // 左开右闭
            wrapper.lt(OrganizationFeeTemplateDetail::getMinAmount, amount);
            wrapper.ge(OrganizationFeeTemplateDetail::getMaxAmount, amount);
        }
        OrganizationFeeTemplateDetail organizationFeeTemplateDetail = organizationFeeTemplateDetailMapper.selectOne(wrapper);

        if(organizationFeeTemplateDetail == null){
            log.warn("费率类型:{},未找到机构费率配置明细,手续费按0处理,费用类型,币种:{},模版号:{}", feeTypeEnum.getDesc(), currencyCode, templateNo);
        }
        return organizationFeeTemplateDetail;
    }

    /**
     * 这里调用了扣kun账户,以及给客户上账
     * @param cardRechargeDetail 卡充值明细表
     * @param organizationBasicInfo 机构基础数据
     * @param customerAccountNo 账户数据
     */
    private void processingDigitalCurrencyAccountTransaction(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo,
                                                             String customerAccountNo) {
        log.info("负金额补扣开始处理扣账币种为数币的账户动账");
        if (cardRechargeDetail.getDeductPrincipalAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 调用kun进行扣账
            KunDebitSubReq kunDebitSubReq = this.assKunDebitSubReq(organizationBasicInfo);
            String requestNo = String.valueOf(IdWorker.getId());
            kunDebitSubReq.setRequestNo(requestNo);
            cardRechargeDetail.setDeductPrincipalBookkeepRequestNo(requestNo);
            kunDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
            kunDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
            kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.TOPUP_NEGATIVE_AMOUNT.getRemark());
            log.info("[BOSS后台负金额补扣]调用KUN账户扣除本金开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
            Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
            log.info("[BOSS后台负金额补扣]调用KUN账户扣除本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[BOSS后台负金额补扣]调用KUN账户扣除本金成功");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败不需要冲账
                    log.error("[BOSS后台负金额补扣]调用KUN账户扣除本金明确失败");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.error("[BOSS后台负金额补扣]调用KUN账户扣除本金状态未知");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                log.error("[BOSS后台负金额补扣]调用KUN账户扣除本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }else {
            log.info("[BOSS后台负金额补扣]本金金额不大于0,本金无需动账");
            cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
            return;
        }

        // 调用账户服务给客户进行上账
        AccountChangeBalanceReq accountChangeBalanceReq = this.assAccountChangeBalanceReq(cardRechargeDetail, customerAccountNo);
        log.info("[BOSS后台负金额补扣]调用账户服务给客户上账开始,请求参数:{}", JSON.toJSONString(accountChangeBalanceReq));
        Result<AccountChangeBalanceRes> accountChangeBalanceResResult = accountTransactionFacade.changeBalance(accountChangeBalanceReq);
        log.info("[BOSS后台负金额补扣]调用账户服务给客户上账结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
        if (Result.isSuccess(accountChangeBalanceResResult)) {
            // 有明确成功状态
            log.info("[BOSS后台负金额补扣]调用账户服务给客户上账成功");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
        } else if (accountChangeBalanceResResult != null && CommonTipConstant.REQUEST_TIMEOUT.equals(accountChangeBalanceResResult.getCode())) {
            log.error("[BOSS后台负金额补扣]调用账户服务给客户上账超时");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        } else {
            log.error("[BOSS后台负金额补扣]调用账户服务给客户上账失败");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
        log.info("[BOSS后台负金额补扣]扣账币种为数币的账户动账完成");
    }

    /**
     * 处理扣账币种为数币的账户动账
     *
     * @param cardRechargeDetail
     */
    private void processingFiatCurrencyAccountTransaction(CardRechargeDetail cardRechargeDetail, OrganizationBasicInfo organizationBasicInfo, String customerAccountNo) {
        log.info("[BOSS后台负金额补扣]开始处理扣账币种为法币的账户动账");
        // 扣除机构本金金额
        if (cardRechargeDetail.getDeductPrincipalAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 调用PayX进行扣账
            PayXDebitSubReq payXDebitSubReq = this.assBasePayXDebitSubReq(organizationBasicInfo);
            String requestNo = String.valueOf(IdWorker.getId());
            payXDebitSubReq.setRequestNo(requestNo);
            cardRechargeDetail.setDeductPrincipalBookkeepRequestNo(requestNo);
            payXDebitSubReq.setCurrency(cardRechargeDetail.getDeductCurrencyCode());
            payXDebitSubReq.setAmount(cardRechargeDetail.getDeductPrincipalAmount());
            payXDebitSubReq.setRemark(KunAndPayXRemarkEnum.TOPUP_NEGATIVE_AMOUNT.getRemark());
            log.info("[BOSS后台负金额补扣]调用PayX账户扣除本金开始,请求参数:{}", JSON.toJSONString(payXDebitSubReq));
            Result<PayXDebitSubRsp> result = kCardPayXAccountFacade.payXDebitSub(payXDebitSubReq);
            log.info("[BOSS后台负金额补扣]调用PayX账户扣除本金结束,响应参数:{}", JSON.toJSONString(result));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 明确成功
                    log.info("[BOSS后台负金额补扣]调用PayX账户扣除本金成功");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
                } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                    // 明确失败不需要冲账
                    log.error("[BOSS后台负金额补扣]调用PayX账户扣除本金明确失败");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                } else {
                    log.error("[BOSS后台负金额补扣]调用PayX账户扣除本金状态未知");
                    cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
                log.error("[BOSS后台负金额补扣]调用PayX账户扣除本金状态未知");
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } else {
            log.info("[BOSS后台负金额补扣]本金金额不大于0,本金无需动账");
            cardRechargeDetail.setDeductPrincipalBookkeepStatus(CardRechargeBookkeepStatusEnum.NO_NEED.getValue());
        }
        

        // 调用账户服务给客户进行上账
        AccountChangeBalanceReq accountChangeBalanceReq = this.assAccountChangeBalanceReq(cardRechargeDetail, customerAccountNo);
        log.info("[BOSS后台负金额补扣]调用账户服务给客户上账开始,请求参数:{}", JSON.toJSONString(accountChangeBalanceReq));
        Result<AccountChangeBalanceRes> accountChangeBalanceResResult = accountTransactionFacade.changeBalance(accountChangeBalanceReq);
        log.info("[BOSS后台负金额补扣]调用账户服务给客户上账结束,响应参数:{}", JSON.toJSONString(accountChangeBalanceResResult));
        if (Result.isSuccess(accountChangeBalanceResResult)) {
            // 有明确成功状态
            log.info("[BOSS后台负金额补扣]调用账户服务给客户上账成功");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.BOOKED.getValue());
        } else if (accountChangeBalanceResResult != null && CommonTipConstant.REQUEST_TIMEOUT.equals(accountChangeBalanceResResult.getCode())) {
            log.error("[BOSS后台负金额补扣]调用账户服务给客户上账超时");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.UNKNOW.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        } else {
            log.error("[BOSS后台负金额补扣]调用账户服务给客户上账失败");
            cardRechargeDetail.setRechargeBookkeepStatus(CardRechargeBookkeepStatusEnum.NOT_BOOKED.getValue());
            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
        log.info("[BOSS后台负金额补扣]扣账币种为法币的账户动账完成");
    }

    /**
     * 组装PayX账户动账基础请求参数
     *
     * @param organizationBasicInfo
     * @return
     */
    private PayXDebitSubReq assBasePayXDebitSubReq(OrganizationBasicInfo organizationBasicInfo) {
        PayXDebitSubReq payXDebitSubReq = new PayXDebitSubReq();
        payXDebitSubReq.setToken(organizationBasicInfo.getMpcToken());
        payXDebitSubReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXDebitSubReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        payXDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        return payXDebitSubReq;
    }
    

    /**
     * 组装账户服务动账请求参数
     *
     * @param cardRechargeDetail
     * @param accountNo
     * @return
     */
    private AccountChangeBalanceReq assAccountChangeBalanceReq(CardRechargeDetail cardRechargeDetail, String accountNo) {
        AccountChangeBalanceReq accountChangeBalanceReq = new AccountChangeBalanceReq();
        accountChangeBalanceReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        accountChangeBalanceReq.setRequestNo(String.valueOf(IdWorker.getId()));
        accountChangeBalanceReq.setBusinessOrganizationNo(cardRechargeDetail.getOrganizationNo());
        accountChangeBalanceReq.setAccountNo(accountNo);
        accountChangeBalanceReq.setBusinessType(BusinessTypeEnum.RECHARGE.getValue());
        accountChangeBalanceReq.setBusinessAction(BusinessActionEnum.RECHARGE.getValue());
        accountChangeBalanceReq.setAccountingAction(AccountingActionEnum.CREDIT.getValue());
        String businessTransactionNo = String.valueOf(IdWorker.getId());
        cardRechargeDetail.setRechargeBookkeepRequestNo(businessTransactionNo);
        accountChangeBalanceReq.setBusinessTransactionNo(businessTransactionNo);
        accountChangeBalanceReq.setAmount(cardRechargeDetail.getRechargeAmount());
        accountChangeBalanceReq.setCurrencyCode(cardRechargeDetail.getRechargeCurrencyCode());
        return accountChangeBalanceReq;
    }



    /**
     * 拼接
     * @param organizationBasicInfo
     * @return
     */
    private KunDebitSubReq assKunDebitSubReq(OrganizationBasicInfo organizationBasicInfo) {
        KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
        kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        kunDebitSubReq.setRemark(KunAndPayXRemarkEnum.TOPUP_NEGATIVE_AMOUNT.getRemark());
        kunDebitSubReq.setToken(organizationBasicInfo.getMpcToken());
        kunDebitSubReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        return kunDebitSubReq;
    }

}
