package com.kun.linkage.customer.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.account.facade.api.AccountInfoManagementFacade;
import com.kun.linkage.account.facade.api.bean.req.CreateAccountReq;
import com.kun.linkage.account.facade.api.bean.req.QueryAccountInfoReq;
import com.kun.linkage.account.facade.api.bean.res.CreateAccountRes;
import com.kun.linkage.account.facade.api.bean.res.QueryAccountInfoRes;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.base.facade.enums.ProcessorEnum;
import com.kun.linkage.base.facade.vo.CardProductInfoSpecialUsageVO;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonConstant;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.*;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.SensitiveInfoUtil;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.*;
import com.kun.linkage.common.external.facade.api.kcard.KCardCardManagementFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.constants.KCardApiErrorCode;
import com.kun.linkage.common.external.facade.api.kcard.req.*;
import com.kun.linkage.common.external.facade.api.kcard.res.*;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.api.bean.req.*;
import com.kun.linkage.customer.facade.api.bean.res.CardInfoQueryRes;
import com.kun.linkage.customer.facade.api.bean.res.OpenCardRes;
import com.kun.linkage.customer.facade.api.bean.res.OpenCardStatusQueryRes;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.*;
import com.kun.linkage.customer.facade.vo.mq.CancelCardRefundBalanceEventVO;
import com.kun.linkage.customer.facade.vo.mq.OpenCardEventVO;
import com.kun.linkage.customer.facade.vo.mq.OrganizationFeeDeductionEventVO;
import com.kun.linkage.customer.service.kyc.CustomerKycLevel1Service;
import com.kun.linkage.customer.service.organization.OrganizationFeeConfigBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;

/**
 * <p>
 * 卡管理业务账户服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class CardManagementBizService {
    private static final Logger log = LoggerFactory.getLogger(CardManagementBizService.class);
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private CardProductInfoMapper cardProductInfoMapper;
    @Resource
    private OrganizationApplicationCardMapper organizationApplicationCardMapper;
    @Resource
    private OrganizationCustomerCardOperationRecordMapper organizationCustomerCardOperationRecordMapper;
    @Resource
    private OrganizationCustomerBasicInfoMapper organizationCustomerBasicInfoMapper;
    @Resource
    private OrganizationCustomerCardInfoMapper organizationCustomerCardInfoMapper;
    @Resource
    private OrganizationCustomerAccountInfoMapper organizationCustomerAccountInfoMapper;
    @Resource
    private OrganizationCustomerCardOperationRecordBizService organizationCustomerCardOperationRecordBizService;
    @Resource
    private KCardCardManagementFacade kCardCardManagementFacade;
    @Resource
    private AccountInfoManagementFacade accountInfoManagementFacade;
    @Resource
    private CardManagementBizService cardManagementBizService;
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;
    @Resource
    private CustomerKycLevel1Service customerKycLevel1Service;
    @Resource
    private OrganizationFeeConfigBizService organizationFeeConfigBizService;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private ExchangeRateBizService exchangeRateBizService;


    /**
     * 开卡
     *
     * @param openCardReq
     * @return
     */
    public Result<OpenCardRes> openCard(OpenCardReq openCardReq) {
        // 校验是否重复请求
        Long num = organizationCustomerCardOperationRecordMapper.selectCount(Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, openCardReq.getOrganizationNo())
                .eq(OrganizationCustomerCardOperationRecord::getRequestNo, openCardReq.getRequestNo()));
        if (num != null && num > 0) {
            log.error("[卡片管理-开卡]重复请求,机构号:{},请求流水号:{}", openCardReq.getOrganizationNo(), openCardReq.getRequestNo());
            return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
        }
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, openCardReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-开卡]机构信息不存在,机构号:{}", openCardReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }

        String organizationNo = openCardReq.getOrganizationNo();
        String customerId = openCardReq.getCustomerId();
        //是否个人客户kyc报送;0否;1:是
        Integer isKycReported = organizationBasicInfo.getIsKycReported();
        if (YesFlagEnum.YES.getNumValue().equals(isKycReported)) {
            CustomerKycLevel1Info customerKycLevel1Info = customerKycLevel1Service.selectCustomerKycLevel1InfoByOrgNoAndCustomerId(organizationNo, customerId);
            //如果没有报送
            if (null == customerKycLevel1Info || YesFlagEnum.NO.getNumValue().equals(customerKycLevel1Info.getIsComplete())) {
                log.error("[卡片管理-开卡]机构暂无报送个人客户KYC信息:organizationNo{},customerId:{}", organizationNo, customerId);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_NOT_FOUND);
            }
            //存在校验 持卡人姓，持卡人名，持卡人国家，邮箱，区号+手机号
            String email = openCardReq.getEmail();
            if (!email.equals(customerKycLevel1Info.getEmail())) {
                log.error("[卡片管理-开卡]开卡邮箱与报送邮箱不一致:organizationNo{},customerId:{},email:{},开卡邮箱:{}", organizationNo, customerId,
                        customerKycLevel1Info.getEmail(), email);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_INCONSISTENT_INFORMATION);
            }

            String mobilePhoneArea = openCardReq.getMobilePhoneArea();
            if (StringUtils.isNotBlank(mobilePhoneArea) && !mobilePhoneArea.equals(customerKycLevel1Info.getPhoneArea())) {
                log.error("[卡片管理-开卡]开卡手机区号与报送手机区号不一致:organizationNo{},customerId:{},报送手机区号:{},开卡手机区号:{}", organizationNo, customerId,
                        customerKycLevel1Info.getPhoneArea(), mobilePhoneArea);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_INCONSISTENT_INFORMATION);
            }
            // 校验手机号码是否一致
            String mobilePhone = openCardReq.getMobilePhone();
            if (StringUtils.isNotBlank(mobilePhone) && !mobilePhone.equals(customerKycLevel1Info.getMobileNo())) {
                log.error("[卡片管理-开卡]开卡手机号码与报送手机号码不一致:organizationNo{},customerId:{},报送手机号码:{},开卡手机号码:{}", organizationNo, customerId,
                        customerKycLevel1Info.getMobileNo(), mobilePhone);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_INCONSISTENT_INFORMATION);
            }
            // 校验持卡人姓是否一致
            String cardHolderFirstName = openCardReq.getCardHolderFirstName();
            if (StringUtils.isNotBlank(cardHolderFirstName) && !cardHolderFirstName.equalsIgnoreCase(customerKycLevel1Info.getFirstName())) {
                log.error("[卡片管理-开卡]开卡持卡人姓与报送持卡人姓不一致:organizationNo{},customerId:{},报送持卡人姓:{},开卡持卡人姓:{}", organizationNo, customerId,
                        customerKycLevel1Info.getFirstName(), cardHolderFirstName);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_INCONSISTENT_INFORMATION);
            }
            // 校验持卡人名是否一致
            String cardHolderLastName = openCardReq.getCardHolderLastName();
            if (StringUtils.isNotBlank(cardHolderLastName) && !cardHolderLastName.equalsIgnoreCase(customerKycLevel1Info.getLastName())) {
                log.error("[卡片管理-开卡]开卡持卡人名与报送持卡人名不一致:organizationNo{},customerId:{},报送持卡人名:{},开卡持卡人名:{}", organizationNo, customerId,
                        customerKycLevel1Info.getLastName(), cardHolderLastName);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_INCONSISTENT_INFORMATION);
            }
            // 校验持卡人国家代码是否一致
            String cardHolderCountryNo = openCardReq.getCardHolderCountryNo();
            if (StringUtils.isNotBlank(cardHolderCountryNo) && !cardHolderCountryNo.equalsIgnoreCase(customerKycLevel1Info.getCountryNo())) {
                log.error("[卡片管理-开卡]开卡持卡人国家代码与报送持卡人国家代码不一致:organizationNo{},customerId:{},报送国家代码:{},开卡国家代码:{}", organizationNo, customerId,
                        customerKycLevel1Info.getCountryNo(), cardHolderCountryNo);
                return Result.fail(CustomerTipConstant.KYC_SUBMISSION_INCONSISTENT_INFORMATION);
            }
        }

        // 获取卡产品信息
        CardProductInfo cardProductInfo = cardProductInfoMapper.selectOne(Wrappers.<CardProductInfo>lambdaQuery()
                .eq(CardProductInfo::getCardProductCode, openCardReq.getCardProductCode())
                .eq(CardProductInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (cardProductInfo == null) {
            log.error("[卡片管理-开卡]卡产品信息不存在,卡产品编号:{}", openCardReq.getCardProductCode());
            return Result.fail(CustomerTipConstant.CARD_PRODUCT_NOT_FOUND);
        }
        if (StringUtils.isBlank(cardProductInfo.getSpecialUsage())) {
            log.error("[卡片管理-开卡]卡产品信息特殊用法不能为空,卡产品编号:{}", openCardReq.getCardProductCode());
            return Result.fail(CustomerTipConstant.CARD_PRODUCT_SPECIAL_USAGE_CANNOT_BE_EMPTY);
        }
        // 校验机构是否拥有该卡产品权限
        OrganizationApplicationCard organizationApplicationCard = organizationApplicationCardMapper.selectOne(Wrappers.<OrganizationApplicationCard>lambdaQuery()
                .eq(OrganizationApplicationCard::getOrganizationNo, openCardReq.getOrganizationNo())
                .eq(OrganizationApplicationCard::getCardProductCode, openCardReq.getCardProductCode())
                .eq(OrganizationApplicationCard::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationApplicationCard == null) {
            log.error("[卡片管理-开卡]机构卡产品信息不存在,机构号:{},卡产品编号:{}", openCardReq.getOrganizationNo(), openCardReq.getCardProductCode());
            return Result.fail(CustomerTipConstant.ORGANIZATION_HAS_NOT_ACTIVATED_CARD_PRODUCT);
        }
        // 校验是否存在处理中的开卡记录
        OrganizationCustomerCardOperationRecord organizationCustomerCardOperationRecord = organizationCustomerCardOperationRecordMapper.selectOne(Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, openCardReq.getOrganizationNo())
                .eq(OrganizationCustomerCardOperationRecord::getCustomerId, openCardReq.getCustomerId())
                .eq(OrganizationCustomerCardOperationRecord::getOperationType, CardOperationTypeEnum.OPEN_CARD.getType())
                .eq(OrganizationCustomerCardOperationRecord::getCardProductCode, openCardReq.getCardProductCode())
                .eq(OrganizationCustomerCardOperationRecord::getOperationStatus, OperationStatusEnum.PENDING.getStatus()));
        if (organizationCustomerCardOperationRecord != null) {
            log.error("[卡片管理-开卡]存在处理中的开卡记录,机构号:{},客户号:{},卡产品编号:{}",
                    openCardReq.getOrganizationNo(), openCardReq.getCustomerId(), openCardReq.getCardProductCode());
            // 此处需要将cardId返回回去,不然如果第一次开卡超时导致他没拿到cardId,则无法进行后续操作
            OpenCardRes openCardRes = new OpenCardRes();
            openCardRes.setCardId(organizationCustomerCardOperationRecord.getCardId());
            return Result.fail(CustomerTipConstant.ALREADY_EXIST_PENDING_OPEN_CARD_RECORD, openCardRes);
        }
        // 校验是否存在未注销的卡
        num = organizationCustomerCardInfoMapper.selectCount(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, openCardReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, openCardReq.getCustomerId())
                .ne(OrganizationCustomerCardInfo::getCardStatus, CardStatusEnum.CANCEL.getStatus())
                .eq(OrganizationCustomerCardInfo::getCardProductCode, openCardReq.getCardProductCode()));
        if (num != null && num > 0) {
            log.error("[卡片管理-开卡]存在未注销的卡,机构号:{},客户号:{},卡产品编号:{}",
                    openCardReq.getOrganizationNo(), openCardReq.getCustomerId(), openCardReq.getCardProductCode());
            return Result.fail(CustomerTipConstant.ALREADY_EXIST_CARD_INFO);
        }
        // 计算手续费,校验机构余额是否充足
        this.calculateFeeAmount(organizationBasicInfo, cardProductInfo.getCardProductCode(), cardProductInfo.getCardCurrencyCode(),
                OrganizationFeeTypeEnum.OPEN_CARD_FEE, null, BigDecimal.ZERO, null, true);
        // 调用KCard进行开卡
        try {
            KCardOpenCardReq kCardOpenCardReq = this.assKCardOpenCardReq(openCardReq, cardProductInfo);
            log.info("[卡片管理-开卡]调用KCard开卡开始,请求参数:{}", JSON.toJSONString(kCardOpenCardReq));
            KCardOpenCardRsp kCardOpenCardRsp = kCardCardManagementFacade.openCard(kCardOpenCardReq);
            log.info("[卡片管理-开卡]调用KCard开卡结束,响应参数:{}", JSON.toJSONString(kCardOpenCardRsp));
            if (kCardOpenCardRsp != null
                    && StringUtils.equals(kCardOpenCardRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())
                    && StringUtils.isNotBlank(kCardOpenCardRsp.getCardId())) {
                log.info("[卡片管理-开卡]调用KCard开卡请求成功,请求参数");
                // 开卡记录为开卡中
                // 保存卡操作记录表
                organizationCustomerCardOperationRecordBizService.insertOpenCardOperationRecord(
                        openCardReq, OperationStatusEnum.PENDING, null, kCardOpenCardRsp.getCardId());
                OpenCardRes openCardRes = new OpenCardRes();
                openCardRes.setCardId(kCardOpenCardRsp.getCardId());
                // 发送mq消息,异步获取开卡结果
                this.sendOpenCardEventToMq(openCardReq.getOrganizationNo(), openCardReq.getCustomerId(), kCardOpenCardRsp.getCardId());
                return Result.success(openCardRes);
            } else {
                // 开卡请求失败
                log.error("[卡片管理-开卡]调用KCard开卡请求失败");
                // 保存卡操作记录表
                organizationCustomerCardOperationRecordBizService.insertOpenCardOperationRecord(
                        openCardReq, OperationStatusEnum.FAIL,
                        "KCard调用失败,失败码:" + (kCardOpenCardRsp != null ? kCardOpenCardRsp.getCode() : null)
                                + ",失败信息:" + (kCardOpenCardRsp != null ? kCardOpenCardRsp.getMessage() : null), null);
                return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-开卡]调用KCard开卡异常,异常信息:", e);
            // 保存卡操作记录表
            organizationCustomerCardOperationRecordBizService.insertOpenCardOperationRecord(
                    openCardReq, OperationStatusEnum.FAIL, "KCard调用异常,请查看具体日志", null);
            return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
    }

    /**
     * 开卡状态查询
     *
     * @param openCardStatusQueryReq
     * @return
     */
    public Result<OpenCardStatusQueryRes> openCardStatusQuery(OpenCardStatusQueryReq openCardStatusQueryReq) {
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, openCardStatusQueryReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-开卡状态查询]机构信息不存在,机构号:{}", openCardStatusQueryReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        // 根据organizationNo+customerId+cardId+开卡操作类型获取申请卡操作记录
        OrganizationCustomerCardOperationRecord organizationCustomerCardOperationRecord = organizationCustomerCardOperationRecordMapper.selectOne(
                Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                        .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, openCardStatusQueryReq.getOrganizationNo())
                        .eq(OrganizationCustomerCardOperationRecord::getCustomerId, openCardStatusQueryReq.getCustomerId())
                        .eq(OrganizationCustomerCardOperationRecord::getOperationType, CardOperationTypeEnum.OPEN_CARD.getType())
                        .eq(OrganizationCustomerCardOperationRecord::getCardId, openCardStatusQueryReq.getCardId()));
        if (organizationCustomerCardOperationRecord == null) {
            log.error("[卡片管理-开卡状态查询]开卡操作记录不存在,机构号:{},客户号:{},卡id:{}",
                    openCardStatusQueryReq.getOrganizationNo(), openCardStatusQueryReq.getCustomerId(), openCardStatusQueryReq.getCardId());
            return Result.fail(CustomerTipConstant.OPEN_CARD_OPERATOR_RECORD_NOT_FOUND);
        }
        OrganizationApplicationCard organizationApplicationCard = organizationApplicationCardMapper.selectOne(
                Wrappers.<OrganizationApplicationCard>lambdaQuery()
                        .eq(OrganizationApplicationCard::getOrganizationNo, openCardStatusQueryReq.getOrganizationNo())
                        .eq(OrganizationApplicationCard::getCardProductCode, organizationCustomerCardOperationRecord.getCardProductCode())
                        .eq(OrganizationApplicationCard::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationApplicationCard == null) {
            log.error("[卡片管理-开卡状态查询]机构卡产品不存在,请检查,机构号:{},卡产品编号:{}",
                    openCardStatusQueryReq.getOrganizationNo(), organizationCustomerCardOperationRecord.getCardProductCode());
            return Result.fail(CustomerTipConstant.ORGANIZATION_HAS_NOT_ACTIVATED_CARD_PRODUCT);
        }
        OpenCardStatusQueryRes openCardStatusQueryRes = new OpenCardStatusQueryRes();
        openCardStatusQueryRes.setOpenCardStatus(organizationCustomerCardOperationRecord.getOperationStatus());
        if (StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OperationStatusEnum.SUCCESS.getStatus())
                || StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OperationStatusEnum.FAIL.getStatus())) {
            if (StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                // 开卡成功需要将是否自动激活标记返回
                openCardStatusQueryRes.setAutoActivationFlag(organizationApplicationCard.getAutoActivationFlag());
                // 开卡成功和失败直接返回结果
                if (StringUtils.isNotBlank(organizationCustomerCardOperationRecord.getCardNo())) {
                    String decryptCardNo = SensitiveInfoUtil.innerDecrypt(organizationCustomerCardOperationRecord.getCardNo());
                    openCardStatusQueryRes.setCardNo(SensitiveInfoUtil.encryptCBC(decryptCardNo, organizationBasicInfo.getSensitiveKey()));
                }
            }
            return Result.success(openCardStatusQueryRes);
        }
        // 开卡处理中的记录需要去KCard查询开卡结果
        RLock lock = null;
        try {
            // 使用机构号+客户号+卡id作为key
            String lockKey = CustomerLockConstant.OPEN_CARD_STATUS_QUERY_LOCK_PREFIX
                    + openCardStatusQueryReq.getOrganizationNo() + openCardStatusQueryReq.getCustomerId() + openCardStatusQueryReq.getCardId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.error("[卡片管理-开卡状态查询]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
            // 再次查询开卡状态防止别处已查询过
            organizationCustomerCardOperationRecord = organizationCustomerCardOperationRecordMapper.selectOne(
                    Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                            .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, openCardStatusQueryReq.getOrganizationNo())
                            .eq(OrganizationCustomerCardOperationRecord::getCustomerId, openCardStatusQueryReq.getCustomerId())
                            .eq(OrganizationCustomerCardOperationRecord::getOperationType, CardOperationTypeEnum.OPEN_CARD.getType())
                            .eq(OrganizationCustomerCardOperationRecord::getCardId, openCardStatusQueryReq.getCardId()));
            if (StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OperationStatusEnum.SUCCESS.getStatus())
                    || StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OperationStatusEnum.FAIL.getStatus())) {
                if (StringUtils.equals(organizationCustomerCardOperationRecord.getOperationStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                    // 开卡成功需要将是否自动激活标记返回
                    openCardStatusQueryRes.setAutoActivationFlag(organizationApplicationCard.getAutoActivationFlag());
                    // 开卡成功和失败直接返回结果
                    if (StringUtils.isNotBlank(organizationCustomerCardOperationRecord.getCardNo())) {
                        String decryptCardNo = SensitiveInfoUtil.innerDecrypt(organizationCustomerCardOperationRecord.getCardNo());
                        openCardStatusQueryRes.setCardNo(SensitiveInfoUtil.encryptCBC(decryptCardNo, organizationBasicInfo.getSensitiveKey()));
                    }
                }
                openCardStatusQueryRes.setOpenCardStatus(organizationCustomerCardOperationRecord.getOperationStatus());
                return Result.success(openCardStatusQueryRes);
            }
            // 调用KCard查询开卡状态
            KCardOpenCardQueryReq kCardOpenCardQueryReq = this.assKCardOpenCardStatusQueryReq(openCardStatusQueryReq);
            log.info("[卡片管理-开卡状态查询]调用KCard开卡状态查询开始,请求参数:{}", JSON.toJSONString(kCardOpenCardQueryReq));
            KCardOpenCardQueryRsp kCardOpenCardQueryRsp = kCardCardManagementFacade.openCardStatusQuery(kCardOpenCardQueryReq);
            log.info("[卡片管理-开卡状态查询]调用KCard开卡状态查询结束,响应参数:{}", JSON.toJSONString(kCardOpenCardQueryRsp));
            if (kCardOpenCardQueryRsp != null
                    && StringUtils.equals(kCardOpenCardQueryRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())) {
                log.info("[卡片管理-开卡状态查询]调用KCard开卡状态查询请求成功");
                // 需要看具体的状态
                if (StringUtils.equals(kCardOpenCardQueryRsp.getOpenCardStatus(), OpenCardStatusEnum.PENDING.getStatus())) {
                    // 开卡中的直接返回
                    return Result.success(openCardStatusQueryRes);
                } else if (StringUtils.equals(kCardOpenCardQueryRsp.getOpenCardStatus(), OpenCardStatusEnum.FAIL.getStatus())) {
                    // 开卡失败更新操作记录表为失败
                    organizationCustomerCardOperationRecordBizService
                            .updateCardOperationRecordStatus(organizationCustomerCardOperationRecord.getId(), organizationCustomerCardOperationRecord.getOrganizationNo(),
                                    null, null, OperationStatusEnum.FAIL, "查询KCard开卡状态响应开卡结果为失败,具体原因需查看KCard");
                    openCardStatusQueryRes.setOpenCardStatus(OpenCardStatusEnum.FAIL.getStatus());
                    return Result.success(openCardStatusQueryRes);
                } else if (StringUtils.equals(kCardOpenCardQueryRsp.getOpenCardStatus(), OpenCardStatusEnum.SUCCESS.getStatus())) {
                    // 开卡成功,更新操作记录表,新增用户信息,卡信息,账户信息
                    try {
                        cardManagementBizService.openCardSuccessProcess(organizationCustomerCardOperationRecord, kCardOpenCardQueryRsp.getCardNo());
                        // 开卡成功
                        openCardStatusQueryRes.setAutoActivationFlag(organizationApplicationCard.getAutoActivationFlag());
                        openCardStatusQueryRes.setOpenCardStatus(OpenCardStatusEnum.SUCCESS.getStatus());
                        if (StringUtils.isNotBlank(kCardOpenCardQueryRsp.getCardNo())) {
                            String decryptCardNo = SensitiveInfoUtil.innerDecrypt(kCardOpenCardQueryRsp.getCardNo());
                            openCardStatusQueryRes.setCardNo(SensitiveInfoUtil.encryptCBC(decryptCardNo, organizationBasicInfo.getSensitiveKey()));
                        }
                        return Result.success(openCardStatusQueryRes);
                    } catch (Exception e) {
                        // 开卡成功处理失败,返回机构端为开卡中(后续再次代用会继续进行尝试)
                        log.error("[卡片管理-开卡状态查询]开卡成功后生成开卡数据异常,请检查,返回机构端开卡中", e);
                        return Result.success(openCardStatusQueryRes);
                    }
                } else {
                    // 未知开卡状态
                    log.error("[卡片管理-开卡状态查询]调用KCard查询到的开卡状态未知,请检查,返回机构端开卡中");
                    return Result.success(openCardStatusQueryRes);
                }
            } else {
                // 开卡请求失败,返回机构端为开卡中
                log.error("[卡片管理-开卡状态查询]调用KCard开卡状态查询请求失败,返回机构端开卡中");
                return Result.success(openCardStatusQueryRes);
            }
        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-开卡状态查询]调用KCard开卡状态查询异常,异常信息:", e);
            return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 卡冻结/解冻
     *
     * @param cardFreezeAndUnfreezeReq
     * @return
     */
    public Result<Void> cardFreezeAndUnfreeze(CardFreezeAndUnfreezeReq cardFreezeAndUnfreezeReq) {
        // 校验是否重复请求
        Long num = organizationCustomerCardOperationRecordMapper.selectCount(Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, cardFreezeAndUnfreezeReq.getOrganizationNo())
                .eq(OrganizationCustomerCardOperationRecord::getRequestNo, cardFreezeAndUnfreezeReq.getRequestNo()));
        if (num != null && num > 0) {
            log.error("[卡片管理-冻结/解冻]重复请求,机构号:{},请求流水号:{}", cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getRequestNo());
            return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
        }
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, cardFreezeAndUnfreezeReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-冻结/解冻]机构信息不存在,机构号:{}", cardFreezeAndUnfreezeReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        // 根据organizationNo+customerId+cardId获取客户卡信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, cardFreezeAndUnfreezeReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cardFreezeAndUnfreezeReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, cardFreezeAndUnfreezeReq.getCardId()));
        if (organizationCustomerCardInfo == null) {
            log.error("[卡片管理-冻结/解冻]客户卡信息不存在,机构号:{},客户号:{},卡id:{}",
                    cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getCustomerId(), cardFreezeAndUnfreezeReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        if (StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.CANCEL.getStatus())) {
            log.error("[卡片管理-冻结/解冻]卡已注销,机构号:{},客户号:{},卡id:{}",
                    cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getCustomerId(), cardFreezeAndUnfreezeReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_HAS_BEEN_CANCELLED);
        }
        if (StringUtils.equals(cardFreezeAndUnfreezeReq.getOperationType(), CardOperationTypeEnum.FREEZE_CARD.getType())) {
            if (StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.FREEZE.getStatus())) {
                // 优化处理,直接返回成功
                log.warn("[卡片管理-冻结/解冻]卡已是冻结状态,直接返回成功,机构号:{},客户号:{},卡id:{}",
                        cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getCustomerId(), cardFreezeAndUnfreezeReq.getCardId());
                return Result.success();
            } else if (!StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.NORMAL.getStatus())) {
                // 如果要冻结卡,那卡之前的状态必须是正常
                log.error("[卡片管理-冻结/解冻]卡状态必须为正常,机构号:{},客户号:{},卡id:{}",
                        cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getCustomerId(), cardFreezeAndUnfreezeReq.getCardId());
                return Result.fail(CustomerTipConstant.CARD_STATUS_MUST_BE_NORMAL);
            }
        } else if (StringUtils.equals(cardFreezeAndUnfreezeReq.getOperationType(), CardOperationTypeEnum.UNFREEZE_CARD.getType())) {
            if (StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.NORMAL.getStatus())) {
                // 优化处理,直接返回成功
                log.warn("[卡片管理-冻结/解冻]卡已是正常状态,直接返回成功,机构号:{},客户号:{},卡id:{}",
                        cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getCustomerId(), cardFreezeAndUnfreezeReq.getCardId());
                return Result.success();
            } else if (!StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.FREEZE.getStatus())) {
                // 如果要解冻卡,那卡之前的状态必须是冻结
                log.error("[卡片管理-冻结/解冻]卡状态必须为冻结,机构号:{},客户号:{},卡id:{}",
                        cardFreezeAndUnfreezeReq.getOrganizationNo(), cardFreezeAndUnfreezeReq.getCustomerId(), cardFreezeAndUnfreezeReq.getCardId());
                return Result.fail(CustomerTipConstant.CARD_STATUS_MUST_BE_FREEZE);
            }
        } else {
            log.error("[卡片管理-冻结/解冻]操作类型非法,操作类型:{}", cardFreezeAndUnfreezeReq.getOperationType());
            return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
        }
        // 调用KCard进行冻结/解冻
        try {
            KCardCardFreezeReq kCardCardFreezeReq = this.assKCardFreezeReq(cardFreezeAndUnfreezeReq);
            log.info("[卡片管理-冻结/解冻]调用KCard冻结/解冻开始,请求参数:{}", JSON.toJSONString(kCardCardFreezeReq));
            KCardCardFreezeRsp kCardCardFreezeRsp = kCardCardManagementFacade.cardFreeze(kCardCardFreezeReq);
            log.info("[卡片管理-冻结/解冻]调用KCard冻结/解冻结束,响应参数:{}", JSON.toJSONString(kCardCardFreezeRsp));
            if (kCardCardFreezeRsp != null && StringUtils.equals(kCardCardFreezeRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())) {
                log.info("[卡片管理-冻结/解冻]调用KCard冻结/解冻请求成功");
                // 保存卡操作记录
                organizationCustomerCardOperationRecordBizService.insertCardFreezeAndUnfreezeOperationRecord(
                        cardFreezeAndUnfreezeReq, organizationCustomerCardInfo, OperationStatusEnum.SUCCESS, null);
                // 更新卡片状态
                OrganizationCustomerCardInfo updateOrganizationCustomerCardInfo = new OrganizationCustomerCardInfo();
                if (StringUtils.equals(cardFreezeAndUnfreezeReq.getOperationType(), CardOperationTypeEnum.UNFREEZE_CARD.getType())) {
                    updateOrganizationCustomerCardInfo.setCardStatus(CardStatusEnum.NORMAL.getStatus());
                } else {
                    updateOrganizationCustomerCardInfo.setCardStatus(CardStatusEnum.FREEZE.getStatus());
                }
                updateOrganizationCustomerCardInfo.setLastModifyTime(LocalDateTime.now());
                int row = organizationCustomerCardInfoMapper.update(updateOrganizationCustomerCardInfo,
                        Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                                .eq(OrganizationCustomerCardInfo::getId, organizationCustomerCardInfo.getId())
                                .eq(OrganizationCustomerCardInfo::getOrganizationNo, organizationCustomerCardInfo.getOrganizationNo()));
                log.info("update {} row OrganizationCustomerCardInfo", row);
                return Result.success();
            } else {
                // 开卡请求失败
                log.error("[卡片管理-冻结/解冻]调用KCard冻结/解冻请求失败");
                // 保存卡操作记录表
                organizationCustomerCardOperationRecordBizService.insertCardFreezeAndUnfreezeOperationRecord(
                        cardFreezeAndUnfreezeReq, organizationCustomerCardInfo, OperationStatusEnum.FAIL,
                        "KCard调用失败,失败码:" + (kCardCardFreezeRsp != null ? kCardCardFreezeRsp.getCode() : null)
                                + ",失败信息:" + (kCardCardFreezeRsp != null ? kCardCardFreezeRsp.getMessage() : null));
                return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-冻结/解冻]调用KCard冻结/解冻异常,异常信息:", e);
            // 保存卡操作记录表
            organizationCustomerCardOperationRecordBizService.insertCardFreezeAndUnfreezeOperationRecord(
                    cardFreezeAndUnfreezeReq, organizationCustomerCardInfo, OperationStatusEnum.FAIL, "KCard调用异常,请查看具体日志");
            return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
    }


    /**
     * 销卡
     *
     * @param cancelCardReq
     * @return
     */
    public Result<Void> cancelCard(CancelCardReq cancelCardReq) {
        // 校验是否重复请求
        Long num = organizationCustomerCardOperationRecordMapper.selectCount(Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, cancelCardReq.getOrganizationNo())
                .eq(OrganizationCustomerCardOperationRecord::getRequestNo, cancelCardReq.getRequestNo()));
        if (num != null && num > 0) {
            log.error("[卡片管理-销卡]重复请求,机构号:{},请求流水号:{}", cancelCardReq.getOrganizationNo(), cancelCardReq.getRequestNo());
            return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
        }
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, cancelCardReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-销卡]机构信息不存在,机构号:{}", cancelCardReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        if (StringUtils.isBlank(organizationBasicInfo.getPoolCurrencyCode())) {
            log.error("[卡片管理-销卡]机构资金池币种未配置,机构号:{}", organizationBasicInfo.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_POOL_CURRENCY_CODE_NOT_CONFIG);
        }
        // 根据organizationNo+customerId+cardId获取客户卡信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, cancelCardReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cancelCardReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, cancelCardReq.getCardId()));
        if (organizationCustomerCardInfo == null) {
            log.error("[卡片管理-销卡]客户卡信息不存在,机构号:{},客户号:{},卡id:{}",
                    cancelCardReq.getOrganizationNo(), cancelCardReq.getCustomerId(), cancelCardReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        if (StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.CANCEL.getStatus())) {
            // 优化处理，如果卡已注销，直接返回成功
            log.warn("[卡片管理-销卡]卡已注销,直接返成功,机构号:{},客户号:{},卡id:{}",
                    cancelCardReq.getOrganizationNo(), cancelCardReq.getCustomerId(), cancelCardReq.getCardId());
            return Result.success();
        }
        // 查询账户是否关联多张卡是，直接销卡，不需要退还账户余额，如果有且只有一张卡并且余额大于0，则先销卡，再退回余额，如果余额为0直接销卡
        QueryWrapper<OrganizationCustomerCardInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrganizationCustomerCardInfo::getOrganizationNo, cancelCardReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cancelCardReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCurrencyCode, organizationCustomerCardInfo.getCurrencyCode())
                .ne(OrganizationCustomerCardInfo::getCardStatus, CardStatusEnum.CANCEL.getStatus());
        long cardCnt = organizationCustomerCardInfoMapper.selectCount(queryWrapper);
        // 先获取换汇汇率,后面退还本金及承兑费也会用到
        BigDecimal fxRate = exchangeRateBizService.getCurrencyExchangeRate(organizationCustomerCardInfo.getCurrencyCode(),
                organizationBasicInfo.getPoolCurrencyCode(), BigDecimal.ZERO, organizationBasicInfo);
        // 销卡
        Result<Void> result = cardManagementBizService.processCardClosure(cancelCardReq, organizationCustomerCardInfo, organizationBasicInfo, fxRate);
        if (Result.isSuccess(result)) {
            log.info("[卡片管理-销卡]卡片注销成功");
            if (cardCnt > 1) {
                // 有多张卡片,直接就可以返回
                return result;
            } else {
                // 只有一张卡还需要查询是否有账户且账户余额是否大于0,大于0需要退还账户余额
                QueryWrapper<OrganizationCustomerAccountInfo> organizationCustomerAccountInfoQueryWrapper = new QueryWrapper<>();
                organizationCustomerAccountInfoQueryWrapper.lambda()
                        .eq(OrganizationCustomerAccountInfo::getOrganizationNo, organizationCustomerCardInfo.getOrganizationNo())
                        .eq(OrganizationCustomerAccountInfo::getCustomerId, organizationCustomerCardInfo.getCustomerId())
                        .eq(OrganizationCustomerAccountInfo::getCurrencyCode, organizationCustomerCardInfo.getCurrencyCode());
                OrganizationCustomerAccountInfo organizationCustomerAccountInfo = organizationCustomerAccountInfoMapper.selectOne(organizationCustomerAccountInfoQueryWrapper);
                if (organizationCustomerAccountInfo == null || StringUtils.isBlank(organizationCustomerAccountInfo.getAccountNo())) {
                    log.info("[卡片管理-销卡]不存在账户信息,无需退还余额,机构号:{},卡id:{}", organizationCustomerCardInfo.getOrganizationNo(), organizationCustomerCardInfo.getCardId());
                    return result;
                }
                QueryAccountInfoReq queryAccountInfoReq = new QueryAccountInfoReq();
                queryAccountInfoReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
                queryAccountInfoReq.setAccountNo(organizationCustomerAccountInfo.getAccountNo());
                queryAccountInfoReq.setBusinessOrganizationNo(organizationCustomerAccountInfo.getOrganizationNo());
                Result<QueryAccountInfoRes> queryAccountInfoResResult = accountInfoManagementFacade.queryAccountInfo(queryAccountInfoReq);
                log.info("[卡片管理-销卡]查询账户余额信息结果：{}", queryAccountInfoResResult);
                if (!Result.isSuccess(queryAccountInfoResResult) || queryAccountInfoResResult.getData() == null) {
                    log.error("[卡片管理-销卡]查询账户信息失败");
                    return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
                } else {
                    // 查询账户成功
                    if (queryAccountInfoResResult.getData().getAvailableAmount() != null
                            && queryAccountInfoResResult.getData().getAvailableAmount().compareTo(BigDecimal.ZERO) > 0) {
                        // 退还账户余额
                        this.sendCancelCardRefundBalanceToMq(cancelCardReq.getRequestNo(), organizationCustomerCardInfo.getCardId(),
                                queryAccountInfoResResult.getData().getAvailableAmount(), fxRate, organizationCustomerAccountInfo.getCurrencyCode(),
                                organizationCustomerAccountInfo.getAccountNo(), organizationBasicInfo);
                    } else {
                        log.warn("[卡片管理-销卡]账户余额为0,无需退还余额,机构号:{},账户号:{}",
                                organizationCustomerCardInfo.getOrganizationNo(), organizationCustomerAccountInfo.getAccountNo());
                        return result;
                    }
                }

            }
        } else {
            log.error("[卡片管理-销卡]卡片注销失败:{}", result);
            return result;
        }
        return Result.success();
    }

    /**
     * 卡激活
     *
     * @param cardActivationReq
     * @return
     */
    public Result<Void> cardActivation(CardActivationReq cardActivationReq) {
        // 校验是否重复请求
        Long num = organizationCustomerCardOperationRecordMapper.selectCount(Wrappers.<OrganizationCustomerCardOperationRecord>lambdaQuery()
                .eq(OrganizationCustomerCardOperationRecord::getOrganizationNo, cardActivationReq.getOrganizationNo())
                .eq(OrganizationCustomerCardOperationRecord::getRequestNo, cardActivationReq.getRequestNo()));
        if (num != null && num > 0) {
            log.error("[卡片管理-卡激活]重复请求,机构号:{},请求流水号:{}", cardActivationReq.getOrganizationNo(), cardActivationReq.getRequestNo());
            return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
        }
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, cardActivationReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-卡激活]机构信息不存在,机构号:{}", cardActivationReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        // 根据organizationNo+customerId+cardId获取客户卡信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, cardActivationReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cardActivationReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, cardActivationReq.getCardId()));
        if (organizationCustomerCardInfo == null) {
            log.error("[卡片管理-卡激活]客户卡信息不存在,机构号:{},客户号:{},卡id:{}",
                    cardActivationReq.getOrganizationNo(), cardActivationReq.getCustomerId(), cardActivationReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        if (!StringUtils.equals(organizationCustomerCardInfo.getProcessor(), ProcessorEnum.BPC_GW.getValue())) {
            log.error("[卡片管理-卡激活]该渠道卡片无需激活,机构号:{},客户号:{},卡id:{},渠道:{}",
                    cardActivationReq.getOrganizationNo(), cardActivationReq.getCustomerId(), cardActivationReq.getCardId(), organizationCustomerCardInfo.getProcessor());
            return Result.fail(CustomerTipConstant.CHANNEL_CARD_NOT_REQUIRE_ACTIVATION);
        }
        if (StringUtils.equals(organizationCustomerCardInfo.getCardActiveStatus(), CardActiveStatusEnum.ACTIVATED.getStatus())) {
            log.warn("[卡片管理-卡激活]卡已是激活状态,无需重复激活,机构号:{},客户号:{},卡id:{}",
                    cardActivationReq.getOrganizationNo(), cardActivationReq.getCustomerId(), cardActivationReq.getCardId());
            return Result.success();
        }
        // 调用KCard进行卡激活
        try {
            KCardCardActivationReq kCardCardActivationReq = this.assKCardCardActivationReq(cardActivationReq);
            log.info("[卡片管理-卡激活]调用KCard卡激活开始,请求参数:{}", JSON.toJSONString(kCardCardActivationReq));
            KCardCardActivationRsp kCardCardActivationRsp = kCardCardManagementFacade.cardActivation(kCardCardActivationReq);
            log.info("[卡片管理-卡激活]调用KCard卡激活结束,响应参数:{}", JSON.toJSONString(kCardCardActivationRsp));
            if (kCardCardActivationRsp != null && StringUtils.equals(kCardCardActivationRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())) {
                log.info("[卡片管理-卡激活]调用KCard卡激活请求成功");
                // 保存操作记录
                organizationCustomerCardOperationRecordBizService.insertCardActivationOperationRecord(
                        cardActivationReq, organizationCustomerCardInfo, OperationStatusEnum.SUCCESS, null);
                // 更新卡片激活状态
                OrganizationCustomerCardInfo updateOrganizationCustomerCardInfo = new OrganizationCustomerCardInfo();
                updateOrganizationCustomerCardInfo.setCardActiveStatus(CardActiveStatusEnum.ACTIVATED.getStatus());
                updateOrganizationCustomerCardInfo.setLastModifyTime(LocalDateTime.now());
                int row = organizationCustomerCardInfoMapper.update(updateOrganizationCustomerCardInfo,
                        Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                                .eq(OrganizationCustomerCardInfo::getId, organizationCustomerCardInfo.getId())
                                .eq(OrganizationCustomerCardInfo::getOrganizationNo, organizationCustomerCardInfo.getOrganizationNo()));
                log.info("update {} row OrganizationCustomerCardInfo", row);
                return Result.success();
            } else {
                // 卡激活请求失败
                log.error("[卡片管理-卡激活]调用KCard卡激活请求失败");
                // 保存卡操作记录表
                organizationCustomerCardOperationRecordBizService.insertCardActivationOperationRecord(
                        cardActivationReq, organizationCustomerCardInfo, OperationStatusEnum.FAIL,
                        "KCard调用失败,失败码:" + (kCardCardActivationRsp != null ? kCardCardActivationRsp.getCode() : null)
                                + ",失败信息:" + (kCardCardActivationRsp != null ? kCardCardActivationRsp.getMessage() : null));
                return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-卡激活]调用KCard销卡异常,异常信息:", e);
            // 保存卡操作记录表
            organizationCustomerCardOperationRecordBizService.insertCardActivationOperationRecord(
                    cardActivationReq, organizationCustomerCardInfo, OperationStatusEnum.FAIL, "KCard调用异常,请查看具体日志");
            return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
    }

    /**
     * 卡信息查询
     *
     * @param cardInfoQueryReq
     * @return
     */
    public Result<CardInfoQueryRes> cardInfoQuery(CardInfoQueryReq cardInfoQueryReq) {
        // 获取机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, cardInfoQueryReq.getOrganizationNo())
                .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfo == null) {
            log.error("[卡片管理-卡信息查询]机构信息不存在,机构号:{}", cardInfoQueryReq.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        // 根据organizationNo+customerId+cardId获取客户卡信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = organizationCustomerCardInfoMapper.selectOne(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, cardInfoQueryReq.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, cardInfoQueryReq.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, cardInfoQueryReq.getCardId()));
        if (organizationCustomerCardInfo == null) {
            log.error("[卡片管理-卡信息查询]客户卡信息不存在,机构号:{},客户号:{},卡id:{}",
                    cardInfoQueryReq.getOrganizationNo(), cardInfoQueryReq.getCustomerId(), cardInfoQueryReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_INFO_NOT_FOUND);
        }
        if (StringUtils.equals(organizationCustomerCardInfo.getCardStatus(), CardStatusEnum.CANCEL.getStatus())) {
            log.error("[卡片管理-卡信息查询]卡已注销,机构号:{},客户号:{},卡id:{}",
                    cardInfoQueryReq.getOrganizationNo(), cardInfoQueryReq.getCustomerId(), cardInfoQueryReq.getCardId());
            return Result.fail(CustomerTipConstant.CARD_HAS_BEEN_CANCELLED);
        }
        // 查询KCard获取卡片信息
        try {
            // 调用KCard查询卡信息
            KCardCardInfoQueryReq kCardCardInfoQueryReq = this.assKCardCardInfoQueryQueryReq(cardInfoQueryReq);
            log.info("[卡片管理-卡信息查询]调用KCard卡信息查询开始,请求参数:{}", JSON.toJSONString(kCardCardInfoQueryReq));
            KCardCardInfoQueryRsp kCardCardInfoQueryRsp = kCardCardManagementFacade.cardInfoQuery(kCardCardInfoQueryReq);
            log.info("[卡片管理-卡信息查询]调用KCard卡信息查询结束,响应参数:{}", JSON.toJSONString(kCardCardInfoQueryRsp));
            if (kCardCardInfoQueryRsp != null && StringUtils.equals(kCardCardInfoQueryRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())) {
                log.info("[卡片管理-卡信息查询]调用KCard卡信息查询请求成功");
                CardInfoQueryRes cardInfoQueryRes = new CardInfoQueryRes();
                BeanUtil.copyProperties(kCardCardInfoQueryRsp, cardInfoQueryRes);
                // 将敏感信息转加密
                if (StringUtils.isNotBlank(kCardCardInfoQueryRsp.getCardNo())) {
                    String cardNo = SensitiveInfoUtil.innerDecrypt(kCardCardInfoQueryRsp.getCardNo());
                    cardInfoQueryRes.setCardNo(SensitiveInfoUtil.encryptCBC(cardNo, organizationBasicInfo.getSensitiveKey()));
                    cardInfoQueryRes.setCardNoMask(SensitiveInfoUtil.mask(cardNo, 6, 4, '*'));
                }
                if (StringUtils.isNotBlank(kCardCardInfoQueryRsp.getCvv())) {
                    String cvv = SensitiveInfoUtil.innerDecrypt(kCardCardInfoQueryRsp.getCvv());
                    cardInfoQueryRes.setCvv(SensitiveInfoUtil.encryptCBC(cvv, organizationBasicInfo.getSensitiveKey()));
                }
                if (StringUtils.isNotBlank(kCardCardInfoQueryRsp.getExpiryDate())) {
                    String expireDate = SensitiveInfoUtil.innerDecrypt(kCardCardInfoQueryRsp.getExpiryDate());
                    cardInfoQueryRes.setExpiryDate(SensitiveInfoUtil.encryptCBC(expireDate, organizationBasicInfo.getSensitiveKey()));
                }
                return Result.success(cardInfoQueryRes);
            } else {
                // 卡信息查询请求失败
                log.error("[卡片管理-卡信息查询]调用KCard卡信息查询请求失败");
                return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-卡信息查询]调用KCard卡信息查询异常,异常信息:", e);
            return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
    }

    /**
     * 开卡成功处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void openCardSuccessProcess(OrganizationCustomerCardOperationRecord organizationCustomerCardOperationRecord, String cardNo) {
        String decryptCardNo = SensitiveInfoUtil.innerDecrypt(cardNo);
        String maskedCardNo = SensitiveInfoUtil.mask(decryptCardNo, 6, 4, '*');
        LocalDateTime now = LocalDateTime.now();
        log.info("[卡片管理-开卡状态查询]查询KCard开卡状态为开卡成功,开始生成卡片数据");
        // 更新操作记录表,新增用户信息,卡信息,账户信息
        organizationCustomerCardOperationRecordBizService.updateCardOperationRecordStatus(
                organizationCustomerCardOperationRecord.getId(), organizationCustomerCardOperationRecord.getOrganizationNo(),
                cardNo, maskedCardNo, OperationStatusEnum.SUCCESS, null);
        // 校验卡id是否已存在记录,以防万一
        Long num = organizationCustomerCardInfoMapper.selectCount(Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                .eq(OrganizationCustomerCardInfo::getOrganizationNo, organizationCustomerCardOperationRecord.getOrganizationNo())
                .eq(OrganizationCustomerCardInfo::getCustomerId, organizationCustomerCardOperationRecord.getCustomerId())
                .eq(OrganizationCustomerCardInfo::getCardId, organizationCustomerCardOperationRecord.getCardId())
                .ne(OrganizationCustomerCardInfo::getCardStatus, CardStatusEnum.CANCEL.getStatus()));
        if (num != null && num > 0) {
            log.warn("[卡片管理-开卡状态查询]开卡成功后准备生成卡片数据信息,但是数据已存在,直接返回开卡成功,请检查!!");
            return;
        }
        // 校验用户信息是否存在,不存在就新增
        num = organizationCustomerBasicInfoMapper.selectCount(Wrappers.<OrganizationCustomerBasicInfo>lambdaQuery()
                .eq(OrganizationCustomerBasicInfo::getOrganizationNo, organizationCustomerCardOperationRecord.getOrganizationNo())
                .eq(OrganizationCustomerBasicInfo::getCustomerId, organizationCustomerCardOperationRecord.getCustomerId()));
        if (num == null || num <= 0) {
            OrganizationCustomerBasicInfo organizationCustomerBasicInfo = new OrganizationCustomerBasicInfo();
            organizationCustomerBasicInfo.setOrganizationNo(organizationCustomerCardOperationRecord.getOrganizationNo());
            organizationCustomerBasicInfo.setCustomerId(organizationCustomerCardOperationRecord.getCustomerId());
            // 默认设置0级
            organizationCustomerBasicInfo.setCustomerLevel(CustomerLevelEnum.LEVEL0.getLevel());
            organizationCustomerBasicInfo.setStatus(ValidStatusEnum.VALID.getValue());
            LocalDateTime localDateTime = LocalDateTime.now();
            organizationCustomerBasicInfo.setCreateTime(localDateTime);
            organizationCustomerBasicInfo.setLastModifyTime(localDateTime);
            int row = organizationCustomerBasicInfoMapper.insert(organizationCustomerBasicInfo);
            log.info("insert {} row OrganizationCustomerBasicInfo", row);
        }
        //查询机构信息
        OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, organizationCustomerCardOperationRecord.getOrganizationNo()));
        //获取开卡时候的入参
        OpenCardReq openCardReq = JSONObject.parseObject(organizationCustomerCardOperationRecord.getRequestParams(), OpenCardReq.class);
        //查询是否报送个人客户KYC信息,如果是不需要报送的需要把持卡人姓,持卡人名,持卡人国家,邮箱,区号,手机号新增或者更新到用户一级信息中
        if (YesFlagEnum.NO.getNumValue().equals(organizationBasicInfo.getIsKycReported())) {
            customerKycLevel1Service.saveOrUpdateCustomerKycLevel1InfoByOpenCardReq(openCardReq);
        }
        CardProductInfo cardProductInfo = cardProductInfoMapper.selectOne(Wrappers.<CardProductInfo>lambdaQuery()
                .eq(CardProductInfo::getCardProductCode, organizationCustomerCardOperationRecord.getCardProductCode()));
        // 计算及收取机构手续费
        String feeDetailId = this.calculateFeeAmount(organizationBasicInfo, cardProductInfo.getCardProductCode(), cardProductInfo.getCardCurrencyCode(),
                OrganizationFeeTypeEnum.OPEN_CARD_FEE, organizationCustomerCardOperationRecord.getRequestNo(), BigDecimal.ZERO, null, false);
        // 生成卡片数据信息
        OrganizationCustomerCardInfo organizationCustomerCardInfo = new OrganizationCustomerCardInfo();
        organizationCustomerCardInfo.setOrganizationNo(organizationCustomerCardOperationRecord.getOrganizationNo());
        organizationCustomerCardInfo.setCustomerId(organizationCustomerCardOperationRecord.getCustomerId());
        organizationCustomerCardInfo.setCardId(organizationCustomerCardOperationRecord.getCardId());
        organizationCustomerCardInfo.setCardProductCode(organizationCustomerCardOperationRecord.getCardProductCode());
        organizationCustomerCardInfo.setCardNo(cardNo);
        organizationCustomerCardInfo.setMaskedCardNo(maskedCardNo);
        organizationCustomerCardInfo.setCurrencyCode(cardProductInfo.getCardCurrencyCode());
        organizationCustomerCardInfo.setCardScheme(cardProductInfo.getCardScheme());
        organizationCustomerCardInfo.setProcessor(cardProductInfo.getProcessor());
        organizationCustomerCardInfo.setCardStatus(CardStatusEnum.NORMAL.getStatus());
        organizationCustomerCardInfo.setCardActiveStatus(CardActiveStatusEnum.UNACTIVATED.getStatus());
        organizationCustomerCardInfo.setMobilePhoneArea(openCardReq.getMobilePhoneArea());
        organizationCustomerCardInfo.setMobilePhone(openCardReq.getMobilePhone());
        organizationCustomerCardInfo.setEmail(openCardReq.getEmail());
        organizationCustomerCardInfo.setCardholderFirstName(openCardReq.getCardHolderFirstName());
        organizationCustomerCardInfo.setCardholderLastName(openCardReq.getCardHolderLastName());
        organizationCustomerCardInfo.setOpenCardFeeDetailId(feeDetailId);
        organizationCustomerCardInfo.setCreateTime(now);
        organizationCustomerCardInfo.setLastModifyTime(now);
        int row = organizationCustomerCardInfoMapper.insert(organizationCustomerCardInfo);
        log.info("insert {} row OrganizationCustomerCardInfo", row);

        if (organizationBasicInfo.getCheckCustomerAccountFlag() != null && organizationBasicInfo.getCheckCustomerAccountFlag() == 1) {
            // 校验用户下是否已存在对应的币种账户,如果不存在则需要开户
            num = organizationCustomerAccountInfoMapper.selectCount(Wrappers.<OrganizationCustomerAccountInfo>lambdaQuery()
                    .eq(OrganizationCustomerAccountInfo::getOrganizationNo, organizationCustomerCardOperationRecord.getOrganizationNo())
                    .eq(OrganizationCustomerAccountInfo::getCustomerId, organizationCustomerCardOperationRecord.getCustomerId())
                    .eq(OrganizationCustomerAccountInfo::getCurrencyCode, cardProductInfo.getCardCurrencyCode())
                    .eq(OrganizationCustomerAccountInfo::getStatus, ValidStatusEnum.VALID.getValue()));
            if (num <= 0) {
                log.info("[卡片管理-开卡状态查询]客户id:{}, 币种账户:{}不存在,准备开户", organizationCustomerCardOperationRecord.getCustomerId(), cardProductInfo.getCardCurrencyCode());
                // 如果为机构配置Check Customer Account Flag为Yes则还需要为用户开一个账户
                String defaultUserId = String.valueOf(BaseVccBossController.DEFAULT_USER_ID);
                String defaultUserName = BaseVccBossController.DEFAULT_USER_NAME;
                OrganizationCustomerAccountInfo organizationCustomerAccountInfo = new OrganizationCustomerAccountInfo();
                organizationCustomerAccountInfo.setOrganizationNo(organizationCustomerCardOperationRecord.getOrganizationNo());
                organizationCustomerAccountInfo.setCustomerId(organizationCustomerCardOperationRecord.getCustomerId());
                organizationCustomerAccountInfo.setAccountType(BusinessAccountTypeEnum.BASIC.getValue());
                organizationCustomerAccountInfo.setCurrencyCode(cardProductInfo.getCardCurrencyCode());
                organizationCustomerAccountInfo.setStatus(ValidStatusEnum.VALID.getValue());
                organizationCustomerAccountInfo.setNetwork(BusinessAccountTypeMappingNetworkEnum.getCustomerNetworkByAccountType(BusinessAccountTypeEnum.BASIC.getValue()));
                organizationCustomerAccountInfo.setCreateTime(now);
                organizationCustomerAccountInfo.setCreateUserId(defaultUserId);
                organizationCustomerAccountInfo.setCreateUserName(defaultUserName);
                organizationCustomerAccountInfo.setLastModifyTime(now);
                organizationCustomerAccountInfo.setLastModifyUserId(defaultUserId);
                organizationCustomerAccountInfo.setLastModifyUserName(defaultUserName);
                // 调用账户服务开户
                CreateAccountReq createAccountReq = new CreateAccountReq();
                createAccountReq.setBusinessSystem(CommonConstant.KL);
                createAccountReq.setRequestNo(UUID.randomUUID().toString().replaceAll("-", ""));
                createAccountReq.setAccountType(BusinessAccountTypeEnum.BASIC.getValue());
                createAccountReq.setCurrencyCode(cardProductInfo.getCardCurrencyCode());
                createAccountReq.setMinimumAmount(BigDecimal.ZERO);
                createAccountReq.setBusinessOrganizationNo(organizationCustomerCardOperationRecord.getOrganizationNo());
                createAccountReq.setBusinessCustomerId(organizationCustomerCardOperationRecord.getCustomerId());
                log.info("[卡片管理-开卡状态查询]调用账户服务进行开户,请求参数:{}", JSON.toJSONString(createAccountReq));
                Result<CreateAccountRes> createAccountRes = accountInfoManagementFacade.createAccount(createAccountReq);
                log.info("[卡片管理-开卡状态查询]调用账户服务进行开户,响应参数:{}", JSON.toJSONString(createAccountRes));
                if (Result.isSuccess(createAccountRes) && createAccountRes.getData() != null && StringUtils.isNotBlank(createAccountRes.getData().getAccountNo())) {
                    log.info("[卡片管理-开卡状态查询]调用账户服务开户成功");
                    organizationCustomerAccountInfo.setAccountNo(createAccountRes.getData().getAccountNo());
                } else {
                    log.error("[卡片管理-开卡状态查询]调用账户服务开户失败,进行重试");
                    createAccountRes = accountInfoManagementFacade.createAccount(createAccountReq);
                    if (Result.isSuccess(createAccountRes) && createAccountRes.getData() != null && StringUtils.isNotBlank(createAccountRes.getData().getAccountNo())) {
                        log.info("[卡片管理-开卡状态查询]重试调用账户服务开户成功");
                        organizationCustomerAccountInfo.setAccountNo(createAccountRes.getData().getAccountNo());
                    } else {
                        log.error("[卡片管理-开卡状态查询]重试调用账户服务开户失败");
                        throw new BusinessException(CommonTipConstant.SYSTEM_INSIDE_ERROR);
                    }
                }
                row = organizationCustomerAccountInfoMapper.insert(organizationCustomerAccountInfo);
                log.info("insert {} row OrganizationCustomerAccountInfo", row);
            }
        }
        log.info("[卡片管理-开卡状态查询]卡片数据生成完成");
    }

    /**
     * 组装KCard开卡请求参数
     *
     * @param openCardReq
     * @return
     */
    private KCardOpenCardReq assKCardOpenCardReq(OpenCardReq openCardReq, CardProductInfo cardProductInfo) {
        KCardOpenCardReq kCardOpenCardReq = new KCardOpenCardReq();
        kCardOpenCardReq.setCustomerId(openCardReq.getOrganizationNo());
        kCardOpenCardReq.setRequestNo(openCardReq.getRequestNo());
        kCardOpenCardReq.setOutUserId(openCardReq.getCustomerId());
        kCardOpenCardReq.setOutUserName(openCardReq.getCustomerName());
        kCardOpenCardReq.setIp(openCardReq.getIp());
        kCardOpenCardReq.setCardProductCode(cardProductInfo.getCardProductCode());
        kCardOpenCardReq.setCardScheme(cardProductInfo.getCardScheme());
        kCardOpenCardReq.setProcessor(cardProductInfo.getProcessor());
        CardProductInfoSpecialUsageVO cardProductInfoSpecialUsageVO = JSONObject.parseObject(cardProductInfo.getSpecialUsage(), CardProductInfoSpecialUsageVO.class);
        kCardOpenCardReq.setCardMode(cardProductInfoSpecialUsageVO.getCardMode());
        kCardOpenCardReq.setCardType(cardProductInfoSpecialUsageVO.getCardType());
        kCardOpenCardReq.setOpenCurrency(cardProductInfo.getCardCurrencyCode());
        kCardOpenCardReq.setOpenAmount(BigDecimal.ZERO);
        KCardOpenCardReq.Meta meta = new KCardOpenCardReq.Meta();
        meta.setDialCode(openCardReq.getMobilePhoneArea());
        meta.setPhone(openCardReq.getMobilePhone());
        meta.setEmail(openCardReq.getEmail());
        meta.setCardName(cardProductInfo.getCardProductName());
        meta.setCardHolderFirstName(openCardReq.getCardHolderFirstName());
        meta.setCardHolderLastName(openCardReq.getCardHolderLastName());
        meta.setCardHolderCountryNo(openCardReq.getCardHolderCountryNo());
        kCardOpenCardReq.setMeta(meta);
        return kCardOpenCardReq;
    }

    /**
     * 组装KCard卡状态查询请求参数
     *
     * @param openCardStatusQueryReq
     * @return
     */
    private KCardOpenCardQueryReq assKCardOpenCardStatusQueryReq(OpenCardStatusQueryReq openCardStatusQueryReq) {
        KCardOpenCardQueryReq kCardOpenCardQueryReq = new KCardOpenCardQueryReq();
        kCardOpenCardQueryReq.setCustomerId(openCardStatusQueryReq.getOrganizationNo());
        kCardOpenCardQueryReq.setRequestNo(openCardStatusQueryReq.getRequestNo());
        kCardOpenCardQueryReq.setIp(openCardStatusQueryReq.getIp());
        kCardOpenCardQueryReq.setCardId(openCardStatusQueryReq.getCardId());
        return kCardOpenCardQueryReq;
    }

    /**
     * 组装KCard卡信息查询请求参数
     *
     * @param cardInfoQueryReq
     * @return
     */
    private KCardCardInfoQueryReq assKCardCardInfoQueryQueryReq(CardInfoQueryReq cardInfoQueryReq) {
        KCardCardInfoQueryReq kCardCardInfoQueryReq = new KCardCardInfoQueryReq();
        kCardCardInfoQueryReq.setCustomerId(cardInfoQueryReq.getOrganizationNo());
        kCardCardInfoQueryReq.setRequestNo(cardInfoQueryReq.getRequestNo());
        kCardCardInfoQueryReq.setIp(cardInfoQueryReq.getIp());
        kCardCardInfoQueryReq.setCardId(cardInfoQueryReq.getCardId());
        return kCardCardInfoQueryReq;
    }

    /**
     * 组装KCard冻结/解冻请求参数
     *
     * @param cardFreezeAndUnfreezeReq
     * @return
     */
    private KCardCardFreezeReq assKCardFreezeReq(CardFreezeAndUnfreezeReq cardFreezeAndUnfreezeReq) {
        KCardCardFreezeReq kCardCardFreezeReq = new KCardCardFreezeReq();
        kCardCardFreezeReq.setCustomerId(cardFreezeAndUnfreezeReq.getOrganizationNo());
        kCardCardFreezeReq.setRequestNo(cardFreezeAndUnfreezeReq.getRequestNo());
        kCardCardFreezeReq.setIp(cardFreezeAndUnfreezeReq.getIp());
        kCardCardFreezeReq.setCardId(cardFreezeAndUnfreezeReq.getCardId());
        kCardCardFreezeReq.setOperationType(
                StringUtils.equals(cardFreezeAndUnfreezeReq.getOperationType(), CardOperationTypeEnum.FREEZE_CARD.getType()) ? "FREEZE" : "THAWED");
        return kCardCardFreezeReq;
    }

    /**
     * 组装KCard销卡请求参数
     *
     * @param cancelCardReq
     * @return
     */
    private KCardCancelCardReq assKCardCancelCardReq(CancelCardReq cancelCardReq) {
        KCardCancelCardReq kCardCancelCardReq = new KCardCancelCardReq();
        kCardCancelCardReq.setCustomerId(cancelCardReq.getOrganizationNo());
        kCardCancelCardReq.setRequestNo(cancelCardReq.getRequestNo());
        kCardCancelCardReq.setIp(cancelCardReq.getIp());
        kCardCancelCardReq.setCardId(cancelCardReq.getCardId());
        return kCardCancelCardReq;
    }

    /**
     * 组装KCard卡激活请求参数
     *
     * @param cardActivationReq
     * @return
     */
    private KCardCardActivationReq assKCardCardActivationReq(CardActivationReq cardActivationReq) {
        KCardCardActivationReq kCardCardActivationReq = new KCardCardActivationReq();
        kCardCardActivationReq.setCustomerId(cardActivationReq.getOrganizationNo());
        kCardCardActivationReq.setRequestNo(cardActivationReq.getRequestNo());
        kCardCardActivationReq.setIp(cardActivationReq.getIp());
        kCardCardActivationReq.setCardId(cardActivationReq.getCardId());
        return kCardCardActivationReq;
    }

    /**
     * 发送开卡事件到mq
     *
     * @param organizationNo
     * @param customerId
     * @param cardId
     */
    private void sendOpenCardEventToMq(String organizationNo, String customerId, String cardId) {
        log.info("[卡片管理-开卡]发送开卡事件到Mq");
        OpenCardEventVO openCardEventVO = new OpenCardEventVO();
        openCardEventVO.setOrganizationNo(organizationNo);
        openCardEventVO.setCustomerId(customerId);
        openCardEventVO.setCardId(cardId);
        rocketMqService.delayedSend(MqTopicConstant.OPEN_CARD_EVENT_TOPIC, openCardEventVO, 10000, MqTopicConstant.DELAY_LEVEL_5M);
    }

    /**
     * 销卡操作
     *
     * @param cancelCardReq
     * @param organizationCustomerCardInfo
     * @param organizationBasicInfo
     * @param fxRate
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> processCardClosure(CancelCardReq cancelCardReq, OrganizationCustomerCardInfo organizationCustomerCardInfo,
                                           OrganizationBasicInfo organizationBasicInfo, BigDecimal fxRate) {
        // 调用KCard进行销卡
        try {
            KCardCancelCardReq kCardCancelCardReq = this.assKCardCancelCardReq(cancelCardReq);
            log.info("[卡片管理-销卡]调用KCard销卡开始,请求参数:{}", JSON.toJSONString(kCardCancelCardReq));
            KCardCancelCardRsp kCardCancelCardRsp = kCardCardManagementFacade.cancelCard(kCardCancelCardReq);
            log.info("[卡片管理-销卡]调用KCard销卡结束,响应参数:{}", JSON.toJSONString(kCardCancelCardRsp));
            if (kCardCancelCardRsp != null && StringUtils.equals(kCardCancelCardRsp.getCode(), KCardApiErrorCode.SUCCESS.getCode())) {
                log.info("[卡片管理-销卡]调用KCard销卡请求成功");
                // 收取销卡手续费
                String feeDetailId = this.calculateFeeAmount(organizationBasicInfo, organizationCustomerCardInfo.getCardProductCode(), organizationCustomerCardInfo.getCurrencyCode(),
                        OrganizationFeeTypeEnum.CANCEL_CARD_FEE, cancelCardReq.getRequestNo(), BigDecimal.ZERO, fxRate, false);
                // 保存操作记录
                organizationCustomerCardOperationRecordBizService.insertCancelCardOperationRecord(
                        cancelCardReq, organizationCustomerCardInfo, OperationStatusEnum.SUCCESS, null);
                // 更新卡片状态
                OrganizationCustomerCardInfo updateOrganizationCustomerCardInfo = new OrganizationCustomerCardInfo();
                updateOrganizationCustomerCardInfo.setCardStatus(CardStatusEnum.CANCEL.getStatus());
                updateOrganizationCustomerCardInfo.setCancelCardFeeDetailId(feeDetailId);
                updateOrganizationCustomerCardInfo.setLastModifyTime(LocalDateTime.now());
                int row = organizationCustomerCardInfoMapper.update(updateOrganizationCustomerCardInfo,
                        Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                                .eq(OrganizationCustomerCardInfo::getId, organizationCustomerCardInfo.getId())
                                .eq(OrganizationCustomerCardInfo::getOrganizationNo, organizationCustomerCardInfo.getOrganizationNo()));
                log.info("update {} row OrganizationCustomerCardInfo", row);
                return Result.success();
            } else {
                // 开卡请求失败
                log.error("[卡片管理-销卡]调用KCard销卡请求失败");
                // 保存卡操作记录表
                organizationCustomerCardOperationRecordBizService.insertCancelCardOperationRecord(
                        cancelCardReq, organizationCustomerCardInfo, OperationStatusEnum.FAIL,
                        "KCard调用失败,失败码:" + (kCardCancelCardRsp != null ? kCardCancelCardRsp.getCode() : null)
                                + ",失败信息:" + (kCardCancelCardRsp != null ? kCardCancelCardRsp.getMessage() : null));
                return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        } catch (Exception e) {
            // 调用KCard异常
            log.error("[卡片管理-销卡]调用KCard销卡异常,异常信息:", e);
            // 保存卡操作记录表
            organizationCustomerCardOperationRecordBizService.insertCancelCardOperationRecord(
                    cancelCardReq, organizationCustomerCardInfo, OperationStatusEnum.FAIL, "KCard调用异常,请查看具体日志");
            return Result.fail(CustomerTipConstant.CALL_CHANNEL_FAIL);
        }
    }

    /**
     * 销卡计算承兑费
     * @param eventVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelCardCalculateAcceptanceFee(CancelCardRefundBalanceEventVO eventVO) {
        // 数据都有此处直接组装
        OrganizationBasicInfo organizationBasicInfo = new OrganizationBasicInfo();
        organizationBasicInfo.setOrganizationNo(eventVO.getOrganizationNo());
        organizationBasicInfo.setMpcToken(eventVO.getMpcToken());
        organizationBasicInfo.setMpcGroupCode(eventVO.getMpcGroupCode());
        organizationBasicInfo.setPoolCurrencyCode(eventVO.getRefundCurrencyCode());
        String feeDetailId = this.calculateFeeAmount(organizationBasicInfo, eventVO.getCardProductCode(),
                eventVO.getCustomerAccountCurrencyCode(), OrganizationFeeTypeEnum.ACCEPTANCE_FEE, eventVO.getLockRequestNo(),
                eventVO.getCustomerAccountBalance(), eventVO.getFxRate(), false);
        // 将费用明细id更新回卡记录信息表中
        // 更新卡片状态
        OrganizationCustomerCardInfo updateOrganizationCustomerCardInfo = new OrganizationCustomerCardInfo();
        updateOrganizationCustomerCardInfo.setCancelCardAcceptanceFeeDetailId(feeDetailId);
        updateOrganizationCustomerCardInfo.setLastModifyTime(LocalDateTime.now());
        int row = organizationCustomerCardInfoMapper.update(updateOrganizationCustomerCardInfo,
                Wrappers.<OrganizationCustomerCardInfo>lambdaQuery()
                        .eq(OrganizationCustomerCardInfo::getCardProductCode, eventVO.getCardProductCode())
                        .eq(OrganizationCustomerCardInfo::getCardId, eventVO.getCardId())
                        .eq(OrganizationCustomerCardInfo::getOrganizationNo, eventVO.getOrganizationNo()));
        log.info("update {} row OrganizationCustomerCardInfo", row);
    }

    /**
     * 发送销卡退还余额事件到mq
     *
     * @param requestNo
     * @param cardId
     * @param customerAvailableAmount
     * @param fxRate
     * @param customerCurrencyCode
     * @param accountNo
     * @param organizationBasicInfo
     */
    private void sendCancelCardRefundBalanceToMq(String requestNo, String cardId, BigDecimal customerAvailableAmount, BigDecimal fxRate,
                                                 String customerCurrencyCode, String accountNo, OrganizationBasicInfo organizationBasicInfo) {
        log.info("[卡片管理-销卡]发送退还余额事件到mq中");
        CancelCardRefundBalanceEventVO cancelCardRefundBalanceEventVO = new CancelCardRefundBalanceEventVO();
        cancelCardRefundBalanceEventVO.setMpcToken(organizationBasicInfo.getMpcToken());
        cancelCardRefundBalanceEventVO.setMpcGroupCode(organizationBasicInfo.getMpcGroupCode());
        cancelCardRefundBalanceEventVO.setOrganizationNo(organizationBasicInfo.getOrganizationNo());
        cancelCardRefundBalanceEventVO.setCardId(cardId);
        cancelCardRefundBalanceEventVO.setLockRequestNo(requestNo);
        cancelCardRefundBalanceEventVO.setCustomerAccountNo(accountNo);
        cancelCardRefundBalanceEventVO.setCustomerAccountBalance(customerAvailableAmount);
        cancelCardRefundBalanceEventVO.setCustomerAccountCurrencyCode(customerCurrencyCode);
        if (DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
            cancelCardRefundBalanceEventVO.setRefundOrganizationProcessor(DeductProcessorEnum.KUN.getValue());
        } else {
            cancelCardRefundBalanceEventVO.setRefundOrganizationProcessor(DeductProcessorEnum.PAYX.getValue());
        }
        BigDecimal refundOrganizationAmount = customerAvailableAmount.multiply(fxRate).setScale(
                OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(organizationBasicInfo.getPoolCurrencyCode()), RoundingMode.DOWN);
        cancelCardRefundBalanceEventVO.setRefundOrganizationAmount(refundOrganizationAmount);
        cancelCardRefundBalanceEventVO.setRefundCurrencyCode(organizationBasicInfo.getPoolCurrencyCode());
        cancelCardRefundBalanceEventVO.setFxRate(fxRate);
        cancelCardRefundBalanceEventVO.setCustomerBookkeepingStatus(YesFlagEnum.NO.getNumValue());
        cancelCardRefundBalanceEventVO.setOrganizationBookkeepingStatus(YesFlagEnum.NO.getNumValue());
        cancelCardRefundBalanceEventVO.setCallCount(0);
        rocketMqService.delayedSend(MqTopicConstant.CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC, cancelCardRefundBalanceEventVO, 10000, MqTopicConstant.DELAY_LEVEL_10S);
    }

    /**
     * 组装kun账户余额查询请求参数
     *
     * @param currency
     * @param organizationBasicInfo
     * @return
     */
    private KunQueryBalanceReq assKunQueryBalanceReq(String currency, OrganizationBasicInfo organizationBasicInfo) {
        KunQueryBalanceReq kunQueryBalanceReq = new KunQueryBalanceReq();
        kunQueryBalanceReq.setToken(organizationBasicInfo.getMpcToken());
        kunQueryBalanceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunQueryBalanceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunQueryBalanceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunQueryBalanceReq.setCurrency(currency);
        return kunQueryBalanceReq;
    }

    /**
     * 组装payx账户余额查询请求参数
     *
     * @param currency
     * @param organizationBasicInfo
     * @return
     */
    private PayXQueryBalanceReq assPayxQueryBalanceReq(String currency, OrganizationBasicInfo organizationBasicInfo) {
        PayXQueryBalanceReq queryBalnaceReq = new PayXQueryBalanceReq();
        queryBalnaceReq.setToken(organizationBasicInfo.getMpcToken());
        queryBalnaceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        queryBalnaceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        queryBalnaceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        queryBalnaceReq.setCurrency(currency);
        return queryBalnaceReq;
    }

    /**
     * 计算手续费
     *
     * @param organizationBasicInfo
     * @param cardProductCode
     * @param currencyCode
     * @param feeTypeEnum
     * @param relatedTransactionId  关联的交易流水id(费用明细中要存)
     * @param trialCalculationFlag  试算标记,true的时候为试算,会调用余额查询接口进行校验,false的时候为真实扣账
     */
    public String calculateFeeAmount(OrganizationBasicInfo organizationBasicInfo, String cardProductCode, String currencyCode, OrganizationFeeTypeEnum feeTypeEnum,
                                     String relatedTransactionId, BigDecimal amount, BigDecimal fxRate, boolean trialCalculationFlag) {
        try {
            log.info("开始计算手续费");
            String feeDetailId = null;
            String organizationNo = organizationBasicInfo.getOrganizationNo();
            BigDecimal deductFeeAmount = BigDecimal.ZERO;
            // 获取费率配置
            OrganizationFeeConfig organizationFeeConfig = organizationFeeConfigBizService.getValidOrganizationFeeConfigByWhere(organizationNo, cardProductCode);
            if (organizationFeeConfig == null) {
                log.warn("未找到机构费率配置信息,手续费按0处理,机构号:{},卡产品编号:{}", organizationNo, cardProductCode);
            } else {
                LambdaQueryWrapper<OrganizationFeeTemplateDetail> wrapper = Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                        .eq(OrganizationFeeTemplateDetail::getTemplateNo, organizationFeeConfig.getTemplateNo())
                        .eq(OrganizationFeeTemplateDetail::getFeeType, feeTypeEnum.getValue())
                        .eq(OrganizationFeeTemplateDetail::getCurrencyCode, currencyCode)
                        .in(OrganizationFeeTemplateDetail::getBillingDimension,
                                Arrays.asList(OrganizationFeeBillingDimensionEnum.SINGLE_AMOUNT.getValue(), OrganizationFeeBillingDimensionEnum.TIERED_SINGLE_AMOUNT.getValue()));
                if (amount.compareTo(BigDecimal.ZERO) > 0) {
                    // 左开右闭
                    wrapper.lt(OrganizationFeeTemplateDetail::getMinAmount, amount);
                    wrapper.ge(OrganizationFeeTemplateDetail::getMaxAmount, amount);
                }
                OrganizationFeeTemplateDetail organizationFeeTemplateDetail = organizationFeeTemplateDetailMapper.selectOne(wrapper);
                // 此处是开卡和销卡的手续费计算,因为开卡和销卡都没有金额,所以此处不用卡金额区间
                if (organizationFeeTemplateDetail == null) {
                    log.warn("费率类型:{},未找到机构费率配置明细,手续费按0处理,机构号:{},卡产品编号:{},币种:{},模版号:{}", feeTypeEnum.getValue(),
                            organizationNo, cardProductCode, currencyCode, organizationFeeConfig.getTemplateNo());
                } else {
                    if (fxRate == null) {
                        // 获取汇率信息
                        fxRate = exchangeRateBizService.getCurrencyExchangeRate(currencyCode, organizationBasicInfo.getPoolCurrencyCode(), amount, organizationBasicInfo);
                    }
                    BigDecimal proportionRate = organizationFeeTemplateDetail.getProportionRate() != null ? organizationFeeTemplateDetail.getProportionRate() : BigDecimal.ZERO;
                    BigDecimal fixedAmount = organizationFeeTemplateDetail.getFixedAmount() != null ? organizationFeeTemplateDetail.getFixedAmount() : BigDecimal.ZERO;
                    BigDecimal proportionFeeAmount = BigDecimal.ZERO;
                    if (amount.compareTo(BigDecimal.ZERO) > 0) {
                        proportionFeeAmount = amount.multiply(proportionRate);
                        if (organizationFeeTemplateDetail.getProportionMinAmount() != null
                                && proportionFeeAmount.compareTo(organizationFeeTemplateDetail.getProportionMinAmount()) < 0) {
                            proportionFeeAmount = organizationFeeTemplateDetail.getProportionMinAmount();
                        } else if (organizationFeeTemplateDetail.getProportionMaxAmount() != null
                                && proportionFeeAmount.compareTo(organizationFeeTemplateDetail.getProportionMaxAmount()) > 0) {
                            proportionFeeAmount = organizationFeeTemplateDetail.getProportionMaxAmount();
                        }
                    }
                    BigDecimal feeAmount = proportionFeeAmount.add(fixedAmount);
                    Integer poolCurrencyPrecision = OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(organizationBasicInfo.getPoolCurrencyCode());
                    // 此处是开卡和销卡的手续费计算,因为开卡和销卡都没有金额,所以手续费都只会配置固定值
                    deductFeeAmount = feeAmount.multiply(fxRate).setScale(poolCurrencyPrecision, RoundingMode.UP);
                    if (deductFeeAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (trialCalculationFlag) {
                            log.info("试算手续费,只校验机构余额,不保存手续费记录,手续费类型:{}", feeTypeEnum.getValue());
                            this.checkOrganizationAccountBalance(organizationBasicInfo, deductFeeAmount, organizationBasicInfo.getPoolCurrencyCode());
                        } else {
                            log.info("需要保存手续费记录,手续费类型:{}", feeTypeEnum.getValue());
                            // 保存费用明细记录
                            LocalDateTime now = LocalDateTime.now().withNano(0);
                            OrganizationFeeDetail organizationFeeDetail = new OrganizationFeeDetail();
                            organizationFeeDetail.setOrganizationNo(organizationNo);
                            organizationFeeDetail.setCardProductCode(cardProductCode);
                            organizationFeeDetail.setRelatedTransactionId(relatedTransactionId);
                            organizationFeeDetail.setCalculateDatetime(now);
                            organizationFeeDetail.setTransactionDatetime(now);
                            organizationFeeDetail.setFeeType(organizationFeeTemplateDetail.getFeeType());
                            organizationFeeDetail.setFeeCollectionMethod(organizationFeeTemplateDetail.getCollectionMethod());
                            organizationFeeDetail.setTransactionAmount(amount);
                            organizationFeeDetail.setTransactionCurrencyCode(currencyCode);
                            organizationFeeDetail.setTransactionCurrencyPrecision(2);
                            organizationFeeDetail.setFeeAmount(feeAmount);
                            organizationFeeDetail.setSnapshotBillingDimension(organizationFeeTemplateDetail.getBillingDimension());
                            organizationFeeDetail.setSnapshotMinAmount(organizationFeeTemplateDetail.getMinAmount());
                            organizationFeeDetail.setSnapshotMaxAmount(organizationFeeTemplateDetail.getMaxAmount());
                            organizationFeeDetail.setSnapshotProportionRate(proportionRate);
                            organizationFeeDetail.setSnapshotProportionMinAmount(organizationFeeTemplateDetail.getProportionMinAmount());
                            organizationFeeDetail.setSnapshotProportionMaxAmount(organizationFeeTemplateDetail.getProportionMaxAmount());
                            organizationFeeDetail.setSnapshotFixedAmount(fixedAmount);
                            organizationFeeDetail.setFxRate(fxRate);
                            // 此处都先设置为未收,实时收取的后面发送mq消息进行收取
                            organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.NOT_COLLECTED.getValue());
                            if (DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
                                // 扣款币种是数币
                                organizationFeeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
                            } else {
                                // 扣款币种是法币
                                organizationFeeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());
                            }
                            organizationFeeDetail.setDeductFeeAmount(deductFeeAmount);
                            organizationFeeDetail.setDeductCurrencyCode(organizationBasicInfo.getPoolCurrencyCode());
                            organizationFeeDetail.setDeductCurrencyPrecision(poolCurrencyPrecision);
                            organizationFeeDetail.setCallCount(0);
                            organizationFeeDetail.setCreateTime(now);
                            organizationFeeDetail.setLastModifyTime(now);
                            organizationFeeDetailMapper.insert(organizationFeeDetail);
                            feeDetailId = organizationFeeDetail.getId();
                            log.info("费率类型:{},生成费用明细记录成功,记录id:{}", feeTypeEnum.getValue(), organizationFeeDetail.getId());
                            if (StringUtils.equals(OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue(), organizationFeeTemplateDetail.getCollectionMethod())) {
                                log.info("费率类型:{},收取方式为实时收取,发送收取消息到mq,记录id:{}", feeTypeEnum.getValue(), organizationFeeDetail.getId());
                                this.sendOrganizationFeeDeductionToMq(organizationFeeDetail, organizationBasicInfo.getMpcToken(), organizationBasicInfo.getMpcGroupCode());
                            }
                        }
                    } else {
                        log.info("费率类型:{},费用为0,直接跳过", feeTypeEnum.getValue());
                    }
                }
                log.info("费率类型:{},手续费计算完成,最终手续费为:{}, 币种:{}", feeTypeEnum.getValue(), deductFeeAmount, organizationBasicInfo.getPoolCurrencyCode());
            }
            return feeDetailId;
        } catch (Exception e) {
            log.error("手续费计算异常", e);
            return null;
        }
    }

    /**
     * 校验机构账户余额是否充足
     *
     * @param organizationBasicInfo
     * @param amount
     * @param currencyCode
     */
    private void checkOrganizationAccountBalance(OrganizationBasicInfo organizationBasicInfo, BigDecimal amount, String currencyCode) {
        if (DigitalCurrencyEnum.contains(currencyCode)) {
            // 扣款币种是数币
            KunQueryBalanceReq queryBalanceReq = this.assKunQueryBalanceReq(currencyCode, organizationBasicInfo);
            log.info("调用KUN账户余额查询接口开始,请求参数:{}", JSON.toJSONString(queryBalanceReq));
            Result<KunQueryBalanceRsp> result = kCardKunAccountFacade.kunQueryBalnace(queryBalanceReq);
            log.info("调用KUN账户余额查询接口结束,响应参数:{}", JSON.toJSONString(result));
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && result.getData() != null && result.getData().getBalance() != null) {
                // 有正确响应结果,校验余额是否充足
                if (result.getData().getBalance().compareTo(amount) >= 0) {
                    log.info("机构账户余额充足,机构号:{},币种:{},余额:{},待扣款金额:{}",
                            organizationBasicInfo.getOrganizationNo(), currencyCode, result.getData().getBalance(), amount);
                } else {
                    log.error("机构账户余额不足,机构号:{},币种:{},余额:{},待扣款金额:{}",
                            organizationBasicInfo.getOrganizationNo(), currencyCode, result.getData().getBalance(), amount);
                    throw new BusinessException(CustomerTipConstant.ORGANIZATION_INSUFFICIENT_ACCOUNT_BALANCE);
                }
            } else {
                log.error("调用KUN账户余额查询接口失败,响应信息:{}", result);
                throw new BusinessException(CustomerTipConstant.ORGANIZATION_ACCOUNT_NOT_FOUND);
            }
        } else {
            // 扣款币种是法币
            PayXQueryBalanceReq queryBalanceReq = this.assPayxQueryBalanceReq(currencyCode, organizationBasicInfo);
            log.info("调用PayX账户余额查询接口开始,请求参数:{}", JSON.toJSONString(queryBalanceReq));
            Result<PayXQueryBalanceRsp> result = kCardPayXAccountFacade.payxQueryBalnace(queryBalanceReq);
            log.info("调用PayX账户余额查询接口结束,响应参数:{}", JSON.toJSONString(result));
            if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && result.getData() != null && result.getData().getBalance() != null) {
                // 有正确响应结果,校验余额是否充足
                if (result.getData().getBalance().compareTo(amount) >= 0) {
                    log.info("机构账户余额充足,机构号:{},币种:{},余额:{},待扣款金额:{}",
                            organizationBasicInfo.getOrganizationNo(), currencyCode, result.getData().getBalance(), amount);
                } else {
                    log.error("机构账户余额不足,机构号:{},币种:{},余额:{},待扣款金额:{}",
                            organizationBasicInfo.getOrganizationNo(), currencyCode, result.getData().getBalance(), amount);
                    throw new BusinessException(CustomerTipConstant.ORGANIZATION_INSUFFICIENT_ACCOUNT_BALANCE);
                }
            } else {
                log.error("调用PayX账户余额查询接口失败,响应信息:{}", result);
                throw new BusinessException(CustomerTipConstant.ORGANIZATION_ACCOUNT_NOT_FOUND);
            }
        }
    }

    /**
     * 发送机构费用扣除事件到mq中
     *
     * @param organizationFeeDetail
     * @param mpcToken
     * @param mpcGroupCode
     */
    public void sendOrganizationFeeDeductionToMq(OrganizationFeeDetail organizationFeeDetail, String mpcToken, String mpcGroupCode) {
        log.info("发送机构费用扣除事件到mq中");
        OrganizationFeeDeductionEventVO organizationFeeDeductionEventVO = new OrganizationFeeDeductionEventVO();
        organizationFeeDeductionEventVO.setFeeDetailId(organizationFeeDetail.getId());
        organizationFeeDeductionEventVO.setTransactionDatetime(organizationFeeDetail.getTransactionDatetime());
        organizationFeeDeductionEventVO.setMpcToken(mpcToken);
        organizationFeeDeductionEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC, organizationFeeDeductionEventVO, 10000, MqTopicConstant.DELAY_LEVEL_10S);
    }
}
