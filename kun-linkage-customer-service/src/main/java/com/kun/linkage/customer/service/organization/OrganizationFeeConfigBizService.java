package com.kun.linkage.customer.service.organization;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.boss.support.vo.VccBossUserVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.enums.OperationTypeEnum;
import com.kun.linkage.common.base.enums.ReviewStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.*;
import com.kun.linkage.customer.ext.mapper.OrganizationFeeConfigExtMapper;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.fee.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构费率配置服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class OrganizationFeeConfigBizService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationFeeConfigBizService.class);
    @Resource
    private OrganizationFeeConfigMapper organizationFeeConfigMapper;
    @Resource
    private OrganizationFeeConfigExtMapper organizationFeeConfigExtMapper;
    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;
    @Resource
    private OrganizationFeeConfigReviewRecordMapper organizationFeeConfigReviewRecordMapper;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private OrganizationFeeTemplateMapper organizationFeeTemplateMapper;

    /**
     * 分页查询机构费率配置列表
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationFeeConfigVO> pageList(OrganizationFeeConfigPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationFeeConfigExtMapper.listOrganizationFeeConfigByWhere(dto));
    }

    /**
     * 查询机构费率配置明细信息
     *
     * @param id
     * @return
     */
    public Result<OrganizationFeeConfigAndDetailVO> detail(Long id) {
        OrganizationFeeConfigAndDetailVO organizationFeeConfigAndDetailVO = organizationFeeConfigExtMapper.getOrganizationFeeConfigById(id);
        if (organizationFeeConfigAndDetailVO == null) {
            log.error("机构费率配置不存在, id:{}", id);
            return Result.fail(CustomerTipConstant.ORGANIZATION_FEE_CONFIG_NOT_FOUND);
        }
        List<OrganizationFeeTemplateDetailVO> organizationFeeConfigDetailVOList = new ArrayList<>();
        List<OrganizationFeeTemplateDetail> organizationFeeTemplateDetailList =
                organizationFeeTemplateDetailMapper.selectList(Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                        .eq(OrganizationFeeTemplateDetail::getTemplateNo, organizationFeeConfigAndDetailVO.getTemplateNo()));
        if (organizationFeeTemplateDetailList != null && !organizationFeeTemplateDetailList.isEmpty()) {
            // 进行分组
            Function<OrganizationFeeTemplateDetail, List<String>> compositeKey = item ->
                    Arrays.asList(item.getFeeType(), item.getBillingDimension(), item.getCollectionMethod(), item.getCurrencyCode());
            Map<List<String>, List<OrganizationFeeTemplateDetail>> collect =
                    organizationFeeTemplateDetailList.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));
            collect.forEach((item, organizationFeeTemplateDetails) -> {
                OrganizationFeeTemplateDetailVO organizationFeeTemplateDetailVO = new OrganizationFeeTemplateDetailVO();
                organizationFeeTemplateDetailVO.setFeeType(item.get(0));
                organizationFeeTemplateDetailVO.setBillingDimension(item.get(1));
                organizationFeeTemplateDetailVO.setCollectionMethod(item.get(2));
                organizationFeeTemplateDetailVO.setCurrencyCode(item.get(3));
                List<OrganizationFeeTemplateDetailValueVO> organizationFeeTemplateDetailValueList =
                        BeanUtil.copyToList(organizationFeeTemplateDetails, OrganizationFeeTemplateDetailValueVO.class);
                organizationFeeTemplateDetailVO.setOrganizationFeeTemplateDetailValueList(organizationFeeTemplateDetailValueList);
                organizationFeeConfigDetailVOList.add(organizationFeeTemplateDetailVO);
            });
            // 按照费率类型进行排序
            organizationFeeConfigDetailVOList.sort((o1, o2) -> {
                int value1 = Integer.parseInt(o1.getFeeType());
                int value2 = Integer.parseInt(o2.getFeeType());
                return Integer.compare(value1, value2);
            });
        }
        organizationFeeConfigAndDetailVO.setOrganizationFeeConfigDetailList(organizationFeeConfigDetailVOList);
        return Result.success(organizationFeeConfigAndDetailVO);
    }

    /**
     * 提交新增机构费率配置
     *
     * @param dto
     * @return
     */
    public Result<Void> addSubmit(OrganizationFeeConfigAddSubmitDTO dto, VccBossUserVO bossOperator) {
        // 校验机构是否存在
        Long num = organizationBasicInfoMapper.selectCount(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                .eq(OrganizationBasicInfo::getOrganizationNo, dto.getOrganizationNo()));
        if (num == null || num <= 0) {
            log.error("机构信息不存在, 机构号:{}", dto.getOrganizationNo());
            return Result.fail(CustomerTipConstant.ORGANIZATION_NOT_FOUND);
        }
        // 校验费率模版是否存在
        num = organizationFeeTemplateMapper.selectCount(Wrappers.<OrganizationFeeTemplate>lambdaQuery()
                .eq(OrganizationFeeTemplate::getTemplateNo, dto.getTemplateNo()));
        if (num == null || num <= 0) {
            log.error("机构费率模版不存在, 模版号:{}", dto.getTemplateNo());
            return Result.fail(CustomerTipConstant.FEE_TEMPLATE_NOT_FOUND);
        }
        // 机构+卡产品编号+有效开始日期+有效结束日期校验数据是否存在(有效时间区间不能重叠)
        num = organizationFeeConfigMapper.selectCount(Wrappers.<OrganizationFeeConfig>lambdaQuery()
                .eq(OrganizationFeeConfig::getOrganizationNo, dto.getOrganizationNo())
                .eq(OrganizationFeeConfig::getCardProductCode, dto.getCardProductCode())
                .lt(OrganizationFeeConfig::getEffectiveStartTime, dto.getEffectiveEndTime())
                .gt(OrganizationFeeConfig::getEffectiveEndTime, dto.getEffectiveStartTime()));
        if (num != null && num > 0) {
            log.error("存在重叠的时间区间, 机构号:{}, 卡产品编号:{}, 有效期开始时间:{}, 有效期结束时间:{}",
                    dto.getOrganizationNo(), dto.getCardProductCode(), dto.getEffectiveStartTime(), dto.getEffectiveEndTime());
            return Result.fail(CustomerTipConstant.TIME_INTERVALS_CANNOT_OVERLAP);
        }
        // 校验机构号+卡产品编号校验是否存在待审核数据
        num = organizationFeeConfigReviewRecordMapper.selectCount(Wrappers.<OrganizationFeeConfigReviewRecord>lambdaQuery()
                .eq(OrganizationFeeConfigReviewRecord::getOrganizationNo, dto.getOrganizationNo())
                .eq(OrganizationFeeConfigReviewRecord::getCardProductCode, dto.getCardProductCode())
                .eq(OrganizationFeeConfigReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (num != null && num > 0) {
            log.error("已存在待审核的数据, 机构号:{}, 卡产品编号:{}", dto.getOrganizationNo(), dto.getCardProductCode());
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeConfigReviewRecord organizationFeeConfigReviewRecord = new OrganizationFeeConfigReviewRecord();
        BeanUtil.copyProperties(dto, organizationFeeConfigReviewRecord);
        organizationFeeConfigReviewRecord.setOperatorType(OperationTypeEnum.ADD.getValue());
        organizationFeeConfigReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationFeeConfigReviewRecord.setSubmitTime(now);
        organizationFeeConfigReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationFeeConfigReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationFeeConfigReviewRecordMapper.insert(organizationFeeConfigReviewRecord);
        return Result.success();
    }

    /**
     * 提交修改机构费率配置
     *
     * @param dto
     * @return
     */
    public Result<Void> modifySubmit(OrganizationFeeConfigModifySubmitDTO dto, VccBossUserVO bossOperator) {
        // 校验待修改的数据是否存在
        OrganizationFeeConfig organizationFeeConfig = organizationFeeConfigMapper.selectById(dto.getId());
        if (organizationFeeConfig == null) {
            log.error("机构费率配置不存在, 机构费率配置id:{}", dto.getId());
            return Result.fail(CustomerTipConstant.ORGANIZATION_FEE_CONFIG_NOT_FOUND);
        }
        // 机构+卡产品编号+有效开始日期+有效结束日期校验修改后的数据是否存在(有效时间区间不能重叠,排除自身)
        Long num = organizationFeeConfigMapper.selectCount(Wrappers.<OrganizationFeeConfig>lambdaQuery()
                .eq(OrganizationFeeConfig::getOrganizationNo, organizationFeeConfig.getOrganizationNo())
                .eq(OrganizationFeeConfig::getCardProductCode, organizationFeeConfig.getCardProductCode())
                .lt(OrganizationFeeConfig::getEffectiveStartTime, dto.getEffectiveEndTime())
                .gt(OrganizationFeeConfig::getEffectiveEndTime, dto.getEffectiveStartTime())
                .ne(OrganizationFeeConfig::getId, dto.getId()));
        if (num != null && num > 0) {
            log.error("存在重叠的时间区间, 机构号:{}, 卡产品编号:{}, 有效期开始时间:{}, 有效期结束时间:{}",
                    organizationFeeConfig.getOrganizationNo(), organizationFeeConfig.getCardProductCode(),
                    dto.getEffectiveStartTime(), dto.getEffectiveEndTime());
            return Result.fail(CustomerTipConstant.TIME_INTERVALS_CANNOT_OVERLAP);
        }
        // 校验机构号+卡产品编号校验是否存在待审核数据
        num = organizationFeeConfigReviewRecordMapper.selectCount(Wrappers.<OrganizationFeeConfigReviewRecord>lambdaQuery()
                .eq(OrganizationFeeConfigReviewRecord::getOrganizationNo, organizationFeeConfig.getOrganizationNo())
                .eq(OrganizationFeeConfigReviewRecord::getCardProductCode, organizationFeeConfig.getCardProductCode())
                .eq(OrganizationFeeConfigReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (num != null && num > 0) {
            log.error("已存在待审核的数据, 机构号:{}, 卡产品编号:{}", organizationFeeConfig.getOrganizationNo(), organizationFeeConfig.getCardProductCode());
            return Result.fail(CommonTipConstant.ALREADY_EXIST_PENDING_DATA);
        }
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeConfigReviewRecord organizationFeeConfigReviewRecord = new OrganizationFeeConfigReviewRecord();
        BeanUtil.copyProperties(dto, organizationFeeConfigReviewRecord);
        organizationFeeConfigReviewRecord.setFeeConfigId(organizationFeeConfig.getId());
        organizationFeeConfigReviewRecord.setOrganizationNo(organizationFeeConfig.getOrganizationNo());
        organizationFeeConfigReviewRecord.setCardProductCode(organizationFeeConfig.getCardProductCode());
        organizationFeeConfigReviewRecord.setOperatorType(OperationTypeEnum.MODIFY.getValue());
        organizationFeeConfigReviewRecord.setReviewStatus(ReviewStatusEnum.PENDING.getValue());
        organizationFeeConfigReviewRecord.setSubmitTime(now);
        organizationFeeConfigReviewRecord.setSubmitUserId(String.valueOf(bossOperator.getId()));
        organizationFeeConfigReviewRecord.setSubmitUserName(bossOperator.getUsername());
        // 新增到审核记录中,审核通过才会到正式表
        organizationFeeConfigReviewRecordMapper.insert(organizationFeeConfigReviewRecord);
        return Result.success();
    }

    /**
     * 审核机构费率配置
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> review(ReviewDTO dto, VccBossUserVO bossOperator) {
        OrganizationFeeConfigReviewRecord organizationFeeConfigReviewRecord =
                organizationFeeConfigReviewRecordMapper.selectOne(
                        Wrappers.<OrganizationFeeConfigReviewRecord>lambdaQuery()
                                .eq(OrganizationFeeConfigReviewRecord::getReviewId, dto.getReviewId())
                                .eq(OrganizationFeeConfigReviewRecord::getReviewStatus, ReviewStatusEnum.PENDING.getValue()));
        if (organizationFeeConfigReviewRecord == null) {
            log.error("机构费率配置待审核记录不存在, 审核记录Id:{}", dto.getReviewId());
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        LocalDateTime now = LocalDateTime.now().withNano(0);
        OrganizationFeeConfigReviewRecord modifyRecord = new OrganizationFeeConfigReviewRecord();
        modifyRecord.setReviewId(dto.getReviewId());
        modifyRecord.setReviewStatus(dto.getReviewStatus());
        if (StringUtils.isNotBlank(dto.getReviewReason())) {
            modifyRecord.setReviewReason(dto.getReviewReason());
        }
        modifyRecord.setReviewTime(now);
        modifyRecord.setReviewUserId(String.valueOf(bossOperator.getId()));
        modifyRecord.setReviewUserName(bossOperator.getUsername());
        if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.REJECT.getValue())) {
            organizationFeeConfigReviewRecordMapper.updateById(modifyRecord);
        } else if (StringUtils.equals(dto.getReviewStatus(), ReviewStatusEnum.PASS.getValue())) {
            if (StringUtils.equals(OperationTypeEnum.ADD.getValue(), organizationFeeConfigReviewRecord.getOperatorType())) {
                // 机构费率配置
                OrganizationFeeConfig organizationFeeConfig = new OrganizationFeeConfig();
                BeanUtil.copyProperties(organizationFeeConfigReviewRecord, organizationFeeConfig);
                organizationFeeConfig.setCreateTime(now);
                organizationFeeConfig.setCreateUserId(String.valueOf(bossOperator.getId()));
                organizationFeeConfig.setCreateUserName(bossOperator.getUsername());
                organizationFeeConfig.setLastModifyTime(now);
                organizationFeeConfig.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationFeeConfig.setLastModifyUserName(bossOperator.getUsername());
                organizationFeeConfigMapper.insert(organizationFeeConfig);
            } else {
                // 修改
                OrganizationFeeConfig organizationFeeConfig = new OrganizationFeeConfig();
                BeanUtil.copyProperties(organizationFeeConfigReviewRecord, organizationFeeConfig);
                organizationFeeConfig.setId(organizationFeeConfigReviewRecord.getFeeConfigId());
                organizationFeeConfig.setLastModifyTime(now);
                organizationFeeConfig.setLastModifyUserId(String.valueOf(bossOperator.getId()));
                organizationFeeConfig.setLastModifyUserName(bossOperator.getUsername());
                organizationFeeConfigMapper.updateById(organizationFeeConfig);
            }
            organizationFeeConfigReviewRecordMapper.updateById(modifyRecord);
        } else {
            log.error("非法审核状态, 审核状态:{}", dto.getReviewStatus());
            return Result.fail(CommonTipConstant.ILLEGAL_REQUEST);
        }
        return Result.success();
    }


    /**
     * 分页查询机构费率配置审核信息
     *
     * @param dto
     * @return
     */
    public PageResult<OrganizationFeeConfigReviewRecordVO> reviewPageList(OrganizationFeeConfigReviewRecordPageQueryDTO dto) {
        return PageHelperUtil.getPage(dto, () -> organizationFeeConfigExtMapper.listOrganizationFeeConfigReviewRecordByWhere(dto));
    }

    /**
     * 查询机构费率配置审核明细
     *
     * @param reviewId
     * @return
     */
    public Result<OrganizationFeeConfigAndDetailReviewRecordVO> reviewDetail(String reviewId) {
        OrganizationFeeConfigAndDetailReviewRecordVO organizationFeeConfigAndDetailReviewRecordVO = organizationFeeConfigExtMapper.getOrganizationFeeConfigReviewRecordByReviewId(reviewId);
        if (organizationFeeConfigAndDetailReviewRecordVO == null) {
            log.error("审核信息不存在, reviewId:{}", reviewId);
            return Result.fail(CommonTipConstant.DATA_NOT_FOUND);
        }
        List<OrganizationFeeTemplateDetailVO> organizationFeeConfigDetailVOList = new ArrayList<>();
        List<OrganizationFeeTemplateDetail> organizationFeeTemplateDetailList =
                organizationFeeTemplateDetailMapper.selectList(Wrappers.<OrganizationFeeTemplateDetail>lambdaQuery()
                        .eq(OrganizationFeeTemplateDetail::getTemplateNo, organizationFeeConfigAndDetailReviewRecordVO.getTemplateNo()));
        if (organizationFeeTemplateDetailList != null && !organizationFeeTemplateDetailList.isEmpty()) {
            // 进行分组
            Function<OrganizationFeeTemplateDetail, List<String>> compositeKey = item ->
                    Arrays.asList(item.getFeeType(), item.getBillingDimension(), item.getCollectionMethod(), item.getCurrencyCode());
            Map<List<String>, List<OrganizationFeeTemplateDetail>> collect =
                    organizationFeeTemplateDetailList.stream().collect(Collectors.groupingBy(compositeKey, Collectors.toList()));
            collect.forEach((item, organizationFeeTemplateDetails) -> {
                OrganizationFeeTemplateDetailVO organizationFeeTemplateDetailVO = new OrganizationFeeTemplateDetailVO();
                organizationFeeTemplateDetailVO.setFeeType(item.get(0));
                organizationFeeTemplateDetailVO.setBillingDimension(item.get(1));
                organizationFeeTemplateDetailVO.setCollectionMethod(item.get(2));
                organizationFeeTemplateDetailVO.setCurrencyCode(item.get(3));
                List<OrganizationFeeTemplateDetailValueVO> organizationFeeTemplateDetailValueList =
                        BeanUtil.copyToList(organizationFeeTemplateDetails, OrganizationFeeTemplateDetailValueVO.class);
                organizationFeeTemplateDetailVO.setOrganizationFeeTemplateDetailValueList(organizationFeeTemplateDetailValueList);
                organizationFeeConfigDetailVOList.add(organizationFeeTemplateDetailVO);
            });
            // 按照费率类型进行排序
            organizationFeeConfigDetailVOList.sort((o1, o2) -> {
                int value1 = Integer.parseInt(o1.getFeeType());
                int value2 = Integer.parseInt(o2.getFeeType());
                return Integer.compare(value1, value2);
            });
        }
        organizationFeeConfigAndDetailReviewRecordVO.setOrganizationFeeConfigDetailReviewRecordList(organizationFeeConfigDetailVOList);
        return Result.success(organizationFeeConfigAndDetailReviewRecordVO);
    }

    /**
     * 根据条件获取机构费率配置信息
     * @param organizationNo
     * @param cardProductCode
     */
    public OrganizationFeeConfig getValidOrganizationFeeConfigByWhere(String organizationNo, String cardProductCode) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        return organizationFeeConfigExtMapper.getOrganizationFeeConfigByWhere(organizationNo, cardProductCode, now, ValidStatusEnum.VALID.getValue());
    }
}
