package com.kun.linkage.customer.controller.org;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.api.bean.req.OrgPageQueryCardRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.OrgPageQueryCardRechargeDetailRes;
import com.kun.linkage.customer.facade.dto.OrganizationCustomerCardListQueryDTO;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerCardListQueryVO;
import com.kun.linkage.customer.service.OrgCardRechargeQueryService;
import com.kun.linkage.customer.service.OrganizationCustomerCardInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 卡管理接口
 * </p>
 */
@Tag(name = "卡管理", description = "商户端卡管理")
@RestController
@RequestMapping("/org/cardManagement")
public class OrgCardManagementController {
    @Resource
    private OrganizationCustomerCardInfoService organizationCustomerCardInfoService;
    @Resource
    private OrgCardRechargeQueryService orgCardRechargeQueryService;

    /**
     * 分页查询机构客户账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构客户卡片信息", summary = "分页查询机构客户卡片信息")
    @PostMapping("/pageList")
    public Result<PageResult<OrganizationCustomerCardListQueryVO>> pageList(@RequestBody OrganizationCustomerCardListQueryDTO dto) {
        PageResult<OrganizationCustomerCardListQueryVO> organizationCustomerCardList = organizationCustomerCardInfoService.getOrganizationCustomerCardList(dto);
        return Result.success(organizationCustomerCardList);
    }

    /**
     * 分页查询卡充值记录
     * @param pageQueryCardRechargeDetailReq
     * @return
     */
    @Operation(description = "分页查询卡充值记录", summary = "分页查询卡充值记录")
    @RequestMapping(value = "/pageQueryCardRechargeDetail", method = RequestMethod.POST)
    public Result<PageResult<OrgPageQueryCardRechargeDetailRes>> pageQueryCardRechargeDetail(
            @RequestBody @Validated OrgPageQueryCardRechargeDetailReq pageQueryCardRechargeDetailReq){
        return Result.success(orgCardRechargeQueryService.pageQueryCardRechargeDetail(pageQueryCardRechargeDetailReq));
    }
}
