package com.kun.linkage.customer.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import com.kun.linkage.common.db.entity.MpcWalletWebhookRecord;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.entity.OrganizationCustomerAccountInfo;
import com.kun.linkage.common.db.entity.OrganizationCustomerWalletInfo;
import com.kun.linkage.common.db.mapper.MpcWalletWebhookRecordMapper;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationCustomerAccountInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationCustomerWalletInfoMapper;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.enums.*;
import com.kun.linkage.customer.service.WalletManagementBizService;
import com.kun.linkage.wallet.gateway.facade.vo.req.WebhookMqReq;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * MPC钱包通知事件消费者
 */
@Component
@RocketMQMessageListener(consumerGroup = MqConsumerGroupConstant.KL_CUSTOMER_MPC_WALLET_WEBHOOK_GROUP,
        topic = MqTopicConstant.MPC_WALLET_WEBHOOK_EVENT_TOPIC, messageModel = MessageModel.CLUSTERING)
public class MpcWalletEventConsumer implements RocketMQListener<WebhookMqReq> {
    private static final Logger log = LoggerFactory.getLogger(MpcWalletEventConsumer.class);
    @Resource
    private WalletManagementBizService walletManagementBizService;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private OrganizationCustomerWalletInfoMapper organizationCustomerWalletInfoMapper;
    @Resource
    private MpcWalletWebhookRecordMapper mpcWalletWebhookRecordMapper;
    @Resource
    private OrganizationCustomerAccountInfoMapper organizationCustomerAccountInfoMapper;
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;

    @Override
    @NewSpan
    public void onMessage(WebhookMqReq webhookMqReq) {
        MDC.put("traceId", webhookMqReq.getLogContext().getTraceId());
        log.info("[MPC钱包Webhook通知事件]接收到事件请求:{}", JSON.toJSONString(webhookMqReq));
        RLock lock = null;
        boolean retryFlag = false;
        MpcWalletWebhookRecord mpcWalletWebhookRecord = null;
        try {
            // 使用租户号+请求id作为key
            String lockKey = CustomerLockConstant.MPC_WALLET_WEBHOOK_LOCK_PREFIX + webhookMqReq.getTenantId() + webhookMqReq.getRequestId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.warn("[MPC钱包Webhook通知事件]获取锁失败,lockKey:{}", lockKey);
                retryFlag = true;
            } else {
                if (StringUtils.isBlank(webhookMqReq.getTenantId())) {
                    log.error("[MPC钱包Webhook通知事件]请求参数异常,请检查,请求参数:{}", JSON.toJSONString(webhookMqReq));
                    return;
                }
                // 先根据租户id获取对应的机构信息,后面可以根据机构号来获取到对应的表
                OrganizationBasicInfo organizationBasicInfo = organizationBasicInfoMapper.selectOne(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                        .eq(OrganizationBasicInfo::getMpcTenantId, webhookMqReq.getTenantId()));
                if (organizationBasicInfo == null) {
                    log.error("[MPC钱包Webhook通知事件]根据租户id获取不到对应机构信息,请检查,租户id:{}", webhookMqReq.getTenantId());
                    return;
                }
                mpcWalletWebhookRecord = mpcWalletWebhookRecordMapper.selectOne(
                        Wrappers.<MpcWalletWebhookRecord>lambdaQuery()
                                .eq(MpcWalletWebhookRecord::getOrganizationNo, organizationBasicInfo.getOrganizationNo())
                                .eq(MpcWalletWebhookRecord::getTenantId, webhookMqReq.getTenantId())
                                .eq(MpcWalletWebhookRecord::getRequestNo, webhookMqReq.getRequestId()));
                if (mpcWalletWebhookRecord == null) {
                    mpcWalletWebhookRecord = new MpcWalletWebhookRecord();
                    mpcWalletWebhookRecord.setOrganizationBookkeepStatus(YesFlagEnum.NO.getNumValue());
                    mpcWalletWebhookRecord.setCustomerBookkeepStatus(YesFlagEnum.NO.getNumValue());
                } else if (StringUtils.equals(mpcWalletWebhookRecord.getProcessingStatus(), MpcWalletWebhookProcessingStatusEnum.SUCCESS.getValue())
                        || StringUtils.equals(mpcWalletWebhookRecord.getProcessingStatus(), MpcWalletWebhookProcessingStatusEnum.BLOCK.getValue())) {
                    log.warn("[MPC钱包Webhook通知事件]通知事件已处理,无需重复处理,机构号:{},租户id:{},请求流水号:{}",
                            organizationBasicInfo.getOrganizationNo(), webhookMqReq.getTenantId(), webhookMqReq.getRequestId());
                    return;
                }
                mpcWalletWebhookRecord.setOrganizationNo(organizationBasicInfo.getOrganizationNo());
                this.assMpcWalletWebhookRecord(webhookMqReq, mpcWalletWebhookRecord);
                if (!this.checkParams(webhookMqReq)) {
                    log.error("[MPC钱包Webhook通知事件]请求参数校验失败,请检查,请求参数参数:{}", JSON.toJSONString(webhookMqReq));
                    // 请求参数有误
                    mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.FAIL.getValue());
                    mpcWalletWebhookRecord.setFailMessage("请求参数有误,请检查");
                } else {
                    // 校验用户钱包地址信息是否存在
                    OrganizationCustomerWalletInfo organizationCustomerWalletInfo = organizationCustomerWalletInfoMapper.selectOne(
                            Wrappers.<OrganizationCustomerWalletInfo>lambdaQuery()
                                    .eq(OrganizationCustomerWalletInfo::getOrganizationNo, organizationBasicInfo.getOrganizationNo())
                                    .eq(OrganizationCustomerWalletInfo::getWalletNetwork, WalletNetworkEnum.KUN_MPC.getValue())
                                    .eq(OrganizationCustomerWalletInfo::getChainNetwork, webhookMqReq.getNetwork())
                                    .eq(OrganizationCustomerWalletInfo::getWalletAddress, webhookMqReq.getAddressTo())
                                    .eq(OrganizationCustomerWalletInfo::getStatus, ValidStatusEnum.VALID.getValue()));
                    if (organizationCustomerWalletInfo == null) {
                        log.error("[MPC钱包Webhook通知事件]不存在有效的钱包地址信息,请检查,机构号:{}, 钱包通道:{}, 链网络:{}, 钱包地址:{}",
                                organizationBasicInfo.getOrganizationNo(), WalletNetworkEnum.KUN_MPC.getValue(), webhookMqReq.getNetwork(), webhookMqReq.getAddressTo());
                        mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.FAIL.getValue());
                        mpcWalletWebhookRecord.setFailMessage("未找到有效的用户钱包地址信息,请检查");
                        return;
                    }
                    // 获取用户账户信息,获取美金账户
                    OrganizationCustomerAccountInfo organizationCustomerAccountInfo = organizationCustomerAccountInfoMapper.selectOne(
                            Wrappers.<OrganizationCustomerAccountInfo>lambdaQuery()
                                    .eq(OrganizationCustomerAccountInfo::getOrganizationNo, organizationBasicInfo.getOrganizationNo())
                                    .eq(OrganizationCustomerAccountInfo::getCustomerId, organizationCustomerWalletInfo.getCustomerId())
                                    .eq(OrganizationCustomerAccountInfo::getCurrencyCode, FiatCurrencyEnum.USD.getCurrencyCode())
                                    .eq(OrganizationCustomerAccountInfo::getAccountType, BusinessAccountTypeEnum.BASIC.getValue())
                                    .eq(OrganizationCustomerAccountInfo::getStatus, ValidStatusEnum.VALID.getValue()));
                    if (organizationCustomerAccountInfo == null || StringUtils.isBlank(organizationCustomerAccountInfo.getAccountNo())) {
                        log.error("[MPC钱包Webhook通知事件]不存在有效的美金用户法币账户信息,机构号:{},客户号:{}",
                                mpcWalletWebhookRecord.getOrganizationNo(), organizationCustomerWalletInfo.getCustomerId());
                        mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.FAIL.getValue());
                        mpcWalletWebhookRecord.setFailMessage("未找到有效的美金用户法币账户信息,请检查");
                        return;
                    }
                    mpcWalletWebhookRecord.setCustomerId(organizationCustomerWalletInfo.getCustomerId());
                    if (StringUtils.equals(webhookMqReq.getStatus(), MpcWalletWebhookStatusEnum.SUCCESS.getValue())) {
                        // 钱包方状态为成功
                        if (StringUtils.equals(webhookMqReq.getScene(), MpcWalletWebhookSceneEnum.DEPOSIT.getValue())) {
                            // 充值场景
                            if (StringUtils.equals(webhookMqReq.getRiskSuggest(), MpcWalletWebhookRiskSuggestEnum.PASS.getValue())) {
                                // 风控检验通过
                                walletManagementBizService.mpcWalletRechargeRiskPassProcess(mpcWalletWebhookRecord,
                                        organizationCustomerWalletInfo, organizationBasicInfo, organizationCustomerAccountInfo);
                                mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.SUCCESS.getValue());
                                mpcWalletWebhookRecord.setFailMessage("充值成功");
                            } else {
                                // 风控校验异常
                                walletManagementBizService.mpcWalletRechargeRiskExceptionProcess(mpcWalletWebhookRecord, organizationCustomerWalletInfo);
                                mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.BLOCK.getValue());
                                mpcWalletWebhookRecord.setFailMessage("风控检验未通过");
                            }
                        } else {
                            // 其余场景一律先按失败处理
                            log.error("[MPC钱包Webhook通知事件]不支持的场景,scene:{}", webhookMqReq.getScene());
                            mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.FAIL.getValue());
                            mpcWalletWebhookRecord.setFailMessage("不支持的业务场景");
                        }
                    } else {
                        mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.FAIL.getValue());
                        mpcWalletWebhookRecord.setFailMessage("钱包方状态不是成功");
                    }
                }
            }
        } catch (Exception e) {
            log.error("[MPC钱包Webhook通知事件]处理异常,异常信息:", e);
            if (mpcWalletWebhookRecord != null) {
                mpcWalletWebhookRecord.setProcessingStatus(MpcWalletWebhookProcessingStatusEnum.FAIL.getValue());
                mpcWalletWebhookRecord.setFailMessage("处理异常");
            }
            retryFlag = true;
        } finally {
            if (mpcWalletWebhookRecord != null) {
                LocalDateTime now = LocalDateTime.now();
                mpcWalletWebhookRecord.setLastModifyTime(now);
                if (mpcWalletWebhookRecord.getId() != null) {
                    int row = mpcWalletWebhookRecordMapper.update(mpcWalletWebhookRecord,
                            Wrappers.<MpcWalletWebhookRecord>lambdaQuery()
                                    .eq(MpcWalletWebhookRecord::getId, mpcWalletWebhookRecord.getId())
                                    .eq(MpcWalletWebhookRecord::getOrganizationNo, mpcWalletWebhookRecord.getOrganizationNo()));
                    log.info("update {} row MpcWalletWebhookRecord", row);
                } else {
                    mpcWalletWebhookRecord.setCreateTime(now);
                    int row = mpcWalletWebhookRecordMapper.insert(mpcWalletWebhookRecord);
                    log.info("insert {} row MpcWalletWebhookRecord", row);
                }
            }
            redissonLockUtil.unlock(lock);
            if (retryFlag && (mpcWalletWebhookRecord == null || mpcWalletWebhookRecord.getRetryCount() < 5)) {
                log.warn("[MPC钱包Webhook通知事件]事件处理失败,30秒后进行重试");
                rocketMqService.delayedSend(MqTopicConstant.MPC_WALLET_WEBHOOK_EVENT_TOPIC, webhookMqReq, 10000, 4);
            }
            // 发送告警
            if (retryFlag && (mpcWalletWebhookRecord != null && mpcWalletWebhookRecord.getRetryCount() >= 5)) {
                log.warn("[MPC钱包Webhook通知事件]重试后依旧失败,发送LARK告警");
                this.sendLarkAlarm(mpcWalletWebhookRecord);
            }
        }
    }

    /**
     * 组装通知记录对象
     *
     * @param webhookMqReq
     * @param mpcWalletWebhookRecord
     */
    private void assMpcWalletWebhookRecord(WebhookMqReq webhookMqReq, MpcWalletWebhookRecord mpcWalletWebhookRecord) {
        mpcWalletWebhookRecord.setRequestNo(webhookMqReq.getRequestId());
        mpcWalletWebhookRecord.setTenantId(webhookMqReq.getTenantId());
        mpcWalletWebhookRecord.setAmount(new BigDecimal(StringUtils.isBlank(webhookMqReq.getAmount()) ? "0" : webhookMqReq.getAmount()));
        mpcWalletWebhookRecord.setCurrencyCode(webhookMqReq.getCurrency());
        mpcWalletWebhookRecord.setAddressFrom(webhookMqReq.getAddressFrom());
        mpcWalletWebhookRecord.setAddressTo(webhookMqReq.getAddressTo());
        mpcWalletWebhookRecord.setChainNetwork(webhookMqReq.getNetwork());
        mpcWalletWebhookRecord.setTxHash(webhookMqReq.getTxHash());
        mpcWalletWebhookRecord.setBlockNumber(webhookMqReq.getBlockNumber());
        mpcWalletWebhookRecord.setStatus(webhookMqReq.getStatus());
        mpcWalletWebhookRecord.setScene(webhookMqReq.getScene());
        mpcWalletWebhookRecord.setGasFee(new BigDecimal(StringUtils.isBlank(webhookMqReq.getGasFee()) ? "0" : webhookMqReq.getGasFee()));
        mpcWalletWebhookRecord.setGasCurrency(webhookMqReq.getGasCurrency());
        mpcWalletWebhookRecord.setRiskScore(webhookMqReq.getRiskScore());
        mpcWalletWebhookRecord.setRiskSuggest(webhookMqReq.getRiskSuggest());
        mpcWalletWebhookRecord.setRetryCount(mpcWalletWebhookRecord.getRetryCount() == null ? 0 : mpcWalletWebhookRecord.getRetryCount() + 1);
    }

    /**
     * 校验请求参数
     *
     * @param webhookMqReq
     * @return
     */
    private boolean checkParams(WebhookMqReq webhookMqReq) {
        return !StringUtils.isBlank(webhookMqReq.getRequestId())
                && !StringUtils.isBlank(webhookMqReq.getAddressFrom())
                && !StringUtils.isBlank(webhookMqReq.getAddressTo())
                && !StringUtils.isBlank(webhookMqReq.getAmount())
                && DigitalCurrencyEnum.contains(webhookMqReq.getCurrency())
                && MpcWalletWebhookSceneEnum.contains(webhookMqReq.getScene())
                && !StringUtils.isBlank(webhookMqReq.getNetwork())
                && MpcWalletWebhookStatusEnum.contains(webhookMqReq.getStatus())
                && MpcWalletWebhookRiskSuggestEnum.contains(webhookMqReq.getRiskSuggest());
    }

    /**
     * 发送LARK告警
     * @param mpcWalletWebhookRecord
     */
    private void sendLarkAlarm(MpcWalletWebhookRecord mpcWalletWebhookRecord) {
        String msg = String.format("[MPC钱包充值异常]租户ID:%s, 钱包地址:%s, 充值金额:%s, 充值币种:%s, 请求流水号:%s",
                mpcWalletWebhookRecord.getOrganizationNo(), mpcWalletWebhookRecord.getAddressTo(), mpcWalletWebhookRecord.getAmount(),
                mpcWalletWebhookRecord.getCurrencyCode(), mpcWalletWebhookRecord.getRequestNo());
        larkAlarmUtil.sendTextAlarm(msg);
    }
}
