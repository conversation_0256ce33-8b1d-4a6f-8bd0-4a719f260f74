package com.kun.linkage.customer.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXAskPriceRsp;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 汇率换算服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service
public class ExchangeRateBizService {
    private static final Logger log = LoggerFactory.getLogger(ExchangeRateBizService.class);
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;

    /**
     * 获取币种换算汇率
     *
     * @param sourceCurrencyCode
     * @param targetCurrencyCode
     * @param organizationBasicInfo
     * @return
     */
    public BigDecimal getCurrencyExchangeRate(String sourceCurrencyCode, String targetCurrencyCode, BigDecimal amount, OrganizationBasicInfo organizationBasicInfo) {
        log.info("开始获取币种换算汇率");
        // 原币种和目标币种不一致才需要换汇
        BigDecimal fxRate = BigDecimal.ONE;
        if (StringUtils.equals(sourceCurrencyCode, targetCurrencyCode)
                || (StringUtils.equals(sourceCurrencyCode, DigitalCurrencyEnum.USDT.getValue()) && StringUtils.equals(targetCurrencyCode, FiatCurrencyEnum.USD.getCurrencyCode()))
                || (StringUtils.equals(sourceCurrencyCode, FiatCurrencyEnum.USD.getCurrencyCode()) && StringUtils.equals(targetCurrencyCode, DigitalCurrencyEnum.USDT.getValue()))) {
            // 币种一致或原币种和目标币种一个为USD一个为USDT,汇率直接当做1
            log.info("币种一致或原币种和目标币种一个为USD一个为USDT,汇率直接当做1,原币种:{}, 目标币种:{}", sourceCurrencyCode, targetCurrencyCode);
        } else {
            if (DigitalCurrencyEnum.contains(sourceCurrencyCode) && DigitalCurrencyEnum.contains(targetCurrencyCode)) {
                log.info("暂不支持原币种和目标币种都为数币, 原币种:{}, 目标币种:{}", sourceCurrencyCode, targetCurrencyCode);
                throw new BusinessException(CommonTipConstant.ILLEGAL_REQUEST);
            } else if (DigitalCurrencyEnum.contains(sourceCurrencyCode)) {
                log.info("原币种为数币,目标币种为法币,开始调用KUN查询汇率信息");
                KunAskPriceReq kunAskPriceReq = this.assKunAskPriceReq(KunSideTypeEnum.SELL.getType(), sourceCurrencyCode, targetCurrencyCode, amount, organizationBasicInfo);
                log.info("调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
                Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
                log.info("调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
                // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                        && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData().getPrice() != null) {
                    fxRate = kunAskPriceRspResult.getData().getPrice();
                } else {
                    log.error("调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else if (DigitalCurrencyEnum.contains(targetCurrencyCode)) {
                log.info("原币种为法币,目标币种为数币,开始调用KUN查询汇率信息");
                // 注意注意!!!!  kun的接口币对必须是数币在前、法币在后
                KunAskPriceReq kunAskPriceReq = this.assKunAskPriceReq(KunSideTypeEnum.BUY.getType(), targetCurrencyCode, sourceCurrencyCode, amount, organizationBasicInfo);
                log.info("调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
                Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
                log.info("调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
                // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                        && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData().getPrice() != null) {
                    /*// 此处应使用kun接口返回汇率的导数
                    fxRate = BigDecimal.ONE.divide(kunAskPriceRspResult.getData().getPrice(), 5, RoundingMode.DOWN);*/
                    // 现在他们的接口又改成返回的是正确的汇率了,先暂时改回,后面等他确认后再改
                    fxRate = kunAskPriceRspResult.getData().getPrice();
                } else {
                    log.error("调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            } else {
                log.info("原币种为法币,目标币种为法币,开始调用PayX查询汇率信息");
                PayXAskPriceReq payXAskPriceReq = this.assPayxAskPriceReq(sourceCurrencyCode, targetCurrencyCode, organizationBasicInfo);
                log.info("调用PayX汇率查询接口开始,请求参数:{}", JSON.toJSONString(payXAskPriceReq));
                Result<PayXAskPriceRsp> payXAskPriceRspResult = kCardPayXAccountFacade.payXExchangeRate(payXAskPriceReq);
                log.info("调用PayX汇率查询接口结束,响应参数:{}", JSON.toJSONString(payXAskPriceRspResult));
                // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                if (payXAskPriceRspResult != null && StringUtils.equals(payXAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                        && payXAskPriceRspResult.getData() != null && payXAskPriceRspResult.getData().getExchangeRate() != null) {
                    fxRate = payXAskPriceRspResult.getData().getExchangeRate();
                } else {
                    log.error("调用PayX汇率查询接口失败,响应信息:{}", fxRate);
                    throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                }
            }
        }
        log.info("获取币种换算汇率完成,原币种:{},目标币种:{},汇率:{}", sourceCurrencyCode, targetCurrencyCode, fxRate);
        return fxRate;
    }

    /**
     * 组装kun汇率查询接口请求参数
     *
     * @param sideType
     * @param sourceCurrency
     * @param targetCurrency
     * @param amt
     * @param organizationBasicInfo
     * @return
     */
    private KunAskPriceReq assKunAskPriceReq(String sideType, String sourceCurrency, String targetCurrency, BigDecimal amt, OrganizationBasicInfo organizationBasicInfo) {
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setPayAmount(amt);
        kunAskPriceReq.setSideType(sideType);
        kunAskPriceReq.setSymbol(sourceCurrency + "_" + targetCurrency);
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        return kunAskPriceReq;
    }

    /**
     * 组装payx汇率查询接口请求参数
     *
     * @param sourceCurrency
     * @param targetCurrency
     * @param organizationBasicInfo
     * @return
     */
    private PayXAskPriceReq assPayxAskPriceReq(String sourceCurrency, String targetCurrency, OrganizationBasicInfo organizationBasicInfo) {
        PayXAskPriceReq payXAskPriceReq = new PayXAskPriceReq();
        payXAskPriceReq.setSourceCurrency(sourceCurrency);
        payXAskPriceReq.setTargetCurrency(targetCurrency);
        payXAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        payXAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        return payXAskPriceReq;
    }
}
