package com.kun.linkage.customer.task;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.log.trace.LogContext;
import com.kun.linkage.base.facade.enums.ReportTypeEnum;
import com.kun.linkage.customer.service.report.OrganizationFeeMonthlyReportBizService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 机构手续费月报表生成任务
 */
@Slf4j
@Component
public class OrganizationFeeMonthlyReportTask {
    @Resource
    private OrganizationFeeMonthlyReportBizService organizationFeeMonthlyReportBizService;

    @NewSpan
    @XxlJob("organizationFeeMonthlyReportTask")
    public boolean organizationFeeMonthlyReportTask() {
        XxlJobHelper.log("[机构手续费月报表生成任务]开始执行");
        String yearMonth = null;
        try {
            LogContext.fromContext(LogContext.getContext());
            String jobParam = XxlJobHelper.getJobParam();
            if (StringUtils.isNotBlank(jobParam)) {
                XxlJobHelper.log("[机构手续费月报表生成任务]生成的月份为XXL-JOB中传入的月份");
                yearMonth = JSON.parseObject(jobParam).getString("yearMonth");
            } else {
                XxlJobHelper.log("[机构手续费月报表生成任务]生成的月份为上个月");
                yearMonth = YearMonth.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyyMM"));
            }
            XxlJobHelper.log("[机构手续费月报表生成任务]生成的月份:{}", yearMonth);
            boolean result = organizationFeeMonthlyReportBizService.generateReport(ReportTypeEnum.ORGANIZATION_FEE_MONTHLY_REPORT, yearMonth);
            if (result) {
                return XxlJobHelper.handleSuccess();
            } else {
                return XxlJobHelper.handleFail();
            }
        } catch (Exception e) {
            XxlJobHelper.log(e.getMessage());
            XxlJobHelper.log(e);
            return XxlJobHelper.handleFail(e.getMessage());
        } finally {
            XxlJobHelper.log("[机构手续费月报表生成任务]执行结束", LogContext.getContext().getTraceId());
            LogContext.destroy();
        }
    }
}
