package com.kun.linkage.common.base.constants;

public class MqTopicConstant {

    /**
     * 延时级别常量 - 1级 - 延迟1s
     */
    public static final int DELAY_LEVEL_1S = 1;

    /**
     * 延时级别常量 - 2级 - 延迟5s
     */
    public static final int DELAY_LEVEL_5S = 2;

    /**
     * 延时级别常量 - 3级 - 延迟10s
     */
    public static final int DELAY_LEVEL_10S = 3;

    /**
     * 延时级别常量 - 4级 - 延迟30s
     */
    public static final int DELAY_LEVEL_30S = 4;

    /**
     * 延时级别常量 - 5级 - 延迟1分钟
     */
    public static final int DELAY_LEVEL_1M = 5;

    /**
     * 延时级别常量 - 6级 - 延迟2分钟
     */
    public static final int DELAY_LEVEL_2M = 6;

    /**
     * 延时级别常量 - 7级 - 延迟3分钟
     */
    public static final int DELAY_LEVEL_3M = 7;

    /**
     * 延时级别常量 - 8级 - 延迟4分钟
     */
    public static final int DELAY_LEVEL_4M = 8;

    /**
     * 延时级别常量 - 9级 - 延迟5分钟
     */
    public static final int DELAY_LEVEL_5M = 9;

    /**
     * 延时级别常量 - 10级 - 延迟6分钟
     */
    public static final int DELAY_LEVEL_6M = 10;

    /**
     * 延时级别常量 - 11级 - 延迟7分钟
     */
    public static final int DELAY_LEVEL_7M = 11;

    /**
     * 延时级别常量 - 12级 - 延迟8分钟
     */
    public static final int DELAY_LEVEL_8M = 12;

    /**
     * 延时级别常量 - 13级 - 延迟9分钟
     */
    public static final int DELAY_LEVEL_9M = 13;

    /**
     * 延时级别常量 - 14级 - 延迟10分钟
     */
    public static final int DELAY_LEVEL_10M = 14;

    /**
     * 延时级别常量 - 15级 - 延迟20分钟
     */
    public static final int DELAY_LEVEL_20M = 15;

    /**
     * 延时级别常量 - 16级 - 延迟30分钟
     */
    public static final int DELAY_LEVEL_30M = 16;

    /**
     * 延时级别常量 - 17级 - 延迟1小时
     */
    public static final int DELAY_LEVEL_1H = 17;

    /**
     * 延时级别常量 - 18级 - 延迟2小时
     */
    public static final int DELAY_LEVEL_2H = 18;
    /**
     * MPC钱包通知事件
     */
    public static final String MPC_WALLET_WEBHOOK_EVENT_TOPIC = "MPC_WALLET_WEBHOOK_EVENT_TOPIC";
    /**
     * 开卡事件(开卡时候会将消息放入其中,消费者消费之后去Kcard获取开卡结果)
     */
    public static final String OPEN_CARD_EVENT_TOPIC = "OPEN_CARD_EVENT_TOPIC";
    /**
     * 钱包充值风控异常通知事件
     */
    public static final String WALLET_RECHARGE_RISK_EXCEPTION_EVENT_TOPIC = "WALLET_RECHARGE_RISK_EXCEPTION_EVENT_TOPIC";
    /**
     * 授权冲账消息
     */
    public static final String AUTH_ACCOUNTING_REVERSAL_TOPIC = "AUTH_ACCOUNTING_REVERSAL_TOPIC";
    /**
     * 卡充值记账冲正冲账通知事件
     */
    public static final String CARD_RECHARGE_BOOKKEEP_REVERSAL_EVENT_TOPIC = "CARD_RECHARGE_BOOKKEEP_REVERSAL_TOPIC";
    /**
     * 机构费用扣除通知事件
     */
    public static final String ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC = "ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC";
    /**
     * 授权交易手续费处理主题
     */
    public static final String AUTH_TRANSACTION_FEE_TOPIC = "AUTH_TRANSACTION_FEE_TOPIC";
    // 重试次数和延迟时间配置
    public static final int[] RETRY_DELAYS = {
            MqTopicConstant.DELAY_LEVEL_1S,    // 1秒
            MqTopicConstant.DELAY_LEVEL_10S,    // 10秒
            MqTopicConstant.DELAY_LEVEL_30S,    // 30秒
            MqTopicConstant.DELAY_LEVEL_1M,    // 1分钟
            MqTopicConstant.DELAY_LEVEL_5M, // 5分钟
            MqTopicConstant.DELAY_LEVEL_30M // 30分钟
    };
    /**
     * 销卡退还余额通知事件
     */
    public static final String CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC = "CANCEL_CARD_REFUND_BALANCE_EVENT_TOPIC";

    /**
     * 机构交易冲账主题
     * <p>
     * 用于处理机构交易的记商户账后交易失败冲账。
     * </p>
     */
    public static final String ORGANIZATION_TRANS_ACCOUNTING_REVERSAL_TOPIC = "ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC";

}
