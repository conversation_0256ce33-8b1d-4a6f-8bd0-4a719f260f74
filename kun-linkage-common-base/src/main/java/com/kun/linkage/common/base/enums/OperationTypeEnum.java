package com.kun.linkage.common.base.enums;

public enum OperationTypeEnum {
    ADD("ADD", "新增"),
    MODIFY("MODIFY", "修改");

    private final String value;
    private final String desc;
    private final String dictType = "KL_OPERATION_TYPE";

    OperationTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static OperationTypeEnum getEnumByValue(String value) {
        for (OperationTypeEnum operationTypeEnum : OperationTypeEnum.values()) {
            if (operationTypeEnum.getValue().equals(value)) {
                return operationTypeEnum;
            }
        }
        return null;
    }
}
