package com.kun.linkage.common.base.page;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class PageResult<T> implements Serializable {
    private static final long serialVersionUID = -1;
    private int pageNum;
    private int pageSize;
    private int pages;
    private int total;
    private List<T> data;
    private Map<String, Object> extraInfo;

    public PageResult() {
    }

    public PageResult(List<T> data, int pageNum, int pageSize, int total) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = (total + pageSize - 1)/pageSize;
        this.total = total;
        this.data = data;
    }

    public PageResult(List<T> data, int pageNum, int pageSize, int total, Map<String, Object> extraInfo) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = (total + pageSize - 1)/pageSize;
        this.total = total;
        this.data = data;
        this.extraInfo = extraInfo;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Map<String, Object> getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Map<String, Object> extraInfo) {
        this.extraInfo = extraInfo;
    }
}
