package com.kun.linkage.common.base.utils;

import cn.hutool.core.util.HexUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.kun.linkage.common.base.config.KeyManagementProperties;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 敏感信息加解密工具类
 */
public class SensitiveInfoUtil {
    private static Logger log = LoggerFactory.getLogger(SensitiveDataStringBuilder.class);

    // 内部调用敏感信息秘钥加密所使用的KEY(用来将秘钥再次加解密)
    private static final String INNER_KEY_ENCRYPT_KEY = "Mp+tEGj45EXOMX6M";
    // 外部敏感信息加解密使用的IV
    private static final String OUTER_IV = "1234567890000000";
    // 算法常量
    private static final String AES = "AES";
    private static final String ECB_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String CBC_ALGORITHM = "AES/CBC/PKCS5Padding";
    // 密钥长度（128/192/256）
    private static final int KEY_SIZE_128 = 128;
    private static final int KEY_SIZE_256 = 256;
    // 内部加解密的Key
    private static final Key innerSecretKey;

    static {
        KeyManagementProperties keyManagementProperties = SpringUtil.getBean(KeyManagementProperties.class);
        String decryptKey = innerDecryptKey(keyManagementProperties.getDbSensitiveInfoAesKey());
        innerSecretKey = new SecretKeySpec(decryptKey.getBytes(StandardCharsets.UTF_8), AES);
    }


    /**
     * 生成 AES 密钥（Base64 编码）
     */
    public static String generateBase64Key() {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance(AES);
            keyGen.init(KEY_SIZE_256, new SecureRandom());
            SecretKey secretKey = keyGen.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            log.error("秘钥生成失败", e);
            throw new BusinessException(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * ECB 模式加密（Base64 编码）
     */
    public static String encryptECB(String plainText, String key) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES);
            Cipher cipher = Cipher.getInstance(ECB_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("ECB加密失败", e);
            throw new BusinessException(CommonTipConstant.ENCRYPT_ERROR);
        }
    }

    /**
     * ECB 模式解密（Base64 编码）
     */
    public static String decryptECB(String cipherText, String key) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES);
            Cipher cipher = Cipher.getInstance(ECB_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(cipherText));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("ECB解密失败", e);
            throw new BusinessException(CommonTipConstant.DECRYPT_ERROR);
        }
    }

    /**
     * CBC 模式加密（Base64 编码）
     */
    public static String encryptCBC(String plainText, String keyBase64) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(keyBase64);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES);
            Cipher cipher = Cipher.getInstance(CBC_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(OUTER_IV.getBytes(StandardCharsets.UTF_8)));
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("CBC加密失败", e);
            throw new BusinessException(CommonTipConstant.ENCRYPT_ERROR);
        }
    }

    /**
     * CBC 模式解密（Base64 编码）
     */
    public static String decryptCBC(String cipherText, String keyBase64) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(keyBase64);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES);
            Cipher cipher = Cipher.getInstance(CBC_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(OUTER_IV.getBytes(StandardCharsets.UTF_8)));
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(cipherText));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("CBC解密失败", e);
            throw new BusinessException(CommonTipConstant.DECRYPT_ERROR);
        }
    }

    public static void main(String[] args) {
        String key = "yfJ4qOnlvszAqKaZLBZrYU4NxbRMk5E21mRNbcM0A/s=";
        System.out.println("K-7c7e2d264530499cae79bddc6e4d45ba" + " 卡号: " + decryptCBC("uJ7M7rCCX/yP1g5gZRAeDe3wJI2mwwsJg37am1ZBmuw=", key) +
                ", 有效期: " + decryptCBC("AkNEzziVqyaJYuGlDAhSNg==", key) + ", cvv: " + decryptCBC("JgxUBYZdpAlIYmnIJdkHXg==", key));
//        System.out.println("K-20ef6085d3954a9ba7b2224b327d31e8" + " 卡号: " + decryptCBC("ZyLeZM9+vUemMHR2RNL/7I+IIUQhxRn32W0DWIFthIU=", key) +
//                ", 有效期: " + decryptCBC("AkNEzziVqyaJYuGlDAhSNg==", key) + ", cvv: " + decryptCBC("OScDw3Hiq2LFmWQ09wG03g==", key));
//        System.out.println("K-6b86262ab0c342bfa4cfa99b7fdb4db1" + " 卡号: " + decryptCBC("wR+qNqwpKscAo3M/gZPDaPmjnxP5EXIMSp9r1f9EOL0=", key) +
//                ", 有效期: " + decryptCBC("AkNEzziVqyaJYuGlDAhSNg==", key) + ", cvv: " + decryptCBC("fcPY2XRc79aLKWPy/uB7ew==", key));

        //System.out.println(innerDecrypt("df6b258a33c9540ada46f7cdc5c8afc5826049166d7c913e87d44a008b8ff03c"));
        //System.out.println(mask("1234567890987654321", 6, 4, '*'));

    }
    /**
     * 内部系统之间加密(使用HEX编码)
     */
    public static String innerEncrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance(ECB_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, innerSecretKey);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return HexUtil.encodeHexStr(encryptedBytes);
        } catch (Exception e) {
            log.error("内部敏感信息加密失败", e);
            throw new BusinessException(CommonTipConstant.ENCRYPT_ERROR);
        }
    }

    /**
     * 内部系统之间解密(使用HEX编码)
     */
    public static String innerDecrypt(String cipherText) {
        try {
            Cipher cipher = Cipher.getInstance(ECB_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, innerSecretKey);
            byte[] decryptedBytes = cipher.doFinal(HexUtil.decodeHex(cipherText));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("内部敏感信息解密失败", e);
            throw new BusinessException(CommonTipConstant.DECRYPT_ERROR);
        }
    }

    /**
     * 敏感数据脱敏
     * @param str
     * @param prefixLength
     * @param suffixLength
     * @param maskChar
     * @return
     */
    public static String mask(String str, int prefixLength, int suffixLength, char maskChar) {
        if (str == null) {
            return null;
        }
        int length = str.length();
        // 处理前缀长度不合法的情况
        if (prefixLength < 0) {
            prefixLength = 0;
        }
        // 确保前缀长度不超过字符串长度
        if (prefixLength > length) {
            prefixLength = length;
        }
        // 处理后缀长度不合法的情况
        if (suffixLength < 0) {
            suffixLength = 0;
        }
        // 确保后缀长度不超过字符串长度
        if (suffixLength > length) {
            suffixLength = length;
        }
        // 计算需要掩码的长度
        int maskLength = length - prefixLength - suffixLength;
        // 如果掩码长度为负，调整前缀或后缀长度
        if (maskLength < 0) {
            if (prefixLength + suffixLength > length) {
                suffixLength = length - prefixLength;
                maskLength = 0;
            }
        }
        StringBuilder sb = new StringBuilder();
        // 添加前缀
        sb.append(str, 0, prefixLength);
        // 添加掩码字符
        for (int i = 0; i < maskLength; i++) {
            sb.append(maskChar);
        }
        // 添加后缀
        sb.append(str, length - suffixLength, length);
        return sb.toString();
    }

    /**
     * 内部系统使用解密秘钥
     *
     * @param cipherAesKey
     * @return 解密后的秘钥
     */
    private static String innerDecryptKey(String cipherAesKey) {
        try {
            Cipher cipher = Cipher.getInstance(ECB_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(INNER_KEY_ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), AES));
            byte[] decoded = Base64.getDecoder().decode(cipherAesKey);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("内部秘钥初始化失败", e);
            throw new BusinessException(CommonTipConstant.DECRYPT_ERROR);
        }
    }
}
