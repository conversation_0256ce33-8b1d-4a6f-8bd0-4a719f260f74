<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <groupId>com.kun.linkage</groupId>
    <artifactId>kun-linkage-root</artifactId>
    <version>1.1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>kun-linkage-root</name>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.9</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.6.1</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.16</spring-boot-admin.version>
        <pagehelper.version>2.0.0</pagehelper.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <shardingsphere.version>5.1.2</shardingsphere.version>
        <redisson.version>3.17.4</redisson.version>
        <druid.version>1.2.16</druid.version>
        <commons.lang.version>2.13.0</commons.lang.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <freemarker.version>2.3.31</freemarker.version>
        <springdoc.version>1.6.9</springdoc.version>
        <rocketmq-spring-boot-starter.version>2.1.1</rocketmq-spring-boot-starter.version>
        <spring-boot-starter-data-redis.version>2.7.18</spring-boot-starter-data-redis.version>
        <kun-linkage-boss-support.version>1.0.0-SNAPSHOT</kun-linkage-boss-support.version>
        <xxl-job-core.version>2.4.0</xxl-job-core.version>
        <tomcat-util.version>9.0.65</tomcat-util.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <kun-common.version>1.0.1-SNAPSHOT</kun-common.version>
        <kun-linkage-common.version>1.1.0-SNAPSHOT</kun-linkage-common.version>
        <kun-linkage-customer.version>1.1.0-SNAPSHOT</kun-linkage-customer.version>
        <kun-linkage-account.version>1.1.0-SNAPSHOT</kun-linkage-account.version>
        <kun-linkage-base.version>1.1.0-SNAPSHOT</kun-linkage-base.version>
        <kun-linkage-auth.version>1.1.0-SNAPSHOT</kun-linkage-auth.version>
        <kun-linkage-notice.version>1.1.0-SNAPSHOT</kun-linkage-notice.version>
        <kun-linkage-wallet-gateway.version>1.0.0-SNAPSHOT</kun-linkage-wallet-gateway.version>
        <kun-linkage-openapi-support.version>1.0.0-SNAPSHOT</kun-linkage-openapi-support.version>
        <kun-linkage-clearing.version>1.1.0-SNAPSHOT</kun-linkage-clearing.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Spring Cloud Alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- PageHelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!-- Fastjson -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- MySQL Connector -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <!-- ShardingSphere -->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
                <version>${shardingsphere.version}</version>
                <!-- 关键排除项 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Springdoc webmvc 依赖配置 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- Druid 连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- Spring Boot Starter Data Redis -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring-boot-starter-data-redis.version}</version>
            </dependency>

            <!-- Apache Lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- MyBatis-Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- MyBatis-Plus Generator -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- 模板引擎 -->
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kun</groupId>
                <artifactId>kun-common</artifactId>
                <version>${kun-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-boss-support</artifactId>
                <version>${kun-linkage-boss-support.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- 添加 Tomcat 工具类依赖 -->
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-util</artifactId>
                <version>${tomcat-util.version}</version>
            </dependency>

            <!-- base api -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-base-facade</artifactId>
                <version>${kun-linkage-base.version}</version>
            </dependency>

            <!-- customer api -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-customer-facade</artifactId>
                <version>${kun-linkage-customer.version}</version>
            </dependency>

            <!-- account api -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-account-facade</artifactId>
                <version>${kun-linkage-account.version}</version>
            </dependency>

            <!-- auth api -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-auth-facade</artifactId>
                <version>${kun-linkage-auth.version}</version>
            </dependency>
            <!-- notice facade api -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-notice-facade</artifactId>
                <version>${kun-linkage-notice.version}</version>
            </dependency>
            <!-- notice external facade api -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-notice-external-facade</artifactId>
                <version>${kun-linkage-notice.version}</version>
            </dependency>

            <!-- 项目common包 -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-common</artifactId>
                <version>${kun-linkage-common.version}</version>
            </dependency>
            <!-- 管理 common 子模块的版本 -->
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-common-db</artifactId>
                <version>${kun-linkage-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-common-base</artifactId>
                <version>${kun-linkage-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-common-redis</artifactId>
                <version>${kun-linkage-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-common-external-facade</artifactId>
                <version>${kun-linkage-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-wallet-gateway-facade</artifactId>
                <version>${kun-linkage-wallet-gateway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-openapi-support-facade</artifactId>
                <version>${kun-linkage-openapi-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kun.linkage</groupId>
                <artifactId>kun-linkage-clearing-facade</artifactId>
                <version>${kun-linkage-clearing.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>kun-nexus</id>
            <name>kun-nexus</name>
            <url>http://nexusqa.kun.global/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>kun-nexus-rls</id>
            <name>kun-nexus-rls</name>
            <url>http://nexusqa.kun.global/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <build>
        <finalName>kun-linkage-root</finalName>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${file.encoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <id>repackage</id>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>