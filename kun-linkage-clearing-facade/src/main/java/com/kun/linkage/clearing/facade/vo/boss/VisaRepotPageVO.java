package com.kun.linkage.clearing.facade.vo.boss;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;

public class VisaRepotPageVO extends PageParam implements Serializable {

    private final static long serialVersionUID = 1L;

    @Schema(description = "通道来源", example = "BPC-GW")
    private String channelSource;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "文件开始日期", example = "2025-07-01")
    private LocalDate fileStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "文件结束日期", example = "2025-09-01")
    private LocalDate fileEndTime;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "文件开始日期", example = "2025-07-01")
    private LocalDate fileTime;

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public LocalDate getFileStartTime() {
        return fileStartTime;
    }

    public void setFileStartTime(LocalDate fileStartTime) {
        this.fileStartTime = fileStartTime;
    }

    public LocalDate getFileEndTime() {
        return fileEndTime;
    }

    public void setFileEndTime(LocalDate fileEndTime) {
        this.fileEndTime = fileEndTime;
    }

    public LocalDate getFileTime() {
        return fileTime;
    }

    public void setFileTime(LocalDate fileTime) {
        this.fileTime = fileTime;
    }
}
