package com.kun.linkage.clearing.facade.vo.boss;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 导出文件记录分页查询VO
 */
public class ExportFileRecordPageQueryVO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "导出开始日期", example = "2025-07-01")
    private LocalDate exportStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "导出结束日期", example = "2025-07-31")
    private LocalDate exportEndDate;

    @Schema(description = "文件类型", example = "VISA_SETTLEMENT_DETAIL")
    private String fileType;

    @Schema(description = "文件状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败", example = "SUCCESS")
    private String fileStatus;

    public LocalDate getExportStartDate() {
        return exportStartDate;
    }

    public void setExportStartDate(LocalDate exportStartDate) {
        this.exportStartDate = exportStartDate;
    }

    public LocalDate getExportEndDate() {
        return exportEndDate;
    }

    public void setExportEndDate(LocalDate exportEndDate) {
        this.exportEndDate = exportEndDate;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }
}
