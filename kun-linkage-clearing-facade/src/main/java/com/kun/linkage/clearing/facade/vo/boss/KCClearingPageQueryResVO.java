package com.kun.linkage.clearing.facade.vo.boss;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;

public class KCClearingPageQueryResVO implements Serializable {

    /**
     * 清算主键id
     */
    @Schema(description = "清算主键id")
    private String clearingId;

    /**
     * 清算日期；yyyymmdd
     */
    @Schema(description = "清算日期")
    private LocalDate clearingDate;

    /**
     * 来源系统:VCC;KL
     */
    @Schema(description = "来源系统:VCC;KL")
    private String system;

    /**
     * 通道来源;PBC-GW；
     */
    @Schema(description = "通道来源;PBC-GW；")
    private String channelSource;

    /**
     * 授权日期
     */
    @Schema(description = "授权日期")
    private String authDate;

    /**
     * 商户;对应机构号
     */
    @Schema(description = "商户id")
    private String customerMerId;

    /**
     * 商户名称;机构名称
     */
    @Schema(description = "商户名称")
    private String cardAcceptorName;

    /**
     * 收单商户名称;
     */
    @Schema(description = "收单商户名称")
    private String acqMerchantName;
    

    /**
     * kcard系统卡id
     */
    @Schema(description = "kcard系统卡id")
    private String kcardId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private String transactionType;

    /**
     * 交易币种;三位字母
     */
    @Schema(description = "交易币种;三位字母")
    private String transactionCurrencyCode;

    /**
     * 清分金额
     */
    @Schema(description = "清分金额")
    private String clearAmount;

    /**
     * 持卡人币种;三位字母
     */
    @Schema(description = "持卡人币种;三位字母")
    private String cardholderCurrencyCode;

    @Schema(description = "持卡人金额")
    private String cardholderAmount;


    @Schema(description = "清算状态;成功;失败")
    private String clearingStatus;

    @Schema(description = "参考号;F37")
    private String referenceNo;
    

    @Schema(description = " F38:清分文件授权码；")
    private String authCode;

    @Schema(description = "收单参考号")
    private String acqArn;

    @Schema(description = "清算流水号")
    private String clearingNo;

    @Schema(description = "原清算流水号")
    private String originalClearingNo;

    @Schema(description = "授权流水表中的交易id")
    private String transId;

    @Schema(description = "脱敏卡号")
    private String maskedCardNumber;

    @Schema(description = "结算标记")
    private String settlementFlag;

    @Schema(description = "intechangeFeeSign")
    private String intechangeFeeSign;

    @Schema(description = "intechangeFeeAmt")
    private String intechangeFeeAmt;

    @Schema(description = "cpd")
    private String visaCPDDate;

    public String getClearingId() {
        return clearingId;
    }

    public void setClearingId(String clearingId) {
        this.clearingId = clearingId;
    }

    public LocalDate getClearingDate() {
        return clearingDate;
    }

    public void setClearingDate(LocalDate clearingDate) {
        this.clearingDate = clearingDate;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public String getAuthDate() {
        return authDate;
    }

    public void setAuthDate(String authDate) {
        this.authDate = authDate;
    }

    public String getCustomerMerId() {
        return customerMerId;
    }

    public void setCustomerMerId(String customerMerId) {
        this.customerMerId = customerMerId;
    }

    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }

    public String getAcqMerchantName() {
        return acqMerchantName;
    }

    public void setAcqMerchantName(String acqMerchantName) {
        this.acqMerchantName = acqMerchantName;
    }

    public String getKcardId() {
        return kcardId;
    }

    public void setKcardId(String kcardId) {
        this.kcardId = kcardId;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public String getClearAmount() {
        return clearAmount;
    }

    public void setClearAmount(String clearAmount) {
        this.clearAmount = clearAmount;
    }

    public String getCardholderCurrencyCode() {
        return cardholderCurrencyCode;
    }

    public void setCardholderCurrencyCode(String cardholderCurrencyCode) {
        this.cardholderCurrencyCode = cardholderCurrencyCode;
    }

    public String getCardholderAmount() {
        return cardholderAmount;
    }

    public void setCardholderAmount(String cardholderAmount) {
        this.cardholderAmount = cardholderAmount;
    }

    public String getClearingStatus() {
        return clearingStatus;
    }

    public void setClearingStatus(String clearingStatus) {
        this.clearingStatus = clearingStatus;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getAcqArn() {
        return acqArn;
    }

    public void setAcqArn(String acqArn) {
        this.acqArn = acqArn;
    }

    public String getClearingNo() {
        return clearingNo;
    }

    public void setClearingNo(String clearingNo) {
        this.clearingNo = clearingNo;
    }

    public String getOriginalClearingNo() {
        return originalClearingNo;
    }

    public void setOriginalClearingNo(String originalClearingNo) {
        this.originalClearingNo = originalClearingNo;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getMaskedCardNumber() {
        return maskedCardNumber;
    }

    public void setMaskedCardNumber(String maskedCardNumber) {
        this.maskedCardNumber = maskedCardNumber;
    }

    public String getSettlementFlag() {
        return settlementFlag;
    }

    public void setSettlementFlag(String settlementFlag) {
        this.settlementFlag = settlementFlag;
    }

    public String getIntechangeFeeSign() {
        return intechangeFeeSign;
    }

    public void setIntechangeFeeSign(String intechangeFeeSign) {
        this.intechangeFeeSign = intechangeFeeSign;
    }

    public String getIntechangeFeeAmt() {
        return intechangeFeeAmt;
    }

    public void setIntechangeFeeAmt(String intechangeFeeAmt) {
        this.intechangeFeeAmt = intechangeFeeAmt;
    }

    public String getVisaCPDDate() {
        return visaCPDDate;
    }

    public void setVisaCPDDate(String visaCPDDate) {
        this.visaCPDDate = visaCPDDate;
    }
}
