package com.vcc.boss.core.vo.userCard;

import com.alibaba.fastjson.JSONObject;
import com.vcc.boss.core.dto.userCard.ShareCardLimitConfigDto;
import com.vcc.boss.core.vo.baseVo.BaseVo;
import com.vcc.core.enums.ChannelEnum;
import com.vcc.core.util.DateUtil;
import com.vcc.core.util.RegexUtil;
import com.vcc.core.util.StringUtil;
import com.vcc.data.entity.Cardholder;
import com.vcc.data.entity.UserCard;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Getter
@Setter
public class UserCardVo extends BaseVo implements Serializable {
    /**
     * 账号
     */
    private String cardNumber;

    private String cardNumberShow;

    private String cardNumberLastFour;

    private String requestDate;

    private String orderId;

    private Long cardId;

    private Long userId;

    private String account;

    private Integer channel;

    private String currency;

    private String cardType;

    private BigDecimal usableQuota;

    private String remark;

    private String cardStatus;

    private String firstName;

    private String lastName;

    private String vcc;

    private String expiry;

    private BigDecimal applyFee;

    private Boolean isShare;

    private String channelCardId;

    private String cardOptDateTime;

    private ShareCardLimitConfigDto limitConfig;

    public UserCardVo() {
    }

    public UserCardVo(UserCard userCard, String account, Cardholder cardholder, boolean showCardNumber, String cardOptDateTime) {
        super.setId(userCard.getId());
        this.cardNumber = showCardNumber ? userCard.getCardNumber() : StringUtil.hideCardNumber(userCard.getCardNumber());
        this.cardNumberShow = userCard.getCardNumber();
        this.requestDate = DateUtil.format(userCard.getRequestDate());
        this.cardId = userCard.getCardBinId();
        this.userId = userCard.getUserId();
        this.account = (account);
        this.channel = userCard.getChannel();
        this.currency = userCard.getCurrency();
        this.cardType = userCard.getCardType();
        this.usableQuota = userCard.getUsableQuota();
        this.remark = userCard.getRemark();
        this.cardStatus = userCard.getCardStatus();
        if (RegexUtil.checkObjectIsNotNull(cardOptDateTime)) {
            this.cardOptDateTime = cardOptDateTime;
        }
        this.firstName = RegexUtil.checkObjectIsNull(cardholder) ? null : cardholder.getFirstName();
        this.lastName = RegexUtil.checkObjectIsNull(cardholder) ? null : cardholder.getLastName();
//        this.vcc = userCard.getCvv();
//        this.expiry = userCard.getExpiry();
        this.applyFee = userCard.getApplyFee();
        this.isShare = userCard.getIsShare();
        this.channelCardId = userCard.getChannelCardId();
        if (Boolean.TRUE.equals(userCard.getIsShare())) {
            this.limitConfig = JSONObject.parseObject(userCard.getLimitConfig(), ShareCardLimitConfigDto.class);
        }
    }
}
