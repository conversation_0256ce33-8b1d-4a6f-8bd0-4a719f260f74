package com.vcc.web.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vcc.core.enums.GroupSystemEnum;
import com.vcc.core.enums.WalletStatusEnum;
import com.vcc.core.enums.WalletTypeEnum;
import com.vcc.core.exception.NotFoundException;
import com.vcc.core.exception.ProgramException;
import com.vcc.data.entity.Wallet;
import com.vcc.data.mapper.WalletMapper;
import com.vcc.web.core.component.SseComponent;
import com.vcc.web.service.service.WalletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Service
public class WalletServiceImpl extends ServiceImpl<WalletMapper, Wallet> implements WalletService {
    @Autowired
    private SseComponent sseComponent;

    @Override
    public List<Wallet> listByUserId(Long userId) {
        return lambdaQuery().eq(Wallet::getUserId, userId).list();
    }

    @Override
    public List<Wallet> listByUserIds(List<Long> userIds) {
        return CollectionUtils.isEmpty(userIds) ? new ArrayList<>() : lambdaQuery().in(Wallet::getUserId, userIds).list();
    }

    @Override
    public List<Wallet> listByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? new ArrayList<>() : lambdaQuery().in(Wallet::getId, ids).list();
    }

    @Override
    public IPage<Wallet> paging(Page<Wallet> pageable) {
        return lambdaQuery().page(pageable);
    }

    @Override
    public Wallet init(Long userId, Long customerId, String walletId,
                       String currency, WalletTypeEnum type,
                       GroupSystemEnum source, BigDecimal usableQuota) {
        Wallet wallet = new Wallet();
        wallet.setUserId(userId);
        wallet.setCustomerId(customerId);
        wallet.setWalletId(walletId);
        wallet.setStatus(WalletStatusEnum.ACTIVE.getCode());
        wallet.setType(type.getCode());
        wallet.setSource(source.getCode());
        wallet.setCurrency(currency);
        wallet.setUsableQuota(usableQuota);
        wallet.setOverflowQuota(BigDecimal.ZERO);
        wallet.setVersion(1L);
        wallet.setIsShare(false);
        wallet.setStatus(WalletStatusEnum.ACTIVE.getCode());
        save(wallet);
        return wallet;
    }

    @Override
    public boolean syncBalance(Long id, BigDecimal balance) {
        return lambdaUpdate().eq(Wallet::getId, id).set(Wallet::getUsableQuota, balance).update();
    }

    @Override
    public Wallet getById(Long walletId) {
        return lambdaQuery().eq(Wallet::getId, walletId).oneOpt().orElseThrow(() -> new NotFoundException("Wallet not found."));
    }

    @Override
    public Wallet getByUserIdAndCurrency(Long userId, String currency) {
        return lambdaQuery()
                .eq(Wallet::getUserId, userId)
                .eq(Wallet::getCurrency, currency)
                .eq(Wallet::getIsShare, false)
                .oneOpt()
                .orElseThrow(() -> new NotFoundException("Wallet not found."));
    }

    @Override
    public Wallet getByUserIdAndCurrencyWithNull(Long userId, String currency) {
        return lambdaQuery()
                .eq(Wallet::getUserId, userId)
                .eq(Wallet::getCurrency, currency)
                .eq(Wallet::getIsShare, false)
                .oneOpt()
                .orElse(null);
    }

    @Override
    public Wallet getByUserIdAndCurrency(Long userId, String currency, Boolean isShare) {
        return lambdaQuery()
                .eq(Wallet::getUserId, userId)
                .eq(Wallet::getCurrency, currency)
                .eq(Wallet::getIsShare, isShare)
                .oneOpt()
                .orElseThrow(() -> new NotFoundException("Wallet not found."));
    }

    @Override
    public BigDecimal addUsableQuotaById(Long id, BigDecimal amount) {
        return updateAmt(id, amount, true, true);
    }

    @Override
    public BigDecimal subtractUsableQuotaById(Long id, BigDecimal amount, Boolean ignoreBalance) {
        return updateAmt(id, amount, false, ignoreBalance);
    }

    public BigDecimal updateAmt(
            Long id,
            BigDecimal amount,
            Boolean isAdd,
            Boolean ignoreBalance
    ) {
        try {
            Wallet wallet = lambdaQuery()
                    .eq(Wallet::getId, id).oneOpt()
                    .orElseThrow(() -> new NotFoundException("Wallet not found."));
            if (isAdd) {
                wallet.setUsableQuota(wallet.getUsableQuota().add(amount).setScale(2, RoundingMode.HALF_UP));
            } else {
                if (!ignoreBalance && wallet.getUsableQuota().compareTo(amount) < 0) {
                    throw new ProgramException("The available credit limit on the wallet is insufficient.");
                }
                wallet.setUsableQuota(wallet.getUsableQuota().subtract(amount).setScale(2, RoundingMode.HALF_UP));
            }
            boolean result = lambdaUpdate()
                    .set(Wallet::getUsableQuota, wallet.getUsableQuota())
                    .set(Wallet::getVersion, wallet.getVersion() + 1)
                    .eq(Wallet::getId, wallet.getId())
                    .eq(Wallet::getVersion, wallet.getVersion()).update();
            if (!result) {
                return updateAmt(id, amount, isAdd, ignoreBalance);
            } else {
                // 推送给前端刷新钱包余额
                sseComponent.sendRefreshUsableQuotaMessage(wallet.getUserId(), listByUserId(wallet.getUserId()));
                return wallet.getUsableQuota();
            }
        } catch (ProgramException e) {
            log.error("操作失败", e);
            throw e;
        } catch (Exception e) {
            log.error("操作失败", e);
            throw new ProgramException("System error, please try again later.");
        }
    }
}
