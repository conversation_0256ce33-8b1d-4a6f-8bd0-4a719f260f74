package com.vcc.web.service.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vcc.data.entity.Comptroller;


public interface ComptrollerService extends IService<Comptroller> {

    /**
     *  0 注册 1 登录  2  禁用  3 启用   4 注销  5  修改密码  6 修改支付密码
     */

    Comptroller register(String ip,Long userId,Integer status);

    Comptroller login(String ip,Long userId,Integer status);

    Comptroller block(String ip,Long userId,Integer status);

    Comptroller unBlock(String ip,Long userId,Integer status);

    Comptroller close(String ip,Long userId,Integer status);

    Comptroller updatePassword(String ip,Long userId,Integer status);

    Comptroller updatePayPassword(String ip,Long userId,Integer status);

    IPage<Comptroller> paging(Page<Comptroller> pageable,Integer useFlag,Long userId, String startDate, String endDate);
}
