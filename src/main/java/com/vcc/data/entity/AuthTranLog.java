package com.vcc.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlCharsetConstant;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlEngineConstant;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.vcc.data.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Setter
@Getter
@TableName(value = "vcc_auth_tran_log", autoResultMap = true)
@TableCharset(MySqlCharsetConstant.UTF8)
@TableEngine(MySqlEngineConstant.InnoDB)
public class AuthTranLog extends BaseEntity implements Serializable {

    @IsNotNull
    @TableField
    @ColumnType(length = 100)
    @ColumnComment("请求号")
    private String requestNo;

    @TableField
    @ColumnType(length = 100)
    @ColumnComment("交易id")
    private String tranId;

    @TableField
    @ColumnType(length = 100)
    @ColumnComment("渠道卡Id")
    private String channelCardId;

    @TableField
    @ColumnType(length = 100)
    @ColumnComment("交易类型")
    private String channelAuthType;

    @TableField
    @Column(type = MySqlTypeConstant.TEXT)
    @ColumnComment("请求参数")
    private String requestParam;

    @TableField
    @ColumnType(length = 100)
    @ColumnComment("响应码")
    private String responseCode;

    @TableField
    @Column(type = MySqlTypeConstant.TEXT)
    @ColumnComment("响应参数")
    private String responseParam;
}
