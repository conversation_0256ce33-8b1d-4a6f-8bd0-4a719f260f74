package com.vcc.core.api.kcard.kun;

import com.vcc.core.api.kcard.common.KCardCommon;

public class CommonResponse<T> {
	private String code;
	private String message;
	private T data;
	// 构造方法、getter 和 setter
	public CommonResponse() {
	}

	public CommonResponse(String code, String message, T data) {
		this.code = code;
		this.message = message;
		this.data = data;
	}

	// getter 和 setter 方法
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	// 通常还会有一个判断是否成功的方法
	public boolean isSuccess() {
		return KCardCommon.API_SUCCESS_CODE.equals(code); // 根据你的实际业务码调整
	}
}
